namespace :fargate do
  task auto_adjustfargate_size: :environment do
    UserProfile.where(fargate_task_definition: "Job-CPU1-4GB").where("total_sku < 15000").update_all(fargate_task_definition: nil)
    UserProfile.where(fargate_task_definition: "Job-CPU1-2GB").where("total_sku < 15000").update_all(fargate_task_definition: nil)
    UserProfile.where(fargate_task_definition: "Job-CPU1-4GB").where("total_sku < 25000").update_all(fargate_task_definition: "Job-CPU1-2GB")
    UserProfile.where(fargate_task_definition: "Job-CPU1-8GB").where("total_sku < 25000").update_all(fargate_task_definition: "Job-CPU1-2GB")

    UserProfile.where.not(total_allocated_memsize: nil).where("total_allocated_memsize < 1000").update_all(fargate_task_definition: nil)
    UserProfile.where.not(total_allocated_memsize: nil).where("total_allocated_memsize > 1000 and total_allocated_memsize < 2000").where(fargate_task_definition: "Job-CPU1-4GB").update_all(fargate_task_definition: "Job-CPU1-2GB")
    UserProfile.where.not(total_allocated_memsize: nil).where("total_allocated_memsize > 1000 and total_allocated_memsize < 2000").where(fargate_task_definition: "Job-CPU1-8GB").update_all(fargate_task_definition: "Job-CPU1-2GB")
    UserProfile.where.not(total_allocated_memsize: nil).where("total_allocated_memsize > 2000 and total_allocated_memsize < 4000").where(fargate_task_definition: "Job-CPU1-8GB").update_all(fargate_task_definition: "Job-CPU1-4GB")
  end

  task check_instances: :environment do
    running_tasks = UserProfile.where(status: "processing").where.not(fargate_task_id: "run local").pluck(:fargate_task_id)
    next if running_tasks.empty?

    non_bulk = []

    running_tasks.each do |fargate_task_id|
      unless (fargate_task_id =~ /^bulk::.*/).present?
        non_bulk << fargate_task_id
      end
    end

    client = AwsServices.ecs_client

    malfunction_fargate_task_arns = []
    restarted_tasks = []

    ## Check for failed tasks
    result = client.describe_tasks(tasks: non_bulk, cluster: "stocksync-job-production")
    result.tasks.each do |task|
      container = task.containers.first

      # For tasks out of memory
      if container.last_status == "STOPPED" && !(container.reason =~ /OutOfMemoryError/).blank?
        malfunction_fargate_task_arns << {task: task.task_arn, upgrade_memory: true}
      end

      # For tasks stopped because of server error e.g. bad redis connection
      if container.last_status == "STOPPED" && container.exit_code == 1
        malfunction_fargate_task_arns << {task: task.task_arn, upgrade_memory: false}
      end
    end

    result.failures.each do |failure|
      if failure.reason == "MISSING"
        malfunction_fargate_task_arns << {task: failure.arn, upgrade_memory: true}
      end
    end

    ## Upgrade memory and restart task
    malfunction_fargate_task_arns.each do |task|
      task_ids = task.fetch(:task)
      upgrade_memory = task.fetch(:upgrade_memory)

      fargate_task_id = task_ids.split("/").last
      profile = UserProfile.find_by(fargate_task_id: fargate_task_id)
      next if !profile

      # Upgrade computing power of fargate task
      current_definition = profile.fargate_task_definition
      updated_fargate_task_definition = Jobs::FargateTaskDefinition.get_next_definition(current_definition)
      upgrade = true

      if upgrade_memory
        computing_power = Jobs::FargateTaskDefinition.computing_power.call(updated_fargate_task_definition)[0]
        if computing_power <= 16
          profile.update(checksum: nil, checksum_file_size: nil, fargate_task_definition: updated_fargate_task_definition)
        else
          upgrade = false
        end
      end

      # Run on fargate
      feed_process_job = ProcessFeedJob.new(profile.id, trigger_by: "support", run_on_local: false)
      _job = Delayed::Job.enqueue(feed_process_job, queue: profile.job_queue, priority: 0)
      profile.queuing

      restarted_tasks << OpenStruct.new({
        profile_id: profile.id,
        current_definition: current_definition,
        updated_definition: updated_fargate_task_definition,
        upgrade_memory: upgrade_memory,
        upgraded: upgrade,
        fargate_task_id: profile.fargate_task_id
      })
    end

    p "Fargate Task Instances Check"
    p "Normal Tasks count: #{result.tasks.size}"
    p "Failures count: #{result.failures.size}"
    p "Restarted Tasks: #{restarted_tasks.size}"
    p "Restarted tasks"
    p restarted_tasks

    if restarted_tasks.size > 0
      BackendMailer.stopped_fargate_tasks(result.failures, restarted_tasks).deliver
    end
  end

  task log_pending_task: :environment do
    fargate_user_profiles = CancelFeed::Runner.get_running_fargates
    processing_user_profiles = UserProfile.where(status: "processing").where.not(fargate_task_id: ["run local", nil]).pluck(:id)
    fargate_not_processing = fargate_user_profiles - processing_user_profiles
    processing_not_fargate = processing_user_profiles - fargate_user_profiles
    pending_fargate_delayed_jobs = Delayed::Job.where(queue: "main_jobs").where(locked_at: nil).where("run_at < ?", Time.now).count
    pending_local_delayed_jobs = Delayed::Job.where(queue: "local_main_jobs").where(locked_at: nil).where("run_at < ?", Time.now).count

    fargate = fargate_not_processing.count
    local_task = processing_not_fargate.count
    task_log = TaskLog.new(fargate: fargate, local_task: local_task, pending_fargate_delayed_jobs: pending_fargate_delayed_jobs, pending_local_delayed_jobs: pending_local_delayed_jobs)
    task_log.save
    Rails.logger.info "CURRENT PROCESSING PROFILES!!!! #{processing_user_profiles}"
    Rails.logger.info "LOCKED JOBS PROFILES!!!! #{Delayed::Job.where("locked_by IS NOT NULL AND user_profile_id IS NOT NULL").pluck(:user_profile_id)}"
    puts DateTime.now
  end

  task check_running_bulk_and_fargate: :environment do
    @fargate_user_profiles = begin
      CancelFeed::Runner.get_unhealthy_and_stucked_fargate
    rescue => e
      Rails.logger.error("check_running_bulk_and_fargate get_unhealthy_and_stucked_fargate error")
      Rails.logger.error(e)
      {}
    end

    @bulk_profiles_stuck = CancelFeed::Runner.get_bulk_profiles_stuck

    BackendMailer.fargate_running_more_than_twelve_hours(@fargate_user_profiles, @bulk_profiles_stuck).deliver if @fargate_user_profiles.present? || @bulk_profiles_stuck.present?

    if @fargate_user_profiles.present?

      UserProfile.with_deleted.where(id: @fargate_user_profiles.pluck(:profile)).in_batches.each do |profiles|
        profiles.each do |profile|
          profile.cancel_process("Stop stucked fargate")
        end
      end
    end

    if @bulk_profiles_stuck.present?
      @bulk_profiles_stuck.each do |profile|
        Rails.logger.info "bulk_stuck #{profile[:profile]}"
        UserProfile.with_deleted.find(profile[:profile]).cancel_process("bulk_stuck")
      rescue => e
        Rails.logger.error("check_running_bulk_and_fargate bulk_profiles_stuck cancel error")
        Rails.logger.error(e)
      end
    end
    CancelFeed::Runner.storelapse_task_cleanup
  end
end
