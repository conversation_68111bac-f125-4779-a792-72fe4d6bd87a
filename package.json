{"name": "stock-sync", "private": true, "type": "module", "engines": {"node": "^20.19.0", "npm": "not-supported", "pnpm": "^9.15.6", "yarn": "not-supported"}, "packageManager": "pnpm@9.15.6", "scripts": {"build": "stale-dep && pnpm build:clean && NODE_OPTIONS=--max-old-space-size=3072 bundle exec vite build", "build:clean": "rimraf public/vite*", "build:production": "pnpm build -- --mode production", "build:visualizer": "pnpm build -- --mode visualizer", "db:migrate:dev": "RAILS_ENV=development bundle exec rails db:migrate", "dev:rails": "RAILS_ENV=development bundle exec rails s", "dev:vite": "stale-dep && RAILS_ENV=development bundle exec vite dev", "dev": "concurrently -k -c auto -n dev: npm:dev:*", "lint": "eslint app/javascript --cache", "postinstall": "stale-dep -u", "prepare": "lefthook install", "typecheck": "stale-dep && tsc -p tsconfig.json --noEmit"}, "dependencies": {"@airbrake/browser": "^2.1.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "~6.2.1", "@fortawesome/pro-light-svg-icons": "~6.2.1", "@fortawesome/pro-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^2.9.11", "@mui/lab": "6.0.0-beta.30", "@mui/material": "^6.4.7", "@mui/x-date-pickers": "^7.27.3", "@shopify/app-bridge-react": "^4.1.5", "@urql/exchange-graphcache": "^6.3.3", "@urql/exchange-refocus": "^1.1.1", "@urql/exchange-retry": "^1.2.0", "@yaireo/tagify": "^4.18.3", "ahooks": "^3.8.4", "crisp-sdk-web": "^1.0.25", "dayjs": "^1.11.13", "graphql": "14.7.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "intro.js": "^7.2.0", "jotai": "^2.2.1", "jotai-immer": "^0.4.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "notistack": "^3.0.2", "posthog-js": "^1.255.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.21.3", "react-virtuoso": "^4.12.3", "sockette": "^2.0.6", "ts-pattern": "^5.0.5", "urql": "^4.0.5", "yup": "^1.4.0", "zod": "^3.24.3"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@rollup/plugin-typescript": "^11.1.6", "@types/js-cookie": "3.0.5", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/yaireo__tagify": "^4.27.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@urql/core": "^4.1.3", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.17", "concurrently": "^9.1.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "^1.24.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^5.2.0", "lefthook": "^1.11.13", "postcss-nesting": "^12.0.2", "prettier": "3.2.5", "rimraf": "5", "rollup-plugin-visualizer": "^5.12.0", "stale-dep": "^0.8.2", "typescript": "^5.8.2", "vite": "^6.3.5", "vite-plugin-rails": "^0.5.0"}, "pnpm": {"overrides": {"@babel/runtime@<7.26.10": ">=7.26.10", "cross-spawn": "^7.0.5", "esbuild@<=0.24.2": ">=0.25.0", "micromatch": "^4.0.8", "nanoid@<3.3.8": ">=3.3.8", "brace-expansion@>=1.0.0 <=1.1.11": "^1.1.12", "brace-expansion@>=2.0.0 <=2.0.1": "^2.0.2"}}}