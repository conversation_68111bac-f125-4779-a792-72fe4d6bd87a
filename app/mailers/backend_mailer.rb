class BackendMailer < ApplicationMailer
  PRODUCT = ["<EMAIL>"]
  BACKEND = ["<EMAIL>"]
  WHOLE_TEAM = ["<EMAIL>"]
  layout "mailer"

  def start_instance_status_email(message)
    @message = message
    emails = ["<EMAIL>"]
    mail(to: emails, subject: "‼‼‼‼‼ Auto start instance status ‼‼‼‼‼")
  end

  def restart_job_instance_status_email(message)
    @message = message
    emails = ["<EMAIL>"]
    mail(to: emails, subject: "✅✅✅✅✅ Restart job instance status ✅✅✅✅✅")
  end

  def daily_report
    # @valid_user = User.valid.order("created_at desc")
    # @invalid_user = User.invalid.order("created_at desc")
    # @user = User.all
    plans = User.select("package, count(*) as total_stores").where.not(charge_id: nil).group(:package).order("package").collect { |a| a }
    add_on_summary = User.select("import_package as package, count(*) as total_stores").where.not(charge_id: nil).where.not(import_package: nil).group(:import_package)
    @plan_summary = plans
    add_on_summary.each do |row|
      @plan_summary << UserReport.new(row.package, row.total_stores)
    end
    @yesterday_summary = {}
    @yesterday_report = DailyReport.order("created_at desc").first
    if @yesterday_report.present?
      @yesterday_report.summary.each do |report|
        @yesterday_summary[report["package"]] = report["total_stores"]
      end
    end
    DailyReport.create(period: Time.now, summary: @plan_summary)

    @source_types = UserProfile.select("source_type, count(*) as total_count").where("id in (select user_profile_id from product_logs where created_at >= ? and created_at <= ?)", Time.zone.now.beginning_of_day, Time.zone.now.end_of_day).group("source_type").order("source_type")
    mail(to: PRODUCT, subject: "syncX: Stock Sync daily report")
  end

  def queuing_profile_email(fargate_count, local_count)
    @fargate_count = fargate_count
    @local_count = local_count
    mail(to: WHOLE_TEAM, subject: "Attention! #{@fargate_count} queuing Fargate profiles")
  end

  def missing_fargate(fargate_not_processing, processing_not_fargate)
    @fargate_not_processing = fargate_not_processing
    @processing_not_fargate = processing_not_fargate
    mail(to: BACKEND, subject: "Processing feeds that fargate not found")
  end

  def stopped_fargate_tasks(failures, tasks = [])
    @tasks = tasks
    @failures = failures
    mail(to: BACKEND, subject: "Restarted #{@tasks.size} Fargate Tasks")
  end

  def restart_ec2(url, subject)
    @url = url
    mail(to: WHOLE_TEAM, subject: subject)
  end

  def notify_stripe_billing(stripe_event)
    @stripe_event = stripe_event
    mail(to: WHOLE_TEAM, subject: "Attn: Unclaimed Stripe Payment")
  end

  def notify_credits_issue(store, charge)
    @store = store
    @charge = charge
    mail(to: WHOLE_TEAM, subject: "Attn: Credits Purchase Error")
  end

  def fargate_running_more_than_twelve_hours(task_ids, bulk_profiles_stuck)
    @task_ids = task_ids
    @bulk_profiles_stuck = bulk_profiles_stuck
    emails = ["<EMAIL>"]
    mail(to: emails, subject: "Stuck Fargate / Bulk Feeds Since 12 Hours")
  end
end
UserReport = Struct.new(:package, :total_stores)
