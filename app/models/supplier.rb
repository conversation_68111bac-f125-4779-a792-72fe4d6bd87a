class Supplier < ApplicationRecord
  has_many :feed_templates, dependent: :destroy

  scope :publish_templates, -> { where(publish_templates: true) }

  scope :categories_includes, ->(search) {
                                where("CONCAT(categories) ILIKE ?", "%#{search}%")
                              }

  def self.ransackable_scopes(auth_object = nil)
    [:categories_includes]
  end

  def self.sort_by_popular
    # return template_ids in a string (from most-used to least-used)
    template_ids = UserProfile.where.not(template_id: nil).group(:template_id).count.sort_by { |_key, value| value }.reverse.map { |array| array[0] }.join(",")

    # return the suppliers in the same order (from most-used to least-used)
    supplier_ids = FeedTemplate.order("ARRAY_POSITION(ARRAY[#{template_ids}], user_profiles.id)").pluck(:supplier_id).uniq.reject(&:blank?)

    # Retrieve supplier's and arrange base on the supplier_ids
    suppliers = Supplier.publish_templates.where(id: supplier_ids)
    supplier_ids.map { |sid| suppliers.detect { |supplier| supplier.id == sid } }.compact
  end
end
