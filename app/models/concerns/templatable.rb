module Templatable
  extend ActiveSupport::Concern

  def duplicate(opts = {})
    klass = opts.delete(:target_class) || UserProfile
    save_user = opts.delete(:save_user) || klass == UserProfile
    template_id = opts.delete(:template_id)
    user = opts.delete(:user) || self.user
    convert_update_feed = opts.delete(:is_update_feed) || false
    convert_remove_feed = opts.delete(:is_remove_feed) || false
    profile_name = opts.delete(:profile_name) || self.profile_name

    save_profile = opts[:save_profile].nil? ? true : opts.delete(:save_profile)

    template = klass.new(attributes.except(*profile_exclude_attributes)) do |t|
      t.scheduler_enabled = false
      t.profile_name = profile_name
      t.job_type = "daily"
      t.status = "pause"
      t.orig_status = "pause"
      t.template_id = template_id
      if save_user
        t.user = user
        t.location_id = user.primary_location_id
        t.location_name = ""
      end

      if convert_update_feed
        t.feed_type = "update"
      elsif convert_remove_feed
        t.feed_type = "remove"
        t.source_type = "feed"
        t.source_url = id
        t.filter_params = "{}"
        t.feed_filters = []
      end

      t.latest_product_log_id = nil
      t.latest_product_log_id_24h_ago = nil
      t.active_product_log_id = nil
      t.fargate_task_id = nil
      t.fargate_task_definition = nil
      t.received_email = nil
      t.match_subject = nil

      # Enable template in orignal owner's store provider
      t.supported_providers = default_supported_providers
      t.supported_providers["on_#{user.provider.downcase}"] = true
      t.update_duplicate_product_key = true
    end

    template.save if save_profile

    provider_sync_fields = "SyncFields::#{template.provider.titleize}".constantize.all_attributes if template.is_a?(UserProfile) && template.provider.present?
    sync_fields.order("created_at asc").each do |field|
      # next if convert_update_feed && convert_import_to_update_exclude_fields.exclude?(field.field_name)
      next if convert_remove_feed && remove_feed_exclude_fields.exclude?(field.field_name)

      if provider_sync_fields
        extracted_field_name = field.field_name.sub(/_\d+\z(?!.*_\d+)/, "").to_sym
        # ? variant_group is not included in the sync_fields
        next unless provider_sync_fields[extracted_field_name] || field.field_name == "variant_group"
      end

      args = field.attributes.except(*sync_field_exclude_attributes)
      if convert_remove_feed
        args["field_mapping"] = "1"
        args["is_locked"] = true
      end

      if save_profile
        template.sync_fields.create(args)
      else
        template.sync_fields.new(args)
      end
    end

    template
  end

  def duplicate_user_profile(opts = {})
    duplicate(opts.merge!(target_class: UserProfile, save_user: true))
  end

  def make_as_template(opts = {})
    duplicate(opts.merge!(target_class: FeedTemplate, save_user: false))
  end

  def create_profile_from_template(opts = {})
    duplicate(opts.merge!(target_class: UserProfile, save_user: true, template_id: id))
  end

  # Used in V2 to return template when user changes connections
  def return_profile_from_template(opts = {})
    duplicate(opts.merge!(target_class: UserProfile, save_user: true, template_id: id, save_profile: false))
  end

  def create_remove_feed_from_profile(opts = {})
    template = Supplier.find_by(name: "Auto Clear Discontinued").feed_templates.last
    profile_name = "Auto Clear Discontinued"
    duplicate(opts.merge!(target_class: UserProfile, save_user: true, template_id: template.id, save_profile: true, is_remove_feed: true, profile_name: profile_name))
  end

  private

  def profile_exclude_attributes
    %w[id user_id is_template email created_at updated_at product_logs supplier_id]
  end

  def sync_field_exclude_attributes
    %w[id user_profile_id created_at updated_at]
  end

  # def convert_import_to_update_exclude_fields
  #   %w[quantity product_id]
  # end

  def remove_feed_exclude_fields
    %w[product_id]
  end

  def default_supported_providers
    {
      on_shopify: false,
      on_woocommerce: false,
      on_bigcommerce: false,
      on_wix: false,
      on_square: false,
      on_ekm: false,
      on_squarespace: false,
      on_quickbooks: false
    }
  end
end
