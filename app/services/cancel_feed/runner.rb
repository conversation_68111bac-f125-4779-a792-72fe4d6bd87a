module CancelFeed
  class Runner
    def self.call(user_profile)
      Rails.logger.error("CancelFeed::Runner fargate_task_id: #{user_profile.fargate_task_id}")

      return false if user_profile.fargate_task_id.blank? || (user_profile.fargate_task_id =~ /^bulk::.*/).present?

      params = {
        cluster: "stocksync-job-production",
        task: user_profile.fargate_task_id,
        reason: "Cancellation of task called from API"
      }

      client.stop_task(params)
    rescue Aws::ECS::Errors::ClientException
      false
    rescue Aws::ECS::Errors::InvalidParameterException => e
      (e.message == "The referenced task was not found")
    end

    def self.get_running_fargates
      fargate_tasks = client.list_tasks(cluster: "stocksync-job-production", desired_status: "RUNNING").task_arns
      resp = client.describe_tasks(tasks: fargate_tasks, cluster: "stocksync-job-production")
      tasks = resp.tasks.map { |t| t.overrides.container_overrides.map { |o| o.command } }

      tasks.flatten.map { |t| t.split("[").last.split(",").first.to_i }.sort
    end

    def self.get_feed_not_in_fargate(feed_status: "processing")
      fargate_user_profiles = get_running_fargates

      processing_user_profiles = UserProfile.where(status: feed_status).where(job_queue: "main_jobs").where.not(fargate_task_id: nil).map(&:id).sort

      fargate_not_processing = fargate_user_profiles - processing_user_profiles
      processing_not_fargate = processing_user_profiles - fargate_user_profiles
      [fargate_not_processing, processing_not_fargate]
    end

    def self.get_unhealthy_and_stucked_fargate
      tasks = []
      fargate_tasks = client.list_tasks(cluster: "stocksync-job-production", desired_status: "RUNNING").task_arns
      resp = client.describe_tasks(tasks: fargate_tasks, cluster: "stocksync-job-production")
      resp.tasks.map { |t| tasks << t if t.created_at < 12.hours.ago && ["UNHEALTHY", "UNKNOWN"].include?(t.health_status.to_s) }

      # tasks = tasks.map{ |t| t.overrides.container_overrides.map { |o| o.command } }
      # tasks.flatten.map { |t| t.split("[").last.split(",").first.to_i }.sort
      tasks.map { |t| {task: t.task_arn.to_s.split("/").last, profile: t.overrides.container_overrides.first.try(:command).try(:first).to_s.split("[").last.split(",").first, health_status: t.health_status.to_s} }
    end

    def self.stop_hanging_fargate(task)
      params = {
        cluster: "stocksync-job-production",
        task: task,
        reason: "Stop hanging task"
      }

      client.stop_task(params)
    rescue Aws::ECS::Errors::ClientException
      false
    end

    def self.client
      AwsServices.ecs_client
    end

    def self.get_bulk_profiles_stuck
      profiles_list = []
      UserProfile.where("fargate_task_id LIKE ?", "bulk%").where.not(status: ["start", "pause"]).each do |profile|
        user = profile.user
        target = profile.fargate_task_id.split("::").last
        result = ShopifyAPI::LightGraphQL.poll_bulk(user.shopify_domain, user.shopify_token, target)

        ## check bulk status available
        status_present = result.dig("data", "node", "status").present?

        ## delayed job not available, bulk status available, bulk completed more than or equal to 12 hours ago
        delayed_job_and_status_check = !Delayed::Job.where(user_profile_id: profile.id, signature: ["lambda", "manual_queue", "rake_queue"]).present? && status_present && result.dig("data", "node", "status") == "COMPLETED" && (result.dig("data", "node", "completedAt") <= (Time.now - 12.hours))

        ## check for expired bulk
        expired_bulk = status_present && result.dig("data", "node", "status") == "EXPIRED"

        ## any of the check true, cancel the feed
        if delayed_job_and_status_check || !status_present || expired_bulk
          profiles_list << {profile: profile.id, bulk_status: status_present ? result.dig("data", "node", "status") : "N/A", bulk_id: target}
        end
      end
      profiles_list
    end

    def self.storelapse_task_cleanup
      fargate_tasks = client.list_tasks(cluster: "backup-job-cluster", desired_status: "RUNNING").task_arns
      resp = client.describe_tasks(tasks: fargate_tasks, cluster: "backup-job-cluster")
      old_tasks = resp.tasks.select { |t| t.started_at < 1.day.ago }
      if old_tasks.present?
        old_tasks.each do |task|
          client.stop_task(
            cluster: "backup-job-cluster",
            task: task.task_arn,
            reason: "Stop hanging storelapse task"
          )
          Rails.logger.debug "storelapse_task_cleanup Stopped #{task.task_arn} (started at #{task.started_at})"
        end
      else
        Rails.logger.debug "storelapse_task_cleanup No old tasks on storelapse cluster"
      end
    rescue => e
      Airbrake.notify("Something went wrong with storelapse_task_cleanup: #{e.message}")
      Rails.logger.debug("storelapse_task_cleanup error #{e.message}")
    end
  end
end
