import { faCircleExclamation } from '@fortawesome/pro-light-svg-icons';
import { Box, MenuItem, Typography, useTheme } from '@mui/material';
import { useState } from 'react';
import { useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { match, P } from 'ts-pattern';

import { Button } from '@/components/Button';
import { Dialog } from '@/components/Dialog';
import { Icon } from '@/components/Icon';
import { RadioGroup } from '@/components/RadioGroup';
import { Switch } from '@/components/Switch';
import { Table } from '@/components/Table';
import { Tags } from '@/components/Tags';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDisplayLanguages } from '@/hooks/useDisplayLanguages';
import { useSampleData } from '@/hooks/useSampleData';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { CaseConvert } from './CaseConvert';
import { FindField } from './FindField';
import { ReplaceField } from './ReplaceField';

const options = [
  { optionName: 'Colour', optionValue: 'Blue' },
  { optionName: 'Size', optionValue: 'M' },
  { optionName: 'Material', optionValue: 'Cotton' },
];

interface OptionProps {
  index: number;
  title: 'option1' | 'option2' | 'option3';
}

export function Option({ index, title }: OptionProps) {
  const { currentFeed } = useCurrentFeed();
  const { currentStore } = useCurrentStore();
  const { t } = useTranslation();
  const theme = useTheme();
  const { data: sampleData, loading } = useSampleData();
  const { languages } = useDisplayLanguages();

  const { control, setValue } = useFeedMappingFormContext();

  const dynamicOptionName = useWatch({
    control,
    name: `productOptionsSyncField.${index}.extra_attributes.dynamic_option_name`,
  });

  const optionFieldMapping = useWatch({
    control,
    name: `productOptionsSyncField.${index}.field_mapping`,
  });

  const optionName = useWatch({
    control,
    name: `productOptionsSyncField.${index}.extra_attributes.${title}_name`,
  });

  const watchOriginalLanguage = useWatch({
    control,
    name: `productOptionsSyncField.${index}.extra_attributes.original_language`,
  });

  const [open, setOpen] = useState(false);
  const closeDialog = () => setOpen(false);
  const openDialog = () => setOpen(true);

  return (
    <>
      <Tags
        label={dynamicOptionName ? t('label_mapping') : t('label_option_name')}
        name={`productOptionsSyncField.${index}.extra_attributes.${title}_name`}
        control={control}
        loading={loading}
        whitelist={
          dynamicOptionName
            ? sampleData?.map((data, index) => ({
                value: data.key
                  ? currentFeed.hasHeader
                    ? data.key
                    : String(index + 1)
                  : '-',
                text: data.value
                  ? data.value.toString().substring(0, 120).concat('...')
                  : '-',
              }))
            : []
        }
        renderOption={(props, item) => (
          <div
            {...props}
            style={{
              pointerEvents: props.value === '-' ? 'none' : 'initial',
            }}
          >
            <Typography component="div" style={theme.typography.body1}>
              {item.value}
            </Typography>
            <Typography
              component="div"
              style={{
                ...theme.typography.body2,
                wordBreak: 'break-all',
                color: theme.palette.grey[300],
              }}
            >
              {item.text}
            </Typography>
          </div>
        )}
        settings={{
          dropdown: {
            enabled: 0, // a;ways show suggestions dropdown
            maxItems: 50,
            caseSensitive: true,
          },
        }}
      />
      <RadioGroup
        control={control}
        size="small"
        direction="row"
        name={`productOptionsSyncField.${index}.extra_attributes.dynamic_option_name`}
        options={[
          { label: t('mappings'), value: true },
          { label: t('static'), value: false },
        ]}
        onChange={(_, value) => {
          const isDynamicOptionName = value === 'true';
          // cast to boolean. DOM API casts to string https://mui.com/material-ui/api/radio-group/#props
          setValue(
            `productOptionsSyncField.${index}.extra_attributes.dynamic_option_name`,
            isDynamicOptionName
          );

          if (!optionName && !isDynamicOptionName)
            setValue(
              `productOptionsSyncField.${index}.extra_attributes.${title}_name`,
              optionFieldMapping ?? '' // TODO: null error without ?? ''
            );

          if (isDynamicOptionName) openDialog();
        }}
      />
      <Box sx={{ mb: theme.spacing(6) }} />
      <CaseConvert
        control={control}
        name={`productOptionsSyncField.${index}.extra_attributes.case_convert`}
      />
      {currentFeed.humanizeFeedType === 'export' ? (
        <>
          <Box sx={{ mb: theme.spacing(6) }} />
          <Switch
            name={`productOptionsSyncField.${index}.extra_attributes.auto_remove_default_title`}
            control={control}
            label={t('auto_remove_default_title')}
            helperText={t('auto_remove_default_title_help_text')}
          />
        </>
      ) : (
        <>
          <Box sx={{ mb: theme.spacing(6) }} />
          <FindField
            control={control}
            name={`productOptionsSyncField.${index}.extra_attributes.find`}
          />
          <Box sx={{ mb: theme.spacing(6) }} />
          <ReplaceField
            control={control}
            name={`productOptionsSyncField.${index}.extra_attributes.replace`}
          />
        </>
      )}
      <Box sx={{ mb: theme.spacing(6) }} />
      {match({
        humanizeFeedType: currentFeed.humanizeFeedType,
        enableTranslationFeature: currentStore.enableTranslationFeature,
      })
        .with(
          {
            humanizeFeedType: P.when((type) => type !== 'export'),
            enableTranslationFeature: true,
          },
          () => (
            <>
              <TextField
                select
                control={control}
                name={`productOptionsSyncField.${index}.extra_attributes.original_language`}
                label={t('translate_from')}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>

              <Box sx={{ mb: theme.spacing(6) }} />
              <TextField
                select
                control={control}
                name={`productOptionsSyncField.${index}.extra_attributes.returned_language`}
                label={t('translate_to')}
                disabled={watchOriginalLanguage === 'no change'}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>
            </>
          )
        )
        .otherwise(() => (
          <></>
        ))}
      <Dialog open={open} onClose={closeDialog} maxWidth="xs">
        <Dialog.Title>
          <Typography
            variant="button"
            sx={{ mb: theme.spacing(2), display: 'block' }}
          >
            <Icon
              type="default"
              icon={faCircleExclamation}
              size="lg"
              style={{ marginRight: theme.spacing(1) }}
            />
            {t('auto_assign_value')}
          </Typography>
        </Dialog.Title>
        <Dialog.Content>
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Cell>{t('label_option_name')}</Table.Cell>
                <Table.Cell>{t('option_value')}</Table.Cell>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {options.map((option, index) => (
                <Table.Row
                  key={index}
                  sx={{
                    '&:last-child td, &:last-child th': { border: 0 },
                  }}
                >
                  <Table.Cell>{option.optionName}</Table.Cell>
                  <Table.Cell>{option.optionValue}</Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </Dialog.Content>
        <Dialog.Actions>
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              setValue(
                `productOptionsSyncField.${index}.extra_attributes.dynamic_option_name`,
                false
              );
              closeDialog();
            }}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="contained"
            size="small"
            onClick={() => {
              setValue(
                `productOptionsSyncField.${index}.extra_attributes.${title}_name`,
                ''
              );
              closeDialog();
            }}
          >
            {t('proceed')}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </>
  );
}
