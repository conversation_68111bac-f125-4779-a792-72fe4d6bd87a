import { faCircleQuestion } from '@fortawesome/pro-light-svg-icons';
import { Box, Divider, FormLabel, Typography, useTheme } from '@mui/material';
import * as React from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { Autocomplete } from '@/components/Autocomplete';
import { Button } from '@/components/Button';
import { FormHelperText } from '@/components/FormHelperText';
import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { RadioGroup } from '@/components/RadioGroup';
import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { FormDivider } from '../FormDivider';
import { FindField } from './FindField';
import { ReplaceField } from './ReplaceField';

interface ImageProps {
  index: number;
}

export function Image({ index }: ImageProps) {
  const {
    currentStore: {
      feedConstants: { image_col_sep_options },
    },
    currentStore,
  } = useCurrentStore();

  const { t } = useTranslation();
  const theme = useTheme();
  const { currentFeed } = useCurrentFeed();
  const { getValues, watch, control, setValue } = useFeedMappingFormContext();
  const [feedType, ioMode, runWebhookMode] = getValues([
    'feedType',
    'ioMode',
    'runWebhookMode',
  ]);
  const variantImageLink = watch('variantImageLink');
  const [anchorEl, setAnchorEl] = React.useState<HTMLInputElement | null>(null);

  return (
    <>
      {currentFeed.humanizeFeedType === 'export' &&
        currentStore.provider === 'shopify' && (
          <>
            <FormLabel sx={{ color: 'black' }}>
              <Typography variant="h5">{t('export_image_option')}</Typography>
            </FormLabel>
            <FormDivider smallGapBetweenComponent />
            <RadioGroup
              control={control}
              name={`syncFieldSettings.${index}.extra_attributes._variantImageFallback`}
              options={[
                {
                  label: t('all_product_images'),
                  labelHelperText: t('export_all_image'),
                  value: 'all',
                },
                {
                  label: t('variant_with_product_image_as_backup'),
                  labelHelperText: t('export_image_for_each_variant'),
                  value: 'first',
                },
                {
                  label: t('variant_only'),
                  labelHelperText: t('export_variant_image'),
                  value: 'none',
                },
              ]}
              onChange={({ target: { value } }) => {
                if (value === 'all') {
                  setValue(
                    `syncFieldSettings.${index}.extra_attributes.variant_image_fallback`,
                    null
                  );
                } else {
                  setValue(
                    `syncFieldSettings.${index}.extra_attributes.variant_image_fallback`,
                    value
                  );
                }
              }}
            />
            <Divider sx={{ m: theme.spacing(3, 0) }} />
          </>
        )}
      {feedType !== 'remove' && ioMode === 'in' && runWebhookMode && (
        <>
          <FormLabel sx={{ color: 'black' }}>
            <Typography variant="h5">{t('variant_image_option')}</Typography>
          </FormLabel>
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name="variantImageLink"
            label={t('variant_image_link')}
          />
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name="assignVariantsToFirstImage"
            label={t('assign_first_image_to_all_variant')}
            disabled={variantImageLink ? false : true}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
        </>
      )}
      {currentStore.provider !== 'wix' &&
        currentFeed.humanizeFeedType === 'update' && (
          <>
            <RadioGroup
              control={control}
              name={`syncFieldSettings.${index}.extra_attributes.force_override`}
              options={[
                { label: t('no_image_append_image'), value: false },
                { label: t('skip_image_import_new_image'), value: true },
              ]}
              helperText={
                <div style={{ paddingLeft: '15px' }}>
                  <Typography component="span" variant="body2">
                    {t('image_force_override_help_text')}
                  </Typography>
                </div>
              }
            />
            <Divider sx={{ m: theme.spacing(3, 0) }} />
          </>
        )}
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <FindField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.find`}
          />
          <Box sx={{ mb: theme.spacing(6) }} />
          <ReplaceField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.replace`}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
        </>
      )}

      {ioMode === 'in' && (
        <>
          <TextField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.alt_mapping`}
            label={t('label_images_alt_mapping')}
            helperText={t('help_text_images_alt_mapping')}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
        </>
      )}
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.url_prefix`}
        label={t('label_images_url_prefix')}
        helperText={t('help_text_images_url_prefix')}
      />
      <FormDivider smallGapBetweenComponent />

      {ioMode === 'in' && (
        <>
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.image_cdn`}
            label={
              <>
                {t('modify_url')}
                <Link
                  to="https://help.stock-sync.com/en/article/cdnproxy-url-9i3ttt/"
                  sx={{ marginLeft: '8px' }}
                >
                  <Button
                    variant="text"
                    size="small"
                    sx={{ padding: 0 }}
                    startIcon={<Icon type="default" icon={faCircleQuestion} />}
                  >
                    {t('learn_more')}
                  </Button>
                </Link>
              </>
            }
            helperText={t('help_text_image_cdn')}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <FormDivider smallGapBetweenComponent />
          <TextField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.default_image_url`}
            label={t('label_default_image_url')}
            helperText={t('use_image_url_if_no_image')}
          />
          <FormDivider />
          <Autocomplete
            freeSolo
            name={`syncFieldSettings.${index}.extra_attributes.col_sep`}
            control={control}
            label={t('label_images_col_sep')}
            helperText={t('help_text_images_col_sep')}
            options={image_col_sep_options.map((o) => o.value)}
            onChange={(_, value) => {
              const keyFromValue = image_col_sep_options.find(
                (option) => option.value === value
              )?.key;
              setValue(
                `syncFieldSettings.${index}.extra_attributes.col_sep`,
                keyFromValue ?? value ?? ''
              );
            }}
            getOptionLabel={(option) => option}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <Typography variant="h5">{t('url_option')}</Typography>
          <FormDivider smallGapBetweenComponent />
          <RadioGroup
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.url_unescape`}
            options={[
              {
                label: (
                  <Trans i18nKey="label_url_unescape_with_sample">
                    Unescape URL{' '}
                    <FormHelperText component="span">
                      https%3A// to https://
                    </FormHelperText>
                  </Trans>
                ),
                value: true,
              },
              {
                label: t('raw_url'),
                value: false,
              },
            ]}
          />
          {feedType !== 'import' && (
            <>
              <Divider sx={{ m: theme.spacing(3, 0) }} />
              <Switch
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.clear_current_images`}
                label={t('clear_image_before_import')}
                helperText={t('help_text_images_clear_current_images')}
                onChange={(event, checked) => {
                  if (checked) {
                    setAnchorEl(event.currentTarget);
                  } else {
                    setValue(
                      `syncFieldSettings.${index}.extra_attributes.clear_current_images`,
                      false
                    );
                  }
                }}
              />
              <PopoverConfirm
                open={Boolean(anchorEl)}
                anchorEl={anchorEl}
                onClose={() => {
                  setAnchorEl(null);
                  setValue(
                    `syncFieldSettings.${index}.extra_attributes.clear_current_images`,
                    false
                  );
                }}
                cancelButtonProps={{
                  children: t('cancel'),
                  onClick: () => {
                    setAnchorEl(null);
                    setValue(
                      `syncFieldSettings.${index}.extra_attributes.clear_current_images`,
                      false
                    );
                  },
                }}
                okButtonProps={{
                  children: t('yes'),
                  onClick: () => {
                    setAnchorEl(null);
                    setValue(
                      `syncFieldSettings.${index}.extra_attributes.clear_current_images`,
                      true
                    );
                  },
                }}
              >
                {t('clear_current_images_confirm')}
              </PopoverConfirm>
            </>
          )}
          <FormDivider />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
            label={t('skip_if_empty')}
          />
        </>
      )}
    </>
  );
}
