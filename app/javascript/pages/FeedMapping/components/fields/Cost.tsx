import { Divider, MenuItem, useTheme } from '@mui/material';
import { useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { FormDivider } from '../FormDivider';
import { CurrencyConverter } from './CurrencyConverter';
import { PriceCondition } from './PriceCondition';
import { PriceDelimiter } from './PriceDelimiter';
import { RestrictCondition } from './RestrictCondition';

interface CostProps {
  index: number;
  title: string;
}

export function Cost({ index, title }: CostProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: {
      feedConstants: { cost_round_options: costRoundOptions },
    },
  } = useCurrentStore();
  const { control } = useFeedMappingFormContext();
  const { currentFeed } = useCurrentFeed();
  const watchForceOverrideCost = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.force_override_cost`,
  });
  const watchSkipIfBlank = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.skip_if_blank`,
  });
  return (
    <>
      <PriceCondition
        fieldName="cost_pricing_conditions"
        index={index}
        title={title}
      />
      <PriceDelimiter index={index} title={title} />
      <FormDivider />
      <TextField
        select
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.price_round`}
        label={
          <span>
            {t(title)} {t('round')}
          </span>
        }
      >
        {costRoundOptions.map((option) => (
          <MenuItem key={option.key} value={option.key}>
            {option.value}
          </MenuItem>
        ))}
      </TextField>
      <FormDivider />
      <CurrencyConverter index={index} />
      <Divider sx={{ m: theme.spacing(3, 0) }} />
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.force_override_cost`}
            label={t('label_force_override_compare_at_price')}
            disabled={watchSkipIfBlank}
          />
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
            label={t('skip_if_empty')}
            disabled={watchForceOverrideCost}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <RestrictCondition
            fieldName="cost_restrict_conditions"
            index={index}
            title={title}
          />
        </>
      )}
    </>
  );
}
