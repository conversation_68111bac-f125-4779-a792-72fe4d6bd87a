import {
  faCircleArrowDown,
  faCircleArrowUp,
  faCircleXmark,
  faPlus,
} from '@fortawesome/pro-light-svg-icons';
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
} from '@mui/lab';
import { Box, Stack, Typography, useTheme } from '@mui/material';
import { useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/Button';
import { ErrorHelperText } from '@/components/ErrorHelperText';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { TextField } from '@/components/TextField';
import type { SyncFieldSetting } from '@/types';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';

interface PriceConditionProps {
  index: number;
  title: string;
  fieldName: keyof Pick<
    SyncFieldSetting['extra_attributes'],
    | 'compare_at_pricing_conditions'
    | 'cost_pricing_conditions'
    | 'pricing_conditions'
  >;
}

export function PriceCondition({
  index,
  fieldName,
  title,
}: PriceConditionProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, formState, trigger } = useFeedMappingFormContext();
  const { fields, prepend, remove, swap } = useFieldArray({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.${fieldName}`,
  });

  return (
    <>
      <Typography variant="h5" sx={{ mb: theme.spacing(2) }}>
        {t(title)} {t('condition_markdown_or_markup')}
      </Typography>
      <Box sx={{ mt: theme.spacing(1), mb: theme.spacing(3) }}>
        <Button
          variant="outlined"
          startIcon={<Icon type="default" icon={faPlus} />}
          onClick={() => prepend({ condition: 'any', formula: '' })}
        >
          {t('add_condition')}
        </Button>

        {fields.length > 0 && (
          <Timeline sx={{ p: theme.spacing(0) }}>
            {fields.map((field, fieldIndex) => (
              <Box
                key={field.id}
                sx={{
                  border: '1px solid rgba(136,152,170,.4)',
                  borderRadius: '8px',
                  padding: '16px',
                  marginBottom: '16px',
                }}
              >
                <Stack
                  direction="row"
                  sx={{ justifyContent: 'space-between', marginBottom: '16px' }}
                >
                  <Box>
                    {fieldIndex !== 0 && (
                      <IconButton
                        onClick={() => swap(fieldIndex, fieldIndex - 1)}
                        id="switch-condition-to-top"
                      >
                        <Icon type="default" icon={faCircleArrowUp} />
                      </IconButton>
                    )}
                    {fieldIndex !== fields.length - 1 && (
                      <IconButton
                        onClick={() => swap(fieldIndex, fieldIndex + 1)}
                        id="switch-condition-to-bottom"
                      >
                        <Icon type="default" icon={faCircleArrowDown} />
                      </IconButton>
                    )}
                  </Box>
                  <IconButton
                    sx={{ color: theme.palette.grey[300] }}
                    onClick={() => remove(fieldIndex)}
                    id="remove-price-condition"
                  >
                    <Icon type="default" icon={faCircleXmark} size="sm" />
                  </IconButton>
                </Stack>
                <TimelineItem
                  sx={{
                    '&:before': {
                      flex: theme.spacing(0),
                      padding: 0,
                      maxWidth: theme.spacing(0),
                    },
                  }}
                >
                  <TimelineSeparator>
                    <TimelineDot sx={{ margin: 0 }} />
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent sx={{ mb: theme.spacing(1) }}>
                    <TextField
                      control={control}
                      name={`syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}.condition`}
                      label={t('condition_if')}
                      // trigger delayed validation on array level.
                      onBlur={() => {
                        trigger(
                          `syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}`,
                          { shouldFocus: false }
                        );
                      }}
                    />
                  </TimelineContent>
                </TimelineItem>
                <TimelineItem
                  sx={{
                    '&:before': {
                      flex: theme.spacing(0),
                      p: theme.spacing(0),
                      maxWidth: theme.spacing(0),
                    },
                  }}
                >
                  <TimelineSeparator>
                    <TimelineDot sx={{ m: theme.spacing(0) }} />
                  </TimelineSeparator>
                  <TimelineContent>
                    <TextField
                      control={control}
                      name={`syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}.formula`}
                      label={t('price_then')}
                      // trigger delayed validation on array level.
                      onBlur={() => {
                        trigger(
                          `syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}`,
                          { shouldFocus: false }
                        );
                      }}
                    />
                  </TimelineContent>
                </TimelineItem>
                {/* array level validation errors - includes condition and formula */}
                <ErrorHelperText
                  name={`syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}`}
                  errors={formState.errors}
                  fontSize={14}
                />
              </Box>
            ))}
          </Timeline>
        )}
      </Box>
    </>
  );
}
