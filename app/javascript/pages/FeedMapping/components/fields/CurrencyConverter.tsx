import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
} from '@mui/lab';
import { MenuItem, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { currencies } from './currencies';

interface CurrencyConverterProps {
  index: number;
}

export function CurrencyConverter({ index }: CurrencyConverterProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, watch } = useFeedMappingFormContext();
  const currencyConverter = watch(
    `syncFieldSettings.${index}.extra_attributes.currency_converter`
  );
  return (
    <>
      <Switch
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.currency_converter`}
        label={t('currency_converter')}
      />
      {currencyConverter && (
        <Timeline>
          <TimelineItem
            sx={{
              '&:before': {
                flex: theme.spacing(0),
                p: theme.spacing(0),
                maxWidth: theme.spacing(0),
              },
            }}
          >
            <TimelineSeparator>
              <TimelineDot sx={{ m: theme.spacing(0) }} />
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.default_currency`}
                label={t('currency_from')}
              >
                {currencies.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.description}
                  </MenuItem>
                ))}
              </TextField>
            </TimelineContent>
          </TimelineItem>
          <TimelineItem
            sx={{
              '&:before': {
                flex: theme.spacing(0),
                p: theme.spacing(0),
                maxWidth: theme.spacing(0),
              },
            }}
          >
            <TimelineSeparator>
              <TimelineDot sx={{ m: theme.spacing(0) }} />
            </TimelineSeparator>
            <TimelineContent>
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.new_currency`}
                label={t('currency_to')}
              >
                {currencies.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.description}
                  </MenuItem>
                ))}
              </TextField>
            </TimelineContent>
          </TimelineItem>
        </Timeline>
      )}
    </>
  );
}
