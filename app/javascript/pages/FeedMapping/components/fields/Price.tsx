import { Divider, useTheme } from '@mui/material';

import { useCurrentFeed } from '@/hooks/useCurrentFeed';

import { FormDivider } from '../FormDivider';
import { CurrencyConverter } from './CurrencyConverter';
import { PriceCondition } from './PriceCondition';
import { PriceDelimiter } from './PriceDelimiter';
import { PriceRound } from './PriceRound';
import { RestrictCondition } from './RestrictCondition';

interface PriceProps {
  index: number;
  title: string;
}

export function Price({ index, title }: PriceProps) {
  const theme = useTheme();
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      <PriceCondition
        fieldName="pricing_conditions"
        index={index}
        title={title}
      />
      <FormDivider />
      <PriceDelimiter index={index} title={title} />
      <FormDivider />
      <PriceRound index={index} title={title} />
      <FormDivider smallGapBetweenComponent />
      <CurrencyConverter index={index} />
      {currentFeed.humanizeFeedType !== 'import' &&
        currentFeed.humanizeFeedType !== 'export' && (
          <>
            <Divider sx={{ m: theme.spacing(3, 0) }} />
            <RestrictCondition
              fieldName="restrict_conditions"
              index={index}
              title={title}
            />
          </>
        )}
    </>
  );
}
