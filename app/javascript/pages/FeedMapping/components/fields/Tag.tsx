import { Divider, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { Autocomplete } from '@/components/Autocomplete';
import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { FormDivider } from '../FormDivider';
import { FindField } from './FindField';
import { ReplaceField } from './ReplaceField';

interface TagProps {
  index: number;
}

export function Tag({ index }: TagProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    currentStore: {
      feedConstants: { tags_col_sep_options: tagsColSepOptions },
    },
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { control, setValue } = useFeedMappingFormContext();
  return (
    <>
      <TextField
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.field_prefix`}
        label={t('label_field_prefix')}
        helperText={t('help_text_field_prefix')}
      />
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <FindField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.find`}
          />
          <FormDivider />
          <ReplaceField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.replace`}
          />
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <TextField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.only_tags`}
            label={t('only_tags')}
            helperText={t('help_text_only_tags')}
          />
          <FormDivider />
          <TextField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.ignore_tags`}
            label={t('ignore_tags')}
            helperText={t('help_text_ignore_tags')}
          />
        </>
      )}
      {currentFeed.humanizeFeedType === 'update' && (
        <>
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.override_tags`}
            label={t('label_tags')}
            helperText={t('help_text_tags')}
          />
          <FormDivider smallGapBetweenComponent />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.add_tags`}
            label={t('label_add_tags')}
            helperText={t('help_text_add_tags')}
          />
        </>
      )}
      <Divider sx={{ m: theme.spacing(3, 0) }} />
      <Autocomplete
        freeSolo
        name={`syncFieldSettings.${index}.extra_attributes._col_sep`}
        control={control}
        label={t('label_tags_col_sep')}
        options={tagsColSepOptions.map((o) => o.value)}
        onChange={(_, value) => {
          const keyFromValue = tagsColSepOptions.find(
            (option) => option.value === value
          )?.key;
          setValue(
            `syncFieldSettings.${index}.extra_attributes.col_sep`,
            keyFromValue ?? value ?? undefined
          );
        }}
        getOptionLabel={(option) => option}
        helperText={t('help_text_tags_col_sep')}
      />
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
            label={t('skip_if_empty')}
          />
        </>
      )}
    </>
  );
}
