import { Box, MenuItem, useTheme } from '@mui/material';
import { useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { match, P } from 'ts-pattern';

import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDisplayLanguages } from '@/hooks/useDisplayLanguages';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';
import { CaseConvert } from './CaseConvert';
import { FindField } from './FindField';
import { IgnoreWords } from './IgnoreWords';
import { ReplaceField } from './ReplaceField';

interface ProductTitleProps {
  index: number;
}

export function ProductTitle({ index }: ProductTitleProps) {
  const {
    currentStore,
    currentStore: {
      feedConstants: { title_separator_options: titleSeparatorOptions },
    },
  } = useCurrentStore();
  const { t } = useTranslation();
  const theme = useTheme();
  const { control } = useFeedMappingFormContext();
  const { languages } = useDisplayLanguages();
  const { currentFeed } = useCurrentFeed();
  const watchOriginalLanguage = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.original_language`,
  });
  return (
    <>
      <TextField
        select
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.title_separator`}
        label={t('label_title_separator')}
        helperText={t('help_text_title_separator')}
      >
        {titleSeparatorOptions.map((option) => (
          <MenuItem key={option.key} value={option.key}>
            {option.value}
          </MenuItem>
        ))}
      </TextField>
      <Box sx={{ mb: theme.spacing(6) }} />
      <CaseConvert
        control={control}
        name={`syncFieldSettings.${index}.extra_attributes.case_convert`}
      />
      {currentFeed.humanizeFeedType !== 'export' && (
        <>
          <Box sx={{ mb: theme.spacing(6) }} />
          <IgnoreWords index={index} />
          <Box sx={{ mb: theme.spacing(6) }} />
          <Switch
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.auto_ignore_option_words`}
            label={t('label_auto_ignore_option_words')}
          />

          <Box sx={{ mb: theme.spacing(6) }} />
          <FindField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.find`}
          />
          <Box sx={{ mb: theme.spacing(6) }} />
          <ReplaceField
            control={control}
            name={`syncFieldSettings.${index}.extra_attributes.replace`}
          />
        </>
      )}
      <Box sx={{ mb: theme.spacing(6) }} />
      {match({
        humanizeFeedType: currentFeed.humanizeFeedType,
        enableTranslationFeature: currentStore.enableTranslationFeature,
      })
        .with(
          {
            humanizeFeedType: P.when((type) => type !== 'export'),
            enableTranslationFeature: true,
          },
          () => (
            <>
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.original_language`}
                label={t('translate_from')}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>

              <Box sx={{ mb: theme.spacing(6) }} />
              <TextField
                select
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.returned_language`}
                label={t('translate_to')}
                disabled={watchOriginalLanguage === 'no change'}
              >
                {languages.map((option) => (
                  <MenuItem key={option.key} value={option.key}>
                    {option.value}
                  </MenuItem>
                ))}
              </TextField>
            </>
          )
        )
        .otherwise(() => (
          <></>
        ))}
    </>
  );
}
