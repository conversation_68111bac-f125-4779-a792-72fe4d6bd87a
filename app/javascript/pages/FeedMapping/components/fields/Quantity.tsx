import {
  faArrowUpRightFromSquare,
  faCircleExclamation,
  faCircleQuestion,
} from '@fortawesome/pro-light-svg-icons';
import {
  Alert,
  Box,
  Divider,
  FormControl,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';

import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { RadioGroup } from '@/components/RadioGroup';
import { Switch } from '@/components/Switch';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import * as routes from '@/routes';

import { FormDivider } from '../FormDivider';
import { QuantityRules } from './QuantityRules';

export function Quantity({ index, title }) {
  const {
    currentStore,
    currentStore: {
      feedConstants: { quantity_delimiter_options: quantityDelimiterOptions },
    },
  } = useCurrentStore();
  const { currentFeed } = useCurrentFeed();
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, setValue, getValues } = useFormContext();
  const [locationId, locationName, feedType, ioMode, runWebhookMode] =
    getValues([
      'locationId',
      'locationName',
      'feedType',
      'ioMode',
      'runWebhookMode',
    ]);

  const addToInitQuantity = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.add_to_init_quantity`,
  });
  const resetInitQuantity = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.reset_init_quantity`,
  });
  const quantityUseOnHand = useWatch({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.quantity_use_on_hand`,
  });
  const syncFieldSettings = useWatch({
    control,
    name: 'syncFieldSettings',
  });

  const unavailableQuantityField = syncFieldSettings.filter((a) =>
    a.field_name.includes('quantity_')
  );

  const locationInList = React.useMemo(
    () => currentStore.locations?.find((l) => l.id === locationId),
    [currentStore, locationId]
  );

  const locations = React.useMemo(() => {
    return locationInList
      ? currentStore.locations
      : [
          {
            id: locationId,
            name: locationName,
            isFulfillmentService: false,
          },
        ].concat(currentStore.locations); // show inactive in options
  }, [currentStore, locationInList, locationId, locationName]);

  const locationDisplayBasePlatform = {
    shopify: ['update', 'import'],
    bigcommerce: ['update'],
    woocommerce: ['update', 'import'],
    squarespace: [''],
    wix: [''],
    quickbooks: [''],
    ekm: [''],
    square: ['update', 'import'],
  };
  return (
    <FormControl fullWidth component="fieldset" variant="standard">
      {feedType !== 'remove' && (
        <>
          {title === 'quantity_incoming' && (
            <Stack
              sx={{
                border: `solid 1px ${theme.palette.yellow[100]}`,
                bgcolor: theme.palette.yellow[50],
                borderRadius: '8px',
              }}
            >
              <Typography variant="body1" sx={{ m: theme.spacing(1, 3) }}>
                {t('quantity_incoming_note')}{' '}
              </Typography>
            </Stack>
          )}
          {(locationDisplayBasePlatform[currentStore.provider].includes(
            currentFeed.feedType
          ) ||
            (currentStore.multiLocationEnabled &&
              currentStore.provider === 'bigcommerce')) && (
            <>
              <Typography variant="h5">{t('location')}</Typography>
              <Box sx={{ paddingBottom: '8px' }} />
              <Typography variant="body1">
                {title?.includes('quantity_')
                  ? t('location_is_selected')
                  : t('select_location_to_update_inventory')}
              </Typography>
              <FormDivider />
              <TextField
                select
                name={
                  title?.includes('quantity_')
                    ? 'locationId'
                    : `syncFieldSettings.${index}.extra_attributes._location.id`
                }
                control={control}
                label={t('inventory_location')}
                disabled={title?.includes('quantity_')}
                onChange={({ target: { value } }) => {
                  setValue('locationId', value.toString());
                  setValue(
                    'locationName',
                    locations.find((l) => l.id === value)?.name ?? ''
                  );
                }}
              >
                {locations.map((option) => (
                  <MenuItem
                    key={`${option.id}-${option.name}`}
                    value={option.id}
                    disabled={!locationInList && option.id === locationId}
                  >
                    {option.name}
                  </MenuItem>
                ))}
              </TextField>
              {currentStore.provider === 'woocommerce' && (
                <Link to="https://help.stock-sync.com/en/article/how-to-install-the-stock-location-plugin-for-woocommerce-17jtbi7/">
                  <Icon
                    type="default"
                    icon={faCircleQuestion}
                    style={{ marginTop: theme.spacing(2) }}
                  />{' '}
                  {t('learn_more')}
                </Link>
              )}
              <FormDivider />
            </>
          )}

          {runWebhookMode &&
            ioMode === 'in' &&
            feedType === 'update' &&
            currentStore.provider === 'shopify' &&
            !title?.includes('quantity_') && (
              <>
                <Switch
                  disabled={locationInList?.isFulfillmentService}
                  name={`syncFieldSettings.${index}.extra_attributes.create_location`}
                  control={control}
                  label={
                    <Trans i18nKey="create_location_label">
                      Auto add stock to{' '}
                      <Link to="https://help.shopify.com/en/manual/locations/assigning-inventory-to-locations">
                        location
                        <Icon
                          type="default"
                          icon={faArrowUpRightFromSquare}
                          style={{
                            fontSize: 12,
                            margin: '0px 0px 1px 3px',
                          }}
                        />
                      </Link>{' '}
                      if not there
                    </Trans>
                  }
                />
                <Divider sx={{ m: theme.spacing(3, 0) }} />
              </>
            )}
        </>
      )}

      {!title?.includes('quantity_') && currentStore.provider === 'shopify' && (
        <>
          <Typography variant="h5" sx={{ mb: theme.spacing(2) }}>
            {t('quantity_selection')}
          </Typography>
          <FormDivider smallGapBetweenComponent />
          <RadioGroup
            name={`syncFieldSettings.${index}.extra_attributes.quantity_use_on_hand`}
            control={control}
            options={
              feedType === 'import'
                ? [
                    {
                      value: false,
                      label: t('quantity'),
                    },
                  ]
                : [
                    {
                      value: false,
                      label: t('quantity'),
                    },
                    {
                      value: true,
                      label: t('on_hand_quantity'),
                      disabled: unavailableQuantityField.length > 0,
                    },
                  ]
            }
          />
          {(quantityUseOnHand === true || quantityUseOnHand === 'true') && (
            <AlertInfo>{t('on_hand_quantity_help_text')}</AlertInfo>
          )}
          {unavailableQuantityField.length > 0 && (
            <AlertInfo>{t('disable_on_hand_quantity')}</AlertInfo>
          )}
          <Divider sx={{ m: theme.spacing(3, 0) }} />
        </>
      )}

      {feedType === 'update' &&
        ioMode === 'in' &&
        currentStore.provider === 'woocommerce' && (
          <>
            <Switch
              name={`syncFieldSettings.${index}.extra_attributes.only_update_stock_location`}
              control={control}
              label={t('only_update_stock_location')}
              helperText={t('only_update_stock_location_help_text')}
            />
            <FormDivider />
          </>
        )}

      <Typography variant="h5" sx={{ mb: theme.spacing(2) }}>
        {t('label_rules_json')}
      </Typography>
      <QuantityRules index={index} />
      <Divider sx={{ m: theme.spacing(3, 0) }} />
      {ioMode === 'in' &&
        feedType !== 'import' &&
        !title?.includes('quantity_') && (
          <>
            <Typography variant="h5" sx={{ mb: theme.spacing(2) }}>
              {t('Products')}
            </Typography>
            {currentStore.provider === 'woocommerce' ? (
              <Trans i18nKey="how_product_updated_in_storeV2_WC">
                <Typography variant="body1">
                  Manage how products are updated in the store. Apply to all
                  products in the feed. Will be ignored if
                  <span style={{ fontWeight: 600 }}>
                    Stock Management field mapped.
                  </span>
                </Typography>
              </Trans>
            ) : (
              <Trans i18nKey="how_product_updated_in_storeV2">
                <Typography variant="body1">
                  Manage how products are updated in the store. Apply to all
                  products in the feed. Will be ignored if
                  <span style={{ fontWeight: 600 }}>
                    Track inventory field mapped.
                  </span>
                </Typography>
              </Trans>
            )}
            <FormDivider />
            <Switch
              name="shopifyTrackInventory"
              control={control}
              label={
                <Link
                  to={
                    {
                      shopify:
                        'https://help.shopify.com/en/manual/products/inventory/transfers/enable-tracking',
                      wix: '',
                      woocommerce: '',
                      bigcommerce:
                        'https://support.bigcommerce.com/s/article/Inventory-Tracking?language=en_US#out-of-stock-settings',
                      squarespace: '',
                      quickbooks: '',
                      ekm: '',
                      square: '',
                    }[currentStore.provider]
                  }
                >
                  {
                    {
                      shopify: t('track_quantity'),
                      wix: t('track_quantity'),
                      woocommerce: t('stock_management_extra_attribute'),
                      bigcommerce: t('track_quantity'),
                      squarespace: t('track_quantity'),
                      quickbooks: t('track_quantity'),
                      ekm: t('track_quantity'),
                      square: t('track_quantity'),
                    }[currentStore.provider]
                  }
                  &nbsp;
                  <Icon
                    type="default"
                    icon={faArrowUpRightFromSquare}
                    style={{
                      fontSize: 12,
                      marginBottom: '1px',
                    }}
                  />
                </Link>
              }
              helperText={t('setup_inventory_tracking')}
            />
            <FormDivider />
          </>
        )}

      {ioMode === 'in' &&
        feedType !== 'import' &&
        currentStore.provider === 'bigcommerce' && (
          <>
            <Switch
              name="extraOptions.sync_inventory_availability"
              control={control}
              label={
                <Link to="https://support.bigcommerce.com/s/article/Inventory-Tracking?language=en_US#levels">
                  {t('sync_inventory_availability')}
                  &nbsp;
                  <Icon
                    type="default"
                    icon={faArrowUpRightFromSquare}
                    style={{
                      fontSize: 12,
                      marginBottom: '1px',
                    }}
                  />
                </Link>
              }
            />
            <FormDivider />
          </>
        )}
      {feedType !== 'import' && feedType !== 'remove' && (
        <>
          {ioMode === 'in' && !title?.includes('quantity_') && (
            <TextField
              name={`syncFieldSettings.${index}.extra_attributes._low_stock_level`}
              control={control}
              label={t('low_stock_level')}
              type="number"
              onChange={(event) =>
                setValue('lowStockLevel', Number(event.target.value))
              }
              helperText={
                <span>
                  {t('low_stock_level_helptext')}&nbsp;
                  <Link to={routes.notifications}>here.</Link>
                </span>
              }
            />
          )}
          {!title?.includes('quantity_') && ioMode !== 'out' && (
            <>
              <Divider sx={{ m: theme.spacing(3, 0) }} />
              <Typography variant="h5">{t('quantity_option')}</Typography>
              <FormDivider smallGapBetweenComponent />
              <RadioGroup
                name={`syncFieldSettings.${index}.extra_attributes._quantity_option`}
                control={control}
                options={[
                  {
                    value: JSON.stringify({
                      only_deduct_quantity: false,
                      add_to_init_quantity: false,
                    }),
                    label: t('overwrite_quantity'),
                  },
                  {
                    value: JSON.stringify({
                      only_deduct_quantity: false,
                      add_to_init_quantity: true,
                    }),
                    label: t('add_or_deduct_quantity'),
                  },
                  {
                    value: JSON.stringify({
                      only_deduct_quantity: true,
                      add_to_init_quantity: false,
                    }),
                    label: t('deduct_quantity'),
                  },
                ]}
                onChange={(_, jsonString) => {
                  const pairValues = JSON.parse(jsonString);
                  Object.entries(pairValues).forEach(([key, value]) => {
                    setValue(
                      `syncFieldSettings.${index}.extra_attributes.${key}`,
                      value
                    );
                  });
                }}
              />
            </>
          )}
          {ioMode !== 'out' && (
            <>
              <FormDivider smallGapBetweenComponent />
              <Switch
                name={`syncFieldSettings.${index}.extra_attributes.reset_init_quantity`}
                control={control}
                label={t('quantity_set_to_zero')}
                disabled={addToInitQuantity === false}
              />
              <FormDivider smallGapBetweenComponent />{' '}
              <Switch
                control={control}
                name={`syncFieldSettings.${index}.extra_attributes.skip_if_blank`}
                label={t('skip_if_empty')}
              />
            </>
          )}
          {addToInitQuantity && resetInitQuantity && (
            <>
              <Stack
                direction="row"
                sx={{
                  alignItems: 'flex-start',
                  margin: theme.spacing(3, 0, 0, 2),
                }}
                spacing="8px"
              >
                <Box sx={{ paddingTop: '1px' }}>
                  <Icon type="default" icon={faCircleExclamation} size="lg" />
                </Box>
                <span>{t('out_of_stock_strategy_helptext')}</span>
              </Stack>
            </>
          )}
          <FormDivider />
        </>
      )}
      {!title?.includes('quantity_') && (
        <TextField
          select
          name={`syncFieldSettings.${index}.extra_attributes.quantity_delimiter`}
          control={control}
          label={t('quantity_delimiter')}
        >
          {quantityDelimiterOptions.map((option) => (
            <MenuItem key={option.key} value={option.key}>
              {t(option.value)}
            </MenuItem>
          ))}
        </TextField>
      )}
    </FormControl>
  );
}

const AlertInfo = ({ children }) => {
  return (
    <Alert severity="info" sx={{ marginBottom: '16px' }}>
      <Typography
        variant="button"
        sx={{
          fontWeight: 400,
        }}
      >
        {children}
      </Typography>
    </Alert>
  );
};
