import { faCircleQuestion } from '@fortawesome/pro-light-svg-icons';
import { Stack } from '@mui/material';
import { type FieldValues, type UseControllerProps } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { FormHelperText } from '@/components/FormHelperText';
import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { TextField } from '@/components/TextField';

interface FindFieldProps<TFieldValues extends FieldValues = FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'control' | 'name'> {}

export function FindField<TFieldValues extends FieldValues>({
  name,
  control,
}: FindFieldProps<TFieldValues>) {
  const { t } = useTranslation();
  return (
    <>
      <Stack
        direction="row"
        spacing="16px"
        sx={{
          alignItems: 'center',
        }}
      >
        <TextField
          control={control}
          name={name}
          label={t('find')}
          multiline={true}
          maxRows={10}
        />
        <Link to="https://help.stock-sync.com/en/article/find-and-replace-in-matching-column-j30v6d/">
          <Icon type="default" icon={faCircleQuestion} fontSize={20} />
        </Link>
      </Stack>
      <FormHelperText>{t('find_help_text')}</FormHelperText>
    </>
  );
}
