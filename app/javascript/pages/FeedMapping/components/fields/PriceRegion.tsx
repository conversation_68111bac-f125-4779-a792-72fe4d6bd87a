import { Divider, useTheme } from '@mui/material';

import { useCurrentFeed } from '@/hooks/useCurrentFeed';

import { FormDivider } from '../FormDivider';
import { AdditionalCurrencies } from './AdditionalCurrencies';
import { PriceCondition } from './PriceCondition';
import { PriceDelimiter } from './PriceDelimiter';
import { PriceRound } from './PriceRound';
import { RestrictCondition } from './RestrictCondition';

interface PriceRegionProps {
  index: number;
  title: string;
}

export function PriceRegion({ index, title }: PriceRegionProps) {
  const theme = useTheme();
  const { currentFeed } = useCurrentFeed();
  return (
    <>
      <AdditionalCurrencies index={index} title={title} />
      <Divider sx={{ m: theme.spacing(2, 0) }} />
      <PriceCondition
        fieldName="pricing_conditions"
        index={index}
        title={title}
      />
      <FormDivider />
      <PriceDelimiter index={index} title={title} />
      <FormDivider />
      <PriceRound index={index} title={title} />
      {currentFeed.feedType !== 'import' && (
        <>
          <Divider sx={{ m: theme.spacing(3, 0) }} />
          <RestrictCondition
            fieldName="restrict_conditions"
            index={index}
            title={title}
          />
        </>
      )}
    </>
  );
}
