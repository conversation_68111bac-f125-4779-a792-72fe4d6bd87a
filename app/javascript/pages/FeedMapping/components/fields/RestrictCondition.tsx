import { faCircleXmark, faPlus } from '@fortawesome/pro-light-svg-icons';
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
} from '@mui/lab';
import { Box, MenuItem, Typography, useTheme } from '@mui/material';
import { useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { match } from 'ts-pattern';

import { Button } from '@/components/Button';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { TextField } from '@/components/TextField';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import type { SyncFieldSetting } from '@/types';

import { useFeedMappingFormContext } from '../../useFeedMappingFormContext';

const newRestrictCondition = {
  attribute: '',
  equal: true,
  value: '',
};

interface RestrictConditionProps {
  index: number;
  title: string;
  fieldName: keyof Pick<
    SyncFieldSetting['extra_attributes'],
    | 'compare_price_at_restrict_conditions'
    | 'cost_restrict_conditions'
    | 'restrict_conditions'
  >;
}

export function RestrictCondition({
  index,
  title,
  fieldName,
}: RestrictConditionProps) {
  const { currentStore } = useCurrentStore();
  const { t } = useTranslation();
  const theme = useTheme();
  const { control, trigger } = useFeedMappingFormContext();
  const { fields, remove, append } = useFieldArray({
    control,
    name: `syncFieldSettings.${index}.extra_attributes.${fieldName}`,
  });

  return (
    <>
      <Typography variant="h5" sx={{ mb: theme.spacing(2) }}>
        {t('dont_update')} {t(title)} {t('when')}
      </Typography>
      <Box sx={{ mt: theme.spacing(1), mb: theme.spacing(3) }}>
        <Button
          variant="outlined"
          startIcon={<Icon type="default" icon={faPlus} />}
          onClick={() => append(newRestrictCondition)}
        >
          {t('add_condition')}
        </Button>
        <Timeline sx={{ padding: 0 }}>
          {fields.map((field, fieldIndex) => (
            <Box
              key={field.id}
              sx={{
                border: '1px solid rgba(136,152,170,.2)',
                padding: theme.spacing(2),
                borderRadius: theme.spacing(1),
                mb: theme.spacing(3),
              }}
            >
              <Box sx={{ textAlign: 'right' }}>
                <IconButton
                  sx={{ color: theme.palette.grey[300] }}
                  onClick={() => remove(fieldIndex)}
                  id="remove-restrict-condition"
                >
                  <Icon type="default" icon={faCircleXmark} size="sm" />
                </IconButton>
              </Box>
              <TimelineItem
                sx={{
                  '&:before': {
                    flex: theme.spacing(0),
                    padding: 0,
                    maxWidth: theme.spacing(0),
                  },
                }}
              >
                <TimelineSeparator>
                  <TimelineDot sx={{ margin: 0 }} />
                  <TimelineConnector />
                </TimelineSeparator>
                <TimelineContent>
                  <TextField
                    select
                    control={control}
                    name={`syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}.attribute`}
                    label={t('column_name')}
                  >
                    {match(currentStore.provider)
                      .with('wix', () => [
                        { key: 'vendor', value: 'vendor' },
                        { key: 'product_type', value: 'product_type' },
                        { key: 'sku', value: 'sku' },
                      ])
                      .otherwise(() => [
                        { key: 'vendor', value: 'vendor' },
                        { key: 'product_type', value: 'product_type' },
                        { key: 'tags', value: 'product_tags' },
                      ])
                      .map((option) => (
                        <MenuItem key={option.key} value={option.key}>
                          {t(option.value)}
                        </MenuItem>
                      ))}
                  </TextField>
                </TimelineContent>
              </TimelineItem>
              <TimelineItem
                sx={{
                  '&:before': {
                    flex: theme.spacing(0),
                    padding: 0,
                    maxWidth: theme.spacing(0),
                  },
                }}
              >
                <TimelineSeparator>
                  <TimelineDot sx={{ margin: 0 }} />
                </TimelineSeparator>
                <TimelineContent>
                  <TextField
                    control={control}
                    name={`syncFieldSettings.${index}.extra_attributes.${fieldName}.${fieldIndex}.value`}
                    label={t('value')}
                    onBlur={() => {
                      trigger(
                        `syncFieldSettings.${index}.extra_attributes.${fieldName}`,
                        { shouldFocus: false }
                      );
                    }}
                  />
                </TimelineContent>
              </TimelineItem>
            </Box>
          ))}
        </Timeline>
      </Box>
    </>
  );
}
