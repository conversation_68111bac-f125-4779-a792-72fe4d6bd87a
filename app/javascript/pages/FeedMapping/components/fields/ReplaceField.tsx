import type { FieldValues, UseControllerProps } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { TextField } from '@/components/TextField';

interface ReplaceFieldProps<TFieldValues extends FieldValues = FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'control' | 'name'> {}

export function ReplaceField<TFieldValues extends FieldValues>({
  name,
  control,
}: ReplaceFieldProps<TFieldValues>) {
  const { t } = useTranslation();
  return (
    <TextField
      control={control}
      name={name}
      label={t('replace')}
      helperText={t('replace_help_text')}
      multiline={true}
      maxRows={10}
    />
  );
}
