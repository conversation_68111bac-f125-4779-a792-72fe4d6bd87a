import {
  faCircleInfo,
  faSquareArrowRight,
} from '@fortawesome/pro-light-svg-icons';
import {
  Box,
  CardActions,
  CardContent,
  Chip,
  Divider,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { match, P } from 'ts-pattern';

import { Card } from '@/components/Card';
import { CardActionArea } from '@/components/CardActionArea';
import { CustomAppBar } from '@/components/CustomAppBar';
import { FeedLimitDialog } from '@/components/FeedLimitDialog';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { Tooltip } from '@/components/Tooltip';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useIntroJs } from '@/hooks/useIntroJs';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { PreCheck } from '@/queries/StoreMutations';
import * as routes from '@/routes';
import { renderPropsByFeedType } from '@/shared/feedTypes';
import { numberFormat } from '@/shared/util';

const creditFeedTypes = [renderPropsByFeedType['import']];

const noChargeFeedTypes = [renderPropsByFeedType['remove']];

const subscriptionFeedTypes = [
  renderPropsByFeedType['update'],
  renderPropsByFeedType['export'],
];

const updateFeedOnly = [renderPropsByFeedType['update']];

export function CreateFeed() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const matchesSm = useMatches('sm');

  const totalUpdateFeeds =
    currentStore.feedsCountByType && currentStore.feedsCountByType.update
      ? currentStore.feedsCountByType.update
      : 0;

  const totalImportFeeds =
    currentStore.feedsCountByType && currentStore.feedsCountByType.import
      ? currentStore.feedsCountByType.import
      : 0;

  const totalRemoveFeeds =
    currentStore.feedsCountByType && currentStore.feedsCountByType.remove
      ? currentStore.feedsCountByType.remove
      : 0;

  useIntroJs({
    key: 'intro_create_feed',
    enabled: currentStore.isShowQuickGuide,
  });

  return (
    <>
      <CustomAppBar
        title={t('connect_product_feed')}
        redirectUrl={routes.home}
      />
      <Grid
        container
        direction="row"
        justifyContent="center"
        alignItems="flex-start"
        spacing={2}
      >
        <Grid size={{ xs: 12, md: 12 }}>
          <Box
            sx={{
              padding: theme.spacing(0, 0, 0, 0),
            }}
          >
            {match({
              provider: currentStore.provider,
            })
              .with(
                {
                  provider: P.when(
                    (provider) =>
                      provider === 'squarespace' || provider === 'quickbooks'
                  ),
                },
                () => (
                  <FeedCard
                    feedType={updateFeedOnly}
                    feedLimitInfo={
                      <Stack
                        direction={matchesSm ? 'column' : 'row'}
                        spacing={matchesSm ? 1 : 4}
                        sx={{
                          alignItems: 'flex-start',
                        }}
                      >
                        <div data-hint="Monitor your feed limits to stay within your subscription plan. Upgrade anytime if you need more capacity.">
                          <Statistic
                            title={t('feed_limit_used')}
                            feedLimit={
                              currentStore.package === 'Snappy'
                                ? '0 / 0'
                                : `${totalUpdateFeeds} / ${currentStore.profileLimit}`
                            }
                            reachLimit={
                              totalUpdateFeeds >= currentStore.profileLimit
                            }
                          />
                        </div>
                        <>
                          <Divider
                            orientation="vertical"
                            variant="middle"
                            flexItem
                            sx={{ margin: '0px 16px 8px' }}
                          />
                          <div data-hint="Total count of all product variants across your feeds. E.g 1 SKU with 3 colour (Red, Blue, Black), count as 3 variants.">
                            <Statistic
                              title={t('total_number_of_variants')}
                              feedLimit={`${numberFormat(currentStore.totalSkusCount)} / ${numberFormat(currentStore.limitSkus)} `}
                              reachLimit={
                                currentStore.totalSkusCount >=
                                currentStore.limitSkus
                              }
                            />
                          </div>
                        </>
                      </Stack>
                    }
                    howItWork="continue_update"
                  />
                )
              )
              .with(
                {
                  provider: P.when(
                    (provider) =>
                      provider === 'square' || provider === 'prestashop'
                  ),
                },
                () => (
                  <>
                    <FeedCard
                      feedType={updateFeedOnly}
                      feedLimitInfo={
                        <Stack
                          direction={matchesSm ? 'column' : 'row'}
                          spacing={matchesSm ? 1 : 4}
                          sx={{
                            alignItems: 'flex-start',
                          }}
                        >
                          <div data-hint="Monitor your feed limits to stay within your subscription plan. Upgrade anytime if you need more capacity.">
                            <Statistic
                              title={t('feed_limit_used')}
                              feedLimit={
                                currentStore.package === 'Snappy'
                                  ? '0 / 0'
                                  : `${totalUpdateFeeds} / ${currentStore.profileLimit}`
                              }
                              reachLimit={
                                totalUpdateFeeds >= currentStore.profileLimit
                              }
                            />
                          </div>
                          <>
                            <Divider
                              orientation="vertical"
                              variant="middle"
                              flexItem
                              sx={{ margin: '0px 16px 8px' }}
                            />
                            <div data-hint="Total count of all product variants across your feeds. E.g 1 SKU with 3 colour (Red, Blue, Black), count as 3 variants.">
                              <Statistic
                                title={t('total_number_of_variants')}
                                feedLimit={`${numberFormat(currentStore.totalSkusCount)} / ${numberFormat(currentStore.limitSkus)} `}
                                reachLimit={
                                  currentStore.totalSkusCount >=
                                  currentStore.limitSkus
                                }
                              />
                            </div>
                          </>
                        </Stack>
                      }
                      howItWork="continue_update"
                    />
                    <Box sx={{ mb: 3, mt: { xs: 4, lg: 0 }, pt: 1 }} />
                    {/* Show credit-required actions */}
                    <FeedCard
                      feedType={creditFeedTypes}
                      feedLimitInfo={
                        <Stack
                          direction={matchesSm ? 'column' : 'row'}
                          spacing={matchesSm ? 1 : 4}
                          sx={{
                            alignItems: 'flex-start',
                          }}
                        >
                          <div data-hint="Credits are used each time products are added. Can purchase any times ">
                            <Statistic
                              maxCountText
                              title={t('credit_remaining_create_feed')}
                              feedLimit={numberFormat(
                                currentStore.importLimitSkus
                              )}
                            />
                          </div>
                          <>
                            <Divider
                              orientation="vertical"
                              variant="middle"
                              flexItem
                              sx={{ margin: '0px 16px 8px' }}
                            />
                            <Statistic
                              withTooltip
                              title={t('feed_limit_used')}
                              feedLimit={`${totalImportFeeds} / ${currentStore.importProfileLimit} `}
                              reachLimit={
                                totalImportFeeds >=
                                currentStore.importProfileLimit
                              }
                            />
                          </>
                        </Stack>
                      }
                      howItWork="pay_as_you_add_products"
                    />
                  </>
                )
              )
              .with(
                {
                  provider: 'ekm',
                },
                () => (
                  <>
                    <FeedCard
                      feedType={updateFeedOnly}
                      feedLimitInfo={
                        <Stack
                          direction={matchesSm ? 'column' : 'row'}
                          spacing={matchesSm ? 1 : 4}
                          sx={{
                            alignItems: 'flex-start',
                          }}
                        >
                          <div data-hint="Monitor your feed limits to stay within your subscription plan. Upgrade anytime if you need more capacity.">
                            <Statistic
                              title={t('feed_limit_used')}
                              feedLimit={
                                currentStore.package === 'Snappy'
                                  ? '0 / 0'
                                  : `${totalUpdateFeeds} / ${currentStore.profileLimit}`
                              }
                              reachLimit={
                                totalUpdateFeeds >= currentStore.profileLimit
                              }
                            />
                          </div>
                          <>
                            <Divider
                              orientation="vertical"
                              variant="middle"
                              flexItem
                              sx={{ margin: '0px 16px 8px' }}
                            />
                            <div data-hint="Total count of all product variants across your feeds. E.g 1 SKU with 3 colour (Red, Blue, Black), count as 3 variants.">
                              <Statistic
                                title={t('total_number_of_variants')}
                                feedLimit={`${numberFormat(currentStore.totalSkusCount)} / ${numberFormat(currentStore.limitSkus)} `}
                                reachLimit={
                                  currentStore.totalSkusCount >=
                                  currentStore.limitSkus
                                }
                              />
                            </div>
                          </>
                        </Stack>
                      }
                      howItWork="continue_update"
                    />
                    <Box sx={{ mb: 3, mt: { xs: 4, lg: 0 }, pt: 1 }} />
                    {/* Show credit-required actions */}
                    <FeedCard
                      feedType={creditFeedTypes}
                      feedLimitInfo={
                        <Stack
                          direction={matchesSm ? 'column' : 'row'}
                          spacing={matchesSm ? 1 : 4}
                          sx={{
                            alignItems: 'flex-start',
                          }}
                        >
                          <div data-hint="Credits are used each time products are added. Can purchase any times ">
                            <Statistic
                              maxCountText
                              title={t('credit_remaining_create_feed')}
                              feedLimit={numberFormat(
                                currentStore.importLimitSkus
                              )}
                            />
                          </div>
                          <>
                            <Divider
                              orientation="vertical"
                              variant="middle"
                              flexItem
                              sx={{ margin: '0px 16px 8px' }}
                            />
                            <Statistic
                              withTooltip
                              title={t('feed_limit_used')}
                              feedLimit={`${totalImportFeeds} / ${currentStore.importProfileLimit} `}
                              reachLimit={
                                totalImportFeeds >=
                                currentStore.importProfileLimit
                              }
                            />
                          </>
                        </Stack>
                      }
                      howItWork="pay_as_you_add_products"
                    />
                    <Box sx={{ mb: 3, mt: { xs: 4, lg: 0 }, pt: 1 }} />
                    {/* Show no charge actions */}
                    <FeedCard
                      feedType={noChargeFeedTypes}
                      feedLimitInfo={
                        <Statistic
                          withTooltip
                          title={t('feed_limit_used')}
                          feedLimit={`${totalRemoveFeeds} / ${currentStore.removeProfileLimit} `}
                          reachLimit={
                            totalRemoveFeeds >= currentStore.removeProfileLimit
                          }
                        />
                      }
                      howItWork="clean_up_store_products"
                    />
                  </>
                )
              )
              .otherwise(() => (
                <>
                  {/* Subscription-related */}
                  <FeedCard
                    feedType={subscriptionFeedTypes}
                    feedLimitInfo={
                      <Stack
                        direction={matchesSm ? 'column' : 'row'}
                        spacing={matchesSm ? 1 : 4}
                        sx={{
                          alignItems: 'flex-start',
                        }}
                      >
                        <div data-hint="Monitor your feed limits to stay within your subscription plan. Upgrade anytime if you need more capacity.">
                          <Statistic
                            title={t('feed_limit_used')}
                            feedLimit={
                              currentStore.package === 'Snappy'
                                ? '0 / 0'
                                : `${totalUpdateFeeds} / ${currentStore.profileLimit}`
                            }
                            reachLimit={
                              totalUpdateFeeds >= currentStore.profileLimit
                            }
                          />
                        </div>
                        <>
                          <Divider
                            orientation="vertical"
                            variant="middle"
                            flexItem
                            sx={{ margin: '0px 16px 8px' }}
                          />
                          <div data-hint="Total count of all product variants across your feeds. E.g 1 SKU with 3 colour (Red, Blue, Black), count as 3 variants.">
                            <Statistic
                              title={t('total_number_of_variants')}
                              feedLimit={`${numberFormat(currentStore.totalSkusCount)} / ${numberFormat(currentStore.limitSkus)} `}
                              reachLimit={
                                currentStore.totalSkusCount >=
                                currentStore.limitSkus
                              }
                            />
                          </div>
                        </>
                      </Stack>
                    }
                    howItWork="continue_update"
                  />
                  <Box sx={{ mb: 3, mt: { xs: 4, lg: 0 }, pt: 1 }} />
                  {/* Show credit-required actions */}
                  <FeedCard
                    feedType={creditFeedTypes}
                    feedLimitInfo={
                      <Stack
                        direction={matchesSm ? 'column' : 'row'}
                        spacing={matchesSm ? 1 : 4}
                        sx={{
                          alignItems: 'flex-start',
                        }}
                      >
                        <div data-hint="Credits are used each time products are added. Can purchase any times ">
                          <Statistic
                            maxCountText
                            title={t('credit_remaining_create_feed')}
                            feedLimit={numberFormat(
                              currentStore.importLimitSkus
                            )}
                          />
                        </div>
                        <>
                          <Divider
                            orientation="vertical"
                            variant="middle"
                            flexItem
                            sx={{ margin: '0px 16px 8px' }}
                          />
                          <Statistic
                            withTooltip
                            title={t('feed_limit_used')}
                            feedLimit={`${totalImportFeeds} / ${currentStore.importProfileLimit} `}
                            reachLimit={
                              totalImportFeeds >=
                              currentStore.importProfileLimit
                            }
                          />
                        </>
                      </Stack>
                    }
                    howItWork="pay_as_you_add_products"
                  />

                  <Box sx={{ mb: 3, mt: { xs: 4, lg: 0 }, pt: 1 }} />
                  {/* Show no charge actions */}
                  <FeedCard
                    feedType={noChargeFeedTypes}
                    feedLimitInfo={
                      <Statistic
                        withTooltip
                        title={t('feed_limit_used')}
                        feedLimit={`${totalRemoveFeeds} / ${currentStore.removeProfileLimit} `}
                        reachLimit={
                          totalRemoveFeeds >= currentStore.removeProfileLimit
                        }
                      />
                    }
                    howItWork="clean_up_store_products"
                  />
                </>
              ))}
          </Box>
        </Grid>
      </Grid>
    </>
  );
}

const FeedCard = ({ feedType, feedLimitInfo, howItWork }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const matchesSm = useMatches('sm');
  const [feedLimitError, setFeedLimitError] = React.useState({
    openDialog: false,
    errorMessage: '',
  });

  const [, preCheck] = useMutation(PreCheck, {
    dataKey: 'preCheck',
  });
  const { currentStore } = useCurrentStore();

  return (
    <>
      <Card
        sx={{
          padding: '24px 24px 0 24px',
          width: '100%',
        }}
      >
        <Stack
          direction={matchesSm ? 'column' : 'row'}
          spacing={1}
          sx={{
            alignItems: matchesSm ? 'flex-start' : 'center',
            paddingBottom: '16px',
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 500 }}>
            {t(howItWork)}
          </Typography>
          {howItWork === 'clean_up_store_products' && (
            <span style={{ color: theme.palette.grey[300], fontSize: '12px' }}>
              {t('no_charges_applied')}
            </span>
          )}
          {howItWork === 'continue_update' && (
            <Link to={routes.quickGuide} sx={{ padding: '3px 0 0 8px' }}>
              <Typography variant="h6">
                {t('what_you_like_to_achieve')}
              </Typography>
            </Link>
          )}
        </Stack>
        {feedType.map((ft) => (
          <CardActionArea
            key={ft.type}
            onClick={() => {
              preCheck(
                {
                  feedType: ft.type === 'export' ? 'update' : ft.type,
                },
                {
                  onSuccess({ navigate }) {
                    console.log(`SUCCESS go to ${routes.newConnection}`);
                    navigate(routes.newConnection, {
                      state: { action: ft.type },
                    });
                  },
                  onError({ error }) {
                    setFeedLimitError({
                      openDialog: true,
                      errorMessage: error,
                    });
                  },
                }
              );
            }}
            sx={{
              margin: '16px 0',
              '&:hover': {
                '[data-icon]': {
                  color: '#303030',
                },
              },
              '[data-icon]': {
                color: '#8898aa33',
              },
            }}
          >
            <CardContent
              sx={{
                padding: '0',
                height: 'auto',
                '&:last-child': {
                  paddingBottom: '0px',
                  borderBottom: 'unset',
                },
              }}
            >
              <Box key={ft.type} className="box">
                <Box sx={{ padding: { xs: '16px 8px', sm: '16px' } }}>
                  <Stack
                    direction="row"
                    spacing="8px"
                    sx={{
                      alignItems: 'center',
                    }}
                  >
                    <Box sx={{ color: ft.iconProps.color }}>
                      <Icon
                        type="kit"
                        fontSize={24}
                        className={ft.iconProps.className}
                      />
                    </Box>
                    <Box sx={{ margin: '8px 4px', width: '100%' }}>
                      <Typography sx={{ fontSize: '16px' }} component="div">
                        {t(ft.title)}
                        {ft.title === 'Update' && (
                          <Chip
                            label={t('most_popular')}
                            size="small"
                            sx={{
                              marginLeft: '8px',
                              color: '#0C7FE8',
                              backgroundColor: '#0C7FE80F',
                            }}
                          />
                        )}
                      </Typography>
                      {!matchesSm && (
                        <Typography
                          variant="body1"
                          sx={{
                            color: theme.palette.grey[300],
                            fontWeight: 400,
                          }}
                        >
                          {match([currentStore.provider, ft.type])
                            .with(['woocommerce', 'remove'], () =>
                              t('hide_unwanted_products')
                            )
                            .with(['bigcommerce', 'remove'], () =>
                              t('archive_description_new_feed')
                            )
                            .with(['wix', 'remove'], () =>
                              t('archive_description_new_feed')
                            )
                            .otherwise(() => t(ft.description))}
                        </Typography>
                      )}
                    </Box>
                    <Icon
                      data-icon
                      type="default"
                      icon={faSquareArrowRight}
                      fontSize={32}
                    />
                  </Stack>
                </Box>
              </Box>
            </CardContent>
          </CardActionArea>
        ))}
        <CardActions
          sx={{
            padding: {
              xs: '16px 8px',
              sm: '16px 16px 24px 16px',
            },
            height: 'auto',
            width: '100%',
            display: 'block',
          }}
        >
          {feedLimitInfo}
        </CardActions>
        <FeedLimitDialog
          open={feedLimitError.openDialog}
          handleClose={() => {
            setFeedLimitError({ openDialog: false, errorMessage: '' });
          }}
          errorMessage={feedLimitError.errorMessage}
        />
      </Card>
    </>
  );
};

interface StatisticProps {
  title: string;
  feedLimit: string;
  reachLimit?: boolean;
  maxCountText?: boolean;
  withTooltip?: boolean;
}
const Statistic = ({
  title,
  feedLimit,
  reachLimit,
  maxCountText = false,
  withTooltip = false,
}: StatisticProps) => {
  const theme = useTheme();
  const matchesSm = useMatches('sm');
  return (
    <Box>
      <Stack
        direction="row"
        sx={{
          alignItems: 'center',
        }}
      >
        <Typography
          variant="subtitle2"
          sx={{ color: theme.palette.grey[300], fontWeight: 400 }}
        >
          {title}
        </Typography>
        {withTooltip && (
          <Tooltip
            variant="default"
            placement="right"
            title="For more feed limit please contact support"
          >
            <IconButton
              disableRipple
              sx={{
                width: '24px',
                height: '12px',
              }}
            >
              <Icon
                type="default"
                icon={faCircleInfo}
                fontSize={14}
                color={theme.palette.grey[300]}
              />
            </IconButton>
          </Tooltip>
        )}
      </Stack>
      <Typography
        variant={matchesSm ? 'body1' : 'h5'}
        sx={{
          color: reachLimit ? theme.palette.danger.main : 'unset',
        }}
      >
        {feedLimit}{' '}
        {!maxCountText && <span style={{ fontSize: '12px' }}>(max)</span>}
      </Typography>
    </Box>
  );
};
