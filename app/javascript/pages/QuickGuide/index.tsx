import { faCircleInfo } from '@fortawesome/pro-light-svg-icons';
import { faCheck } from '@fortawesome/pro-solid-svg-icons';
import { Box, Stack, Typography, useTheme } from '@mui/material';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CustomAppBar } from '@/components/CustomAppBar';
import { FeedLimitDialog } from '@/components/FeedLimitDialog';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { Tooltip } from '@/components/Tooltip';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { PreCheck } from '@/queries/StoreMutations';
import * as routes from '@/routes';
import { renderPropsByFeedType } from '@/shared/feedTypes';
import {
  connectQuickbooks,
  exportQuickguide,
  importQuickguide,
  removeQuickguide,
  updateQuickguide,
} from '@/shared/images';

export function QuickGuide() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const [feedLimitError, setFeedLimitError] = React.useState({
    openDialog: false,
    errorMessage: '',
  });
  const matchesSm = useMatches('sm');
  const videoFlag = useFeatureFlagEnabled('Video');

  const tabs = React.useMemo(() => {
    const updateFeedOnly = [renderPropsByFeedType['update']];
    const ekmAvailableFeedType = [
      renderPropsByFeedType['update'],
      renderPropsByFeedType['import'],
      renderPropsByFeedType['remove'],
    ];
    const squareAvailableFeedType = [
      renderPropsByFeedType['update'],
      renderPropsByFeedType['import'],
    ];
    const feedTypeForOtherPlatform = [
      renderPropsByFeedType['update'],
      renderPropsByFeedType['import'],
      renderPropsByFeedType['export'],
      renderPropsByFeedType['remove'],
    ];
    if (['squarespace', 'quickbooks'].includes(currentStore.provider)) {
      return updateFeedOnly;
    } else if (['ekm'].includes(currentStore.provider)) {
      return ekmAvailableFeedType;
    } else if (['square', 'prestashop'].includes(currentStore.provider)) {
      return squareAvailableFeedType;
    } else {
      return feedTypeForOtherPlatform;
    }
  }, [currentStore.provider]);

  const [preCheckState, preCheck] = useMutation(PreCheck, {
    dataKey: 'preCheck',
  });

  return (
    <>
      <CustomAppBar
        title={t('what_you_like_to_achieve')}
        redirectUrl={routes.newFeed}
      />
      <Grid container spacing={2} rowSpacing={3}>
        {tabs.map((tab) => (
          <Grid key={tab.type} size={{ xs: 12, md: 6 }}>
            <Card sx={{ height: '100%', padding: '0px' }}>
              {videoFlag && (
                <Link
                  to={
                    {
                      import: 'https://www.youtube.com/watch?v=UX_aGwEM9X0',
                      update: 'https://www.youtube.com/watch?v=n2OLgMGsgL0',
                      export: 'https://www.youtube.com/watch?v=kYSQRFaGmfw',
                      remove: 'https://www.youtube.com/watch?v=ze4wXrpnuJU',
                    }[tab.type ?? '']
                  }
                  target="_blank"
                >
                  <img
                    src={
                      {
                        import: importQuickguide,
                        update: updateQuickguide,
                        export: exportQuickguide,
                        remove: removeQuickguide,
                      }[tab.type ?? '']
                    }
                    width={matchesSm ? '390' : '490'}
                  />
                </Link>
              )}
              <Box
                sx={{
                  padding: '8px 24px 32px',
                  position: 'relative',
                  minHeight: { xs: '410px', md: '350px' },
                }}
              >
                <Stack
                  direction="row"
                  sx={{
                    justifyContent: 'flex-start',
                    alignItems: 'flex-start',
                    marginBottom: '32px',
                  }}
                  spacing={1}
                >
                  <Typography variant="h5" sx={{ fontWeight: '600' }}>
                    {t(
                      {
                        update: 'get_to_know_update',
                        import: 'get_to_know_add',
                        export: 'get_to_know_export',
                        remove: 'get_to_know_remove',
                      }[tab.type ?? '']
                    )}
                  </Typography>
                  <Box sx={{ color: tab.iconProps.color }}>
                    <Icon
                      type="kit"
                      fontSize={24}
                      className={tab.iconProps.className}
                    />
                  </Box>
                </Stack>
                {
                  {
                    update: (
                      <Stack spacing="8px">
                        <Feature>
                          <SecondaryText>
                            {t('monthly_subscription_required')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('keep_quantity_same')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('adjust_pricing_with')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('update_fields_available')}
                          </SecondaryText>
                        </Feature>
                      </Stack>
                    ),
                    export: (
                      <Stack spacing="8px">
                        <Feature>
                          <SecondaryText>
                            {t('monthly_subscription_required')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('export_from_store')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('filter_product_to_external_file')}
                          </SecondaryText>
                        </Feature>
                      </Stack>
                    ),
                    import: (
                      <Stack spacing="8px">
                        <Feature>
                          <SecondaryText>{t('require_credit')}</SecondaryText>
                          <Box
                            sx={{
                              display: 'inline-flex',
                              transform: 'translateY(2px)',
                            }}
                          >
                            <Tooltip
                              variant="default"
                              title={t('each_credit_can_be_used')}
                              placement="right"
                            >
                              <IconButton
                                disableRipple
                                sx={{
                                  height: '12px',
                                  width: theme.spacing(4),
                                  alignSelf: 'center',
                                }}
                              >
                                <Icon
                                  type="default"
                                  icon={faCircleInfo}
                                  fontSize={14}
                                />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('support_variant_and_options')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('undo_process_credit_returned')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('only_add_product_that_not_exist_in_store')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('support_sort_by_size')}
                          </SecondaryText>
                        </Feature>
                      </Stack>
                    ),
                    remove: (
                      <Stack spacing="8px">
                        <Feature>
                          <SecondaryText>
                            {t('monthly_subscription_required')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('remove_unwants_products')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('remove_process_cant_reverted')}
                          </SecondaryText>
                        </Feature>
                        <Feature>
                          <SecondaryText>
                            {t('trigger_by_not_in_feed_run_after_update')}
                          </SecondaryText>
                        </Feature>
                      </Stack>
                    ),
                  }[tab.type ?? '']
                }
                {currentStore.provider === 'quickbooks' &&
                  !currentStore.quickbooksRealmId &&
                  tab.type === 'update' && (
                    <Box sx={{ marginTop: '24px' }}>
                      <Button
                        variant="text"
                        sx={{ padding: '0px' }}
                        onClick={() =>
                          (window.location.href = routes.quickbooksAuthenticate(
                            currentStore.id
                          ))
                        }
                      >
                        <img
                          src={connectQuickbooks}
                          style={{ height: '35px' }}
                        />
                      </Button>
                    </Box>
                  )}
                {(currentStore.provider !== 'quickbooks' ||
                  currentStore.quickbooksRealmId) && (
                  <Button
                    variant="contained"
                    size="small"
                    loading={preCheckState.fetching}
                    onClick={async () => {
                      preCheck(
                        {
                          feedType: tab.type === 'export' ? 'update' : tab.type,
                        },
                        {
                          onSuccess({ navigate }) {
                            console.log(
                              `SUCCESS go to ${routes.newConnection}`
                            );
                            navigate(routes.newConnection, {
                              state: { action: tab.type },
                            });
                          },
                          onError({ error }) {
                            setFeedLimitError({
                              openDialog: true,
                              errorMessage: error,
                            });
                          },
                        }
                      );
                    }}
                    sx={{
                      position: 'absolute',
                      bottom: matchesSm ? 'initial' : '16px',
                      marginTop: matchesSm ? '32px' : '0px',
                    }}
                  >
                    {t('create_new_feed')}
                  </Button>
                )}
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>
      <FeedLimitDialog
        open={feedLimitError.openDialog}
        handleClose={() => {
          setFeedLimitError({ openDialog: false, errorMessage: '' });
        }}
        errorMessage={feedLimitError.errorMessage}
      />
    </>
  );
}

function Feature({ children, sx = {} }) {
  const theme = useTheme();

  return (
    <Typography variant="caption" sx={sx}>
      <Icon
        type="default"
        icon={faCheck}
        style={{ color: theme.palette.link }}
      />
      {children}
    </Typography>
  );
}

function SecondaryText({ children }) {
  const theme = useTheme();
  return (
    <Typography
      variant="h6"
      component="span"
      sx={{
        color: theme.palette.grey[300],
        marginLeft: '0px',
        wordBreak: 'break-word',
        whiteSpace: 'initial',
      }}
    >
      &nbsp;
      {children}
    </Typography>
  );
}
