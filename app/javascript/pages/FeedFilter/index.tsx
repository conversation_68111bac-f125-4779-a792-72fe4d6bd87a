import {
  faArrowUpRightAndArrowDownLeftFromCenter,
  faCircleInfo,
  faCircleQuestion,
  faPlus,
  faTrash,
} from '@fortawesome/pro-light-svg-icons';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Alert,
  Box,
  CircularProgress,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import {
  FormProvider,
  useFieldArray,
  useForm,
  useFormContext,
  useWatch,
} from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'urql';

import { Autocomplete } from '@/components/Autocomplete';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { ErrorHelperText } from '@/components/ErrorHelperText';
import { FeedEditAppBar, type OnSubmit } from '@/components/FeedEditAppBar';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { Loading } from '@/components/Loading';
import { Paper } from '@/components/Paper';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { QuickEditDialog } from '@/components/QuickEditDialog';
import {
  StoreProduct,
  useFormValuesStoreFilters,
} from '@/components/StoreProduct';
import { Switch } from '@/components/Switch';
import { Tags } from '@/components/Tags';
import { TextField } from '@/components/TextField';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useIntroJs } from '@/hooks/useIntroJs';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { useSampleData } from '@/hooks/useSampleData';
import { UpdateFeedSettings } from '@/queries/FeedSettings';
import { GetProductCounts } from '@/queries/Users';
import * as routes from '@/routes';
import { numberFormat } from '@/shared/util';
import { feedFilterOperatorOptions, type FeedFilter } from '@/types';

import { feedFiltersSchema, type FeedFiltersFormValues } from './schema';

const newFilter: FeedFilter = {
  key: '',
  op: 'equals',
  value: '',
};

export function FeedFilter() {
  const { currentStore } = useCurrentStore();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const theme = useTheme();
  const matchesMd = useMatches('md');
  const { currentFeed } = useCurrentFeed();

  const [, updateFeedSettings] = useMutation(UpdateFeedSettings, {
    dataKey: 'updateFeedSettings',
  });

  const storeFilters = useFormValuesStoreFilters(currentFeed.storeFilters);

  const formProps = useForm<FeedFiltersFormValues>({
    resolver: zodResolver(feedFiltersSchema),
    reValidateMode: 'onBlur',
    mode: 'all',
    values: {
      bypassBlankRow: currentFeed.bypassBlankRow,
      feedFilters: currentFeed.feedFilters,
      id: currentFeed.id,
      profileName: currentFeed.profileName,
      ignoreZeroQuantity: currentFeed.ignoreZeroQuantity,
      skipImportWithZeroQty: currentFeed.skipImportWithZeroQty,
      storeFilters,
      _filteredProduct: '0',
      _totalProduct: '0',
      _precision: 'exact',
    },
  });

  // https://react-hook-form.com/docs/useform/formstate
  // ❌ formProps.formState.isDirty is accessed conditionally, does not subscribe to changes of that state
  // ✅ read formState values to subscribe to changes:
  const { isDirty } = formProps.formState;

  const submitHandler: OnSubmit<FeedFiltersFormValues> =
    ({ onSuccess, onError, onSkip }) =>
    async (values) => {
      if (isDirty === false) {
        return onSkip({ data: values });
      }

      values.storeFilters.forEach((filter) => {
        filter.conditions.filter(Boolean).forEach((condition) => {
          if ('_value' in condition) {
            delete condition._value;
          }
        });
      });

      const variables = {
        feedId: parseInt(values.id),
        storeFilters: values.storeFilters,
        feedFilters: values.feedFilters,
        bypassBlankRow: values.bypassBlankRow,
        skipImportWithZeroQty: values.skipImportWithZeroQty,
        ignoreZeroQuantity: values.ignoreZeroQuantity,
        _filteredProduct: undefined,
        _totalProduct: undefined,
        _precision: undefined,
      };

      // TODO: updateFeedSettings variables types
      await updateFeedSettings(variables, {
        onSuccess,
        onError,
      });
    };
  const onSubmit = formProps.handleSubmit(
    submitHandler({
      onSuccess({ data: feed, enqueueSnackbar }) {
        enqueueSnackbar(`${feed.profileName} settings updated`, {
          variant: 'success',
        });
        navigate(routes.advancedSettingsRoute(feed.id));
      },
      onError() {},
      onSkip({ data: feed }) {
        navigate(routes.advancedSettingsRoute(feed.id));
      },
    })
  );
  const watchFilteredProduct = useWatch({
    // to ensure value changes and not break the component
    control: formProps.control,
    name: '_filteredProduct',
  });

  const watchTotalProduct = useWatch({
    // to ensure value changes and not break the component
    control: formProps.control,
    name: '_totalProduct',
  });

  useIntroJs({
    key: 'intro_step_three',
    enabled: currentStore.isShowQuickGuide,
  });

  return (
    <FormProvider {...formProps}>
      <form onSubmit={onSubmit}>
        <FeedEditAppBar
          title={t('feed_filter_title')}
          redirectUrl={routes.feedDetailRoute(currentFeed.id)}
          backButton={
            <FeedEditAppBar.BackButton
              loading={formProps.formState.isSubmitting}
              onClick={formProps.handleSubmit(
                submitHandler({
                  onSuccess({ data: feed, enqueueSnackbar }) {
                    enqueueSnackbar(`${feed.profileName} settings updated`, {
                      variant: 'success',
                    });
                    navigate(routes.mappingRoute(currentFeed.id));
                  },
                  onError() {},
                  onSkip() {
                    navigate(routes.mappingRoute(currentFeed.id));
                  },
                })
              )}
            >
              {t('back')}
            </FeedEditAppBar.BackButton>
          }
          nextButton={
            <FeedEditAppBar.NextButton
              type="submit"
              loading={formProps.formState.isSubmitting}
            >
              {t('next')}
            </FeedEditAppBar.NextButton>
          }
        />
        {currentFeed.humanizeFeedType !== 'import' && (
          <Stack
            direction={matchesMd ? 'column' : 'row'}
            spacing={3}
            sx={{ marginTop: '24px' }}
          >
            <Box sx={{ maxWidth: '250px', width: '100%' }}>
              <Typography variant="h5" sx={{ fontWeight: 500 }}>
                {t('store_product_filters')}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.grey[300],
                  marginTop: '8px',
                }}
              >
                {t('to_filter_products_from_store_products')}
                <Link
                  to="https://help.stock-sync.com/en/article/store-product-filter-1uqbxfq/"
                  sx={{
                    display: 'block',
                    verticalAlign: 'text-bottom',
                    whiteSpace: 'nowrap',
                    marginTop: '8px',
                  }}
                >
                  <Button
                    variant="text"
                    size="small"
                    sx={{ padding: 0 }}
                    startIcon={<Icon type="default" icon={faCircleQuestion} />}
                  >
                    {t('learn_more')}
                  </Button>
                </Link>
              </Typography>
              <div data-hint="Shows how many products are estimated to be processed. This number changes as you add filters.">
                {currentStore.provider === 'shopify' &&
                  watchFilteredProduct &&
                  watchTotalProduct && (
                    <React.Suspense
                      fallback={
                        <Paper
                          sx={{
                            background: '#6E6D7A0D',
                            margin: '8px 0px 24px',
                            padding: '16px',
                            maxWidth: '400px',
                          }}
                        >
                          <CircularProgress
                            color="inherit"
                            size={20}
                            sx={{ display: 'inline-flex' }}
                          />
                        </Paper>
                      }
                    >
                      <ProductsScanCount />
                    </React.Suspense>
                  )}
              </div>
            </Box>
            <Card
              data-hint="Store product filter <br> Choose which existing products in your store should be updated by this feed."
              sx={{ padding: '32px 24px 24px', width: '100%' }}
            >
              <React.Suspense fallback={<Loading sx={{ maxHeight: '33vh' }} />}>
                <StoreProduct />
              </React.Suspense>
            </Card>
          </Stack>
        )}

        {currentFeed.humanizeFeedType !== 'export' && (
          <Stack
            direction={matchesMd ? 'column' : 'row'}
            spacing={3}
            sx={{ marginTop: '24px' }}
          >
            <Box sx={{ maxWidth: '250px', width: '100%' }}>
              <Typography variant="h5" sx={{ fontWeight: 500 }}>
                {t('incoming_feed')}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.grey[300],
                  marginTop: '8px',
                }}
              >
                {t('filter_product_from_feed_file')}
                <Link
                  to="https://help.stock-sync.com/en/article/incoming-feed-filter-1autex5/"
                  sx={{
                    display: 'block',
                    verticalAlign: 'text-bottom',
                    whiteSpace: 'nowrap',
                    marginTop: '8px',
                  }}
                >
                  <Button
                    variant="text"
                    size="small"
                    sx={{ padding: 0 }}
                    startIcon={<Icon type="default" icon={faCircleQuestion} />}
                  >
                    {t('learn_more')}
                  </Button>
                </Link>
              </Typography>
            </Box>
            <Card sx={{ padding: '32px 24px 24px', width: '100%' }}>
              <React.Suspense fallback={<Loading sx={{ maxHeight: '33vh' }} />}>
                <IncomingFeed />
              </React.Suspense>
            </Card>
          </Stack>
        )}
        <Stack
          direction="row"
          spacing="8px"
          sx={{
            alignSelf: 'center',
            justifyContent: 'flex-end',
            marginTop: '16px',
            paddingBottom: '60px',
          }}
        >
          <FeedEditAppBar.BackButton
            loading={formProps.formState.isSubmitting}
            onClick={formProps.handleSubmit(
              submitHandler({
                onSuccess({ data: feed, enqueueSnackbar }) {
                  enqueueSnackbar(`${feed.profileName} settings updated`, {
                    variant: 'success',
                  });
                  navigate(routes.mappingRoute(currentFeed.id));
                },
                onError() {},
                onSkip() {
                  navigate(routes.mappingRoute(currentFeed.id));
                },
              })
            )}
          >
            {t('back')}
          </FeedEditAppBar.BackButton>
          <FeedEditAppBar.NextButton
            loading={formProps.formState.isSubmitting}
            onClick={onSubmit}
          >
            {t('next')}
          </FeedEditAppBar.NextButton>
        </Stack>
      </form>
    </FormProvider>
  );
}

function DeletePopover({ onClose, onConfirm, anchorEl }) {
  const { t } = useTranslation();
  return (
    <PopoverConfirm
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      cancelButtonProps={{ children: t('no'), onClick: onClose }}
      okButtonProps={{ children: t('yes'), onClick: onConfirm }}
    >
      {t('confirm_delete')}
    </PopoverConfirm>
  );
}

function IncomingFeed() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentFeed } = useCurrentFeed();
  const [open, setOpen] = React.useState(false);
  const [openIndex, setOpenIndex] = React.useState<number | null>(null);
  const name = 'feedFilters';
  const { control, formState, setValue, getValues } = useFormContext();
  const { fields, prepend, remove } = useFieldArray({
    control,
    name,
  });
  const { data: sampleData } = useSampleData();
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
  const [removeIndex, setRemoveIndex] = React.useState(-1);

  console.log('formState errors', formState.errors);
  return (
    <>
      <Button
        variant="outlined"
        startIcon={<Icon type="default" icon={faPlus} />}
        onClick={() =>
          setTimeout(() => {
            prepend(newFilter);
          }, 200)
        } // add delay because of race condition when click add filter button prepend new array first then update text input value
      >
        {t('add_filter')}
      </Button>
      <Box sx={{ marginTop: '32px' }} />
      {fields.map((field, fieldIndex) => (
        <React.Fragment key={field.id}>
          <Box sx={{ marginBottom: '48px' }}>
            <Stack direction="row" spacing="5px" sx={{ marginBottom: '32px' }}>
              <IconButton
                edge="start"
                disableRipple
                id="delete-incoming-feed-filter"
                onClick={(event) => {
                  setRemoveIndex(fieldIndex);
                  setAnchorEl(event.currentTarget);
                }}
                sx={{ color: theme.palette.grey[300], marginLeft: '0px' }}
              >
                <Icon type="default" icon={faTrash} fontSize={16} />
              </IconButton>
              <Stack
                direction="column"
                spacing="5px"
                sx={{ marginBottom: '0', width: '100%' }}
              >
                <Stack direction="row" spacing="5px">
                  <Autocomplete
                    freeSolo
                    variant="standard"
                    name={`${name}.${fieldIndex}.key`}
                    control={control}
                    label={t('feed_column_name')}
                    options={sampleData.map((o) => o.key)}
                    getOptionLabel={(option) => option}
                    onChange={(_, value) => {
                      const keyFromValue = sampleData.find(
                        (option) => option.key === value
                      )?.key;
                      setValue(
                        `${name}.${fieldIndex}.key`,
                        keyFromValue ?? value
                      );
                    }}
                  />
                  <TextField
                    select
                    name={`${name}.${fieldIndex}.op`}
                    control={control}
                    label={t('condition')}
                  >
                    {feedFilterOperatorOptions.map((option) => (
                      <MenuItem key={option.key} value={option.key}>
                        {t(option.value)}
                      </MenuItem>
                    ))}
                  </TextField>
                </Stack>
                <ErrorHelperText
                  name={`${name}.${fieldIndex}`}
                  errors={formState.errors}
                />
              </Stack>
            </Stack>
            <Stack direction="row" spacing={1}>
              {['update', 'import'].includes(currentFeed.humanizeFeedType) ? (
                <>
                  <IconButton
                    onClick={() => {
                      setOpen(true);
                      setOpenIndex(fieldIndex);
                    }}
                    sx={{
                      color: theme.palette.grey[300],
                      textAlign: 'center',
                      marginTop: '8px',
                    }}
                  >
                    <Icon
                      type="default"
                      icon={faArrowUpRightAndArrowDownLeftFromCenter}
                      fontSize={16}
                    />
                  </IconButton>
                  <QuickEditDialog
                    value={getValues(`${name}.${fieldIndex}.value`).toString()}
                    open={open && openIndex === fieldIndex}
                    onClose={() => {
                      setOpen(false);
                      setOpenIndex(null);
                    }}
                    onSubmit={(value) =>
                      setValue(`${name}.${fieldIndex}.value`, value)
                    }
                  />
                </>
              ) : (
                <Box sx={{ marginLeft: '42px' }} />
              )}
              <Tags
                name={`${name}.${fieldIndex}.value`}
                control={control}
                label={t('value')}
              />
            </Stack>
          </Box>
        </React.Fragment>
      ))}

      {currentFeed.humanizeFeedType === 'import' && (
        <>
          <Stack sx={{ maxWidth: '330px' }}>
            <Switch
              name="bypassBlankRow"
              control={control}
              sx={{ alignItems: 'flex-start' }}
              label={
                <Typography variant="h6" component="span">
                  {t('bypass_blank_row_label')}{' '}
                  <Link to="https://help.stock-sync.com/en/article/include-empty-row-1taei3d/">
                    {t('more_info')}
                  </Link>
                </Typography>
              }
            />
            <Box sx={{ marginTop: '8px' }}>
              <Switch
                name="skipImportWithZeroQty"
                control={control}
                label={t('skip_product_zero_quantity')}
                disabled={
                  !currentFeed.syncFieldSettings.find(
                    (field) => field.field_name === 'quantity'
                  )
                }
              />
            </Box>
          </Stack>
          {!currentFeed.syncFieldSettings.find(
            (field) => field.field_name === 'quantity'
          ) && (
            <Alert
              sx={{ marginTop: '8px' }}
              severity="warning"
              icon={
                <Icon
                  type="default"
                  icon={faCircleInfo}
                  style={{ fontSize: '18px', paddingTop: '3px' }}
                />
              }
            >
              <Typography variant="body1">
                <Trans i18nKey="quantity_field_mapped">
                  To enable this feature, please map quantity field on
                  <Link to={routes.mappingRoute(currentFeed.id)}>
                    &nbsp;Step 2: Matching column
                  </Link>
                </Trans>
              </Typography>
            </Alert>
          )}
        </>
      )}

      <DeletePopover
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        onConfirm={() => {
          remove(removeIndex);
          setAnchorEl(null);
        }}
      />
    </>
  );
}
function useProductCounts() {
  const { currentStore } = useCurrentStore();

  const { currentFeed } = useCurrentFeed();

  const { setValue, control } = useFormContext();

  const [{ data }] = useQuery({
    query: GetProductCounts,
    variables: {
      feedId: parseInt(currentFeed.id),
      storeFilters: useWatch({
        control,
        name: 'storeFilters',
      }),
    },
    pause: currentStore.provider !== 'shopify',
    requestPolicy: 'network-only',
  });

  React.useEffect(() => {
    if (!data) return;
    setValue('_filteredProduct', data.getProductCounts.filtered);
    setValue('_totalProduct', data.getProductCounts.total);
    setValue('_precision', data.getProductCounts.precision);
  }, [data, setValue]);
}

function ProductsScanCount() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { control } = useFormContext();

  useProductCounts();
  const watchPrecision = useWatch({
    control,
    name: '_precision',
  });

  const watchFilteredProduct = useWatch({
    control,
    name: '_filteredProduct',
  });

  const watchTotalProduct = useWatch({
    control,
    name: '_totalProduct',
  });

  return (
    <>
      <Paper
        sx={{
          background: '#6E6D7A0D',
          margin: '8px 0px 24px',
          padding: '16px',
          maxWidth: '400px',
        }}
      >
        <strong>
          {watchPrecision === 'at_least' ? '>' : ''}{' '}
          {numberFormat(Number(watchFilteredProduct))}
        </strong>{' '}
        / <strong>{numberFormat(Number(watchTotalProduct))}</strong>{' '}
        <span style={{ color: theme.palette.grey[300] }}>
          {t('total_product')}
        </span>{' '}
        <Box sx={{ marginTop: '16px' }} />
        <Typography variant="body1" style={{ color: theme.palette.grey[300] }}>
          {t('note_scanned')}
        </Typography>
      </Paper>
    </>
  );
}
