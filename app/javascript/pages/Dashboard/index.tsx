import {
  faArrowRotateRight,
  faArrowUpRightFromSquare,
  faCircleXmark,
  faFileInvoiceDollar,
  faMagnifyingGlass,
} from '@fortawesome/pro-light-svg-icons';
import { faCircleQuestion } from '@fortawesome/pro-solid-svg-icons';
import {
  Box,
  CircularProgress,
  Divider,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import { Crisp } from 'crisp-sdk-web';
import debounce from 'lodash/debounce';
import posthog from 'posthog-js';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Alert } from '@/components/Alert';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CustomAppBar } from '@/components/CustomAppBar';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { Tab } from '@/components/Tab';
import { Table } from '@/components/Table';
import { TextField } from '@/components/TextField';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import {
  useDashboardFeeds,
  type DashboardLiveFeed,
} from '@/hooks/useDashboardFeeds';
import { useIsEmbeddedApp } from '@/hooks/useIsEmbeddedApp';
import { useIsLegacyPricingPlan } from '@/hooks/useIsLegacyPricingPlan';
import { useMatches } from '@/hooks/useMatches';
import * as routes from '@/routes';
import { noDashboardImage, noSearchResult } from '@/shared/images';
import { renderAdminLink, shortenShopifyStore } from '@/shared/sharedUtil';
import { numberFormat } from '@/shared/util';

import { ExpandedFeedRow } from './components/ExpandedFeedRow';
import { FeedTableRow } from './components/FeedTableRow';

const tabFeedTypes = [
  'all',
  'import',
  'update',
  'export',
  'remove',
  'snappy',
] as const;

const otherPlatformTabFeedTypes = [
  'all',
  'import',
  'update',
  'export',
  'remove',
] as const;

type TabFeedType = (typeof tabFeedTypes)[number];

type OtherPlatformTabFeedType = (typeof otherPlatformTabFeedTypes)[number];

const tabs: Array<{ name: string; feedType: TabFeedType }> = [
  { name: 'all', feedType: 'all' },
  { name: 'add', feedType: 'import' },
  { name: 'Update', feedType: 'update' },
  { name: 'Export', feedType: 'export' },
  { name: 'Remove', feedType: 'remove' },
];

const otherPlatformTabs: Array<{
  name: string;
  feedType: OtherPlatformTabFeedType;
}> = [
  { name: 'All', feedType: 'all' },
  { name: 'Add', feedType: 'import' },
  { name: 'Update', feedType: 'update' },
  { name: 'Export', feedType: 'export' },
  { name: 'Remove', feedType: 'remove' },
];

export function Dashboard() {
  const { feeds: dashboardFeeds } = useDashboardFeeds();
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentStore } = useCurrentStore();
  const { isEmbeddedApp } = useIsEmbeddedApp();
  const [activeTab, setActiveTab] = React.useState<TabFeedType>('all');
  const [searchValue, setSearchValue] = React.useState('');
  const ref = React.useRef<HTMLDivElement | null>(null);
  const { isLegacyPricingPlan } = useIsLegacyPricingPlan();

  const feedsByFeedType = {
    all: React.useMemo(
      () => dashboardFeeds.filter(({ fastTrack }) => !fastTrack),
      [dashboardFeeds]
    ),
    import: React.useMemo(
      () => dashboardFeeds.filter(({ feedType }) => feedType === 'import'),
      [dashboardFeeds]
    ),
    update: React.useMemo(
      () =>
        dashboardFeeds.filter(
          ({ feedType, ioMode, fastTrack }) =>
            feedType === 'update' && ioMode === 'in' && !fastTrack
        ),
      [dashboardFeeds]
    ),
    export: React.useMemo(
      () => dashboardFeeds.filter(({ ioMode }) => ioMode === 'out'),
      [dashboardFeeds]
    ),
    remove: React.useMemo(
      () => dashboardFeeds.filter(({ feedType }) => feedType === 'remove'),
      [dashboardFeeds]
    ),
  }[activeTab];

  const hasOnlySnappy = React.useMemo(
    () => feedsByFeedType.filter(({ fastTrack }) => !fastTrack).length === 0,
    [feedsByFeedType]
  );

  const feeds = React.useMemo(
    () =>
      searchValue.length > 0
        ? feedsByFeedType.filter(({ profileName }) =>
            profileName.toLowerCase().includes(searchValue.toLowerCase())
          )
        : feedsByFeedType,
    [feedsByFeedType, searchValue]
  );
  React.useEffect(() => {
    if (Number(currentStore.id) >= 74650) {
      Crisp.session.pushEvent('email-campaign');
      posthog.capture('inventory_update', {
        store_id: currentStore.id,
        sku_count: currentStore.totalSkusCount,
        total_run: currentStore.totalProcess,
      });
    }
  }, [currentStore]);

  return (
    <div>
      <Tab.Context value={activeTab}>
        <CustomAppBar
          ref={ref}
          title={t('dashboard')}
          iconProps={{ type: 'kit', className: 'fak fa-home2' }}
          btnGroup={
            isEmbeddedApp && (
              <Button
                size="small"
                variant="contained"
                onClick={() => navigate(routes.newFeed)}
                sx={{ maxHeight: '40px' }}
              >
                {t('create_new_feed')}
              </Button>
            )
          }
        />
        {isEmbeddedApp ? (
          <Card sx={{ marginTop: '32px', padding: '16px', maxHeight: '80px' }}>
            <Stack
              direction="row"
              sx={{
                width: '100%',
                height: '60px',
                overflowX: 'auto',
                overflowY: 'hidden',
              }}
            >
              <Stack sx={{ minWidth: '180px', width: '100%' }}>
                <Typography
                  variant="subtitle1"
                  sx={{ color: theme.palette.grey[300] }}
                >
                  {t('store_name')}
                </Typography>
                <Link
                  to={
                    renderAdminLink(
                      {
                        shopify: shortenShopifyStore(
                          currentStore.shopifyDomain
                        ),
                        wix: currentStore.shopifyDomain,
                        squarespace: currentStore.shopifyDomain,
                        woocommerce: currentStore.shopifyDomain,
                        bigcommerce: `store-${currentStore.bigcommerceStoreHash}.mybigcommerce.com`,
                        ekm: currentStore.shopifyDomain,
                        square: currentStore.shopifyDomain,
                      }[currentStore.provider],
                      currentStore.provider
                    ) ?? ''
                  }
                  underline="always"
                  sx={{
                    fontSize: '16px',
                  }}
                >
                  {shortenShopifyStore(currentStore.shopifyDomain)}
                  &nbsp;
                  <Icon
                    type="default"
                    fontSize={12}
                    icon={faArrowUpRightFromSquare}
                  />
                </Link>
              </Stack>
              <Stack sx={{ minWidth: '180px', width: '100%' }}>
                <Typography
                  variant="subtitle1"
                  sx={{ color: theme.palette.grey[300] }}
                >
                  {t('current_plan')}
                </Typography>
                <>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {currentStore.currentPlan.isCustomPlan
                      ? t('custom_plan')
                      : `${currentStore.package} Plan `}
                    {currentStore.package === 'Trial' &&
                      `-${currentStore.fullPlanName}`}
                  </Typography>
                  &nbsp;
                  <Typography
                    variant="h6"
                    sx={{ color: theme.palette.grey[300] }}
                  >
                    {isLegacyPricingPlan && t('legacy')}
                  </Typography>
                </>
              </Stack>
              <Stack sx={{ minWidth: '200px' }}>
                <Typography
                  variant="subtitle1"
                  sx={{ color: theme.palette.grey[300] }}
                >
                  {t('total_number_of_variants')}
                </Typography>
                <Typography variant="h6">
                  {' '}
                  {numberFormat(currentStore.totalSkusCount)} /{' '}
                  {numberFormat(currentStore.limitSkus)}
                  <span style={{ fontSize: '12px' }}> (max)</span>
                </Typography>
              </Stack>
              {currentStore.monthlyCreditPlan && (
                <Stack sx={{ overflowX: 'auto', minWidth: '150px' }}>
                  <Typography
                    variant="body1"
                    sx={{ color: theme.palette.grey[300] }}
                  >
                    {t('stock_sync_monthly_credits')}
                  </Typography>
                  <Typography variant="h6">
                    {' '}
                    {numberFormat(Number(currentStore.monthlyCredit))}
                  </Typography>
                </Stack>
              )}
              <Stack sx={{ minWidth: '150px' }}>
                <Typography
                  variant="subtitle1"
                  sx={{ color: theme.palette.grey[300] }}
                >
                  {t('stock_sync_credit')}
                </Typography>
                <Typography variant="h6">
                  {' '}
                  {numberFormat(currentStore.importLimitSkus)}
                </Typography>
              </Stack>
              <Divider
                orientation="vertical"
                variant="middle"
                flexItem
                sx={{ margin: '0px 16px 8px' }}
              />
              <Stack sx={{ minWidth: '150px', margin: '16px 0px 0px 16px' }}>
                <Box>
                  <Link to={routes.billing} sx={{ fontSize: '14px' }}>
                    <Icon
                      type="default"
                      fontSize={14}
                      icon={faFileInvoiceDollar}
                    />
                    &nbsp; {t('view_billing')}
                  </Link>
                </Box>
              </Stack>
            </Stack>
          </Card>
        ) : (
          <></>
        )}

        <Box sx={{ marginTop: '32px' }} />
        <SearchBarAndTabs
          setSearchValue={setSearchValue}
          setActiveTab={setActiveTab}
        />
        {hasOnlySnappy && activeTab !== 'snappy' ? (
          <EmptyDashboard />
        ) : feeds.length === 0 ? (
          <FeedsNotFound />
        ) : (
          <DashboardTable feeds={feeds} />
        )}

        {isEmbeddedApp ? (
          <Box
            sx={{
              maxWidth: 'fit-content',
              margin: '32px auto 0',
            }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{
                justifyContent: 'space-evenly',
                alignItems: 'center',
              }}
            >
              <Box
                sx={{
                  border: '1px solid #8898aa33',
                  borderRadius: '24px',
                  padding: '8px 16px',
                }}
              >
                <Stack
                  direction="row"
                  spacing={1}
                  sx={{
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                  }}
                >
                  <Icon
                    type="default"
                    fontSize={16}
                    icon={faCircleQuestion}
                    style={{ color: theme.palette.link }}
                  />
                  <Typography
                    variant="body1"
                    sx={{ color: theme.palette.grey[300] }}
                  >
                    Learn more about syncX: Stock Sync or click{' '}
                    <Link to="https://help.stock-sync.com/support/home">
                      Help center
                    </Link>
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Box>
        ) : (
          <></>
        )}
      </Tab.Context>
    </div>
  );
}

// TODO: type props
function SearchBarAndTabs({ setSearchValue, setActiveTab }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const feedsQuery = useDashboardFeeds();
  const { fetching, stale, refetch } = feedsQuery;
  const { control, watch, resetField } = useForm({
    defaultValues: { searchName: '' },
  });

  React.useEffect(() => {
    // watch also accepts a callback, doesn't rerender
    const subscription = watch(
      debounce(({ searchName }) => setSearchValue(searchName), 300)
    );
    return () => subscription.unsubscribe(); // unsubscribe when done
  }, [watch, setSearchValue]);

  const [openSearch, setOpenSearch] = React.useState(false);
  const toggleSearch = () => setOpenSearch((prev) => !prev);
  const closeSearchBar = () => {
    toggleSearch();
    resetField('searchName');
  };

  const tabsFeedType =
    currentStore.provider === 'shopify' ? tabs : otherPlatformTabs;

  return (
    <Box
      sx={{
        height: '66px', // avoid layout shift when toggle showSearchbar
        display: 'flex',
        alignItems: 'center',
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        background: theme.palette.common.white,
      }}
    >
      {openSearch ? (
        <TextField
          control={control}
          name="searchName"
          autoFocus
          label={t('search')}
          size="small"
          onKeyDownCapture={(e) => {
            if (e.key === 'Escape') closeSearchBar();
          }}
          slotProps={{
            input: {
              endAdornment: (
                <IconButton
                  disableRipple
                  onClick={() => closeSearchBar()}
                  sx={{ color: theme.palette.grey[300] }}
                  id="close-search-feed"
                >
                  <Icon type="default" icon={faCircleXmark} />
                </IconButton>
              ),
            },
          }}
          sx={{ margin: '0 16px' }}
        />
      ) : (
        <Stack
          direction="row"
          spacing={{ xs: '8px', sm: '16px' }}
          sx={{
            alignItems: 'center',
            width: '100%',
            paddingLeft: { xs: 0, sm: '16px' },
          }}
        >
          <IconButton
            onClick={toggleSearch}
            sx={{ background: '#8898aa33', color: theme.palette.grey[300] }}
            id="on-search-feed"
          >
            <Icon type="default" fontSize={18} icon={faMagnifyingGlass} />
          </IconButton>
          <Tab.List onChange={(_, value) => setActiveTab(value)}>
            {tabsFeedType.map((tab) => (
              <Tab key={tab.name} label={t(tab.name)} value={tab.feedType} />
            ))}
          </Tab.List>

          <IconButton
            disableRipple
            disabled={fetching || stale}
            onClick={() => refetch()}
            sx={{ display: 'block' }}
            id="dashbord-refresh"
          >
            {fetching || stale ? (
              <CircularProgress size={18} />
            ) : (
              <Icon type="default" icon={faArrowRotateRight} fontSize={18} />
            )}
          </IconButton>
        </Stack>
      )}
    </Box>
  );
}

interface DashboardTableProps {
  feeds: DashboardLiveFeed[];
}

function DashboardTable({ feeds }: DashboardTableProps) {
  const theme = useTheme();
  const [expandedRows, setExpandedRows] = React.useState(
    new Set<string>(new Set([]))
  );
  const [order, setOrder] = React.useState<'asc' | 'desc'>('desc');
  const [sortBy, setSortBy] = React.useState('');
  const mdAndDown = useMatches('md');

  const onExpandRow = (feedId) => {
    setExpandedRows((prev) => {
      // multiple rows
      if (prev.has(feedId)) {
        prev.delete(feedId);
        return new Set([...prev]);
      } else {
        return new Set([...prev, feedId]);
      }
    });
  };
  const isExpandedRow = (feed: DashboardLiveFeed) => expandedRows.has(feed.id);

  const handleRequestSort = (_, property) => {
    const isAsc = sortBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setSortBy(property);
  };

  const createSortHandler = (property) => (event) => {
    handleRequestSort(event, property);
  };

  const sortedRows = React.useMemo(
    () => stableSort(feeds, getComparator(order, sortBy)),
    [order, sortBy, feeds]
  );

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '998px',
        overflowX: mdAndDown ? 'scroll' : 'unset',
      }}
    >
      <DisplayErrorMessage />
      <Table
        sx={{
          borderColor: theme.palette.grey[400],
          tableLayout: 'fixed',
          overflowX: 'scroll',
          minWidth: '700px',
        }}
      >
        <Table.Head>
          <Table.Row>
            {[
              {
                id: 'settingSummary',
                width: '75px',
              },
              {
                id: 'profileName',
                label: 'Feed Name',
                width: '300px',
              },
              {
                id: 'sourceType',
                label: 'Connection',
              },
              {
                id: 'lastRunAtUnix',
                label: 'Status',
              },
              {
                id: 'humanizedJobType',
                label: 'Schedule',
              },
              {
                id: 'actions',
                width: '75px',
              },
            ].map((headCell) => (
              <Table.Cell
                width={headCell.width}
                key={headCell.id}
                sortDirection={sortBy === headCell.id ? order : false}
                sx={{
                  fontSize: theme.typography.body1.fontSize,
                  color: theme.palette.grey[300],
                  borderWidth: 0,
                  borderBottomWidth: '1px',
                  background: theme.palette.common.white,
                  fontWeight: 400,
                  padding: '32px 16px 16px 16px',
                }}
              >
                <Table.SortLabel
                  active={sortBy === headCell.id}
                  direction={sortBy === headCell.id ? order : 'desc'}
                  onClick={createSortHandler(headCell.id)}
                >
                  {headCell.label}
                </Table.SortLabel>
              </Table.Cell>
            ))}
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {sortedRows.map((feed, index) => (
            <React.Fragment key={index}>
              <Table.Row
                sx={{
                  backgroundColor: isExpandedRow(feed) ? '#6E6D7A0D' : 'white',
                }}
              >
                <FeedTableRow
                  feed={feed}
                  onExpand={onExpandRow}
                  isExpanded={isExpandedRow(feed)}
                />
              </Table.Row>
              <Table.Row>
                <ExpandedFeedRow
                  setStatus={feed.setStatus}
                  key={feed.id}
                  feedId={feed.id}
                  expanded={isExpandedRow(feed)}
                />
              </Table.Row>
            </React.Fragment>
          ))}
        </Table.Body>
      </Table>
    </Box>
  );
}

const DisplayErrorMessage = () => {
  const alert = useAlert();
  return alert.message.length > 0 && <Alert>{alert.message}</Alert>;
};

const EmptyDashboard = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Card
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '40px',
        textAlign: 'center',
      }}
    >
      <img src={noDashboardImage} style={{ width: '20%' }} />
      <Box sx={{ paddingBottom: '20px' }} />
      <Typography variant="h4">{t('empty_dashboard_title')}</Typography>
      <Typography variant="caption" sx={{ marginTop: 3 }}>
        {t('empty_dashboard_helptext')}
      </Typography>
      <Button
        variant="contained"
        size="small"
        sx={{ marginTop: 5 }}
        onClick={() => navigate(routes.newFeed)}
      >
        {t('create_new')}
      </Button>
    </Card>
  );
};

function FeedsNotFound() {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Grid
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
      sx={{ bgcolor: theme.palette.primary.contrastText, pb: 5 }}
    >
      <img src={noSearchResult} style={{ width: '20%', marginTop: '16px' }} />
      <Typography variant="h4">{t('no_connections_found')}</Typography>
      <Typography variant="body2" sx={{ mt: 3 }}>
        {t('no_connections_found_helptext')}
      </Typography>
    </Grid>
  );
}

function descendingComparator(a, b, sortBy) {
  if (b[sortBy] < a[sortBy]) {
    return -1;
  }
  if (b[sortBy] > a[sortBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, sortBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, sortBy)
    : (a, b) => -descendingComparator(a, b, sortBy);
}

function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });

  return stabilizedThis.map((el) => el[0]);
}
