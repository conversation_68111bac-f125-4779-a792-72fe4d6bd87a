import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Button } from '@/components/Button';
import { Dialog } from '@/components/Dialog';
import { TextField } from '@/components/TextField';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import type { DashboardLiveFeed } from '@/hooks/useDashboardFeeds';
import { useMutation } from '@/hooks/useMutation';
import { DeleteFeed } from '@/queries/FeedSettings';
import { CreateTicket } from '@/queries/StoreMutations';
import * as routes from '@/routes';
import { storeSchema } from '@/types';

interface DeleteConfirmationDialogProps {
  setOpenDeleteConfirm: (state: boolean) => void;
  openDeleteConfirm: boolean;
  feed: DashboardLiveFeed;
}

const formSchema = z.object({
  message: z.string().min(1, 'Message is Required'),
  provider: storeSchema.shape.provider,
  reachEmail: storeSchema.shape.reachEmail,
});

type FormValues = z.infer<typeof formSchema>;

export const DeleteConfirmationDialog = ({
  openDeleteConfirm,
  setOpenDeleteConfirm,
  feed,
}: DeleteConfirmationDialogProps) => {
  const { t } = useTranslation();
  const { currentStore } = useCurrentStore();
  const [, deleteFeed] = useMutation(DeleteFeed, {
    dataKey: 'deleteFeed',
  });
  const [, createTicket] = useMutation(CreateTicket, {
    dataKey: 'createTicket',
  });

  const { control, handleSubmit, formState } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    values: {
      message: '',
      provider: currentStore.provider,
      reachEmail: currentStore.reachEmail,
    },
  });

  const alert = useAlert();

  return (
    <Dialog
      open={openDeleteConfirm}
      onClose={() => setOpenDeleteConfirm(false)}
      maxWidth="xs"
    >
      <Dialog.Title>{t('having_trouble_setting_up')}</Dialog.Title>
      <Dialog.Content>
        {feed.feedType === 'update' && (
          <>
            <div>{t('let_us_know_we_ready_to_help')}</div>
            <Stack
              sx={{
                alignItems: 'flex-end',
                marginTop: '16px',
              }}
            >
              <TextField
                name="message"
                control={control}
                fullWidth
                multiline
                label="Message"
                rows={5}
              />
              <Button
                variant="text"
                loading={formState.isSubmitting}
                onClick={handleSubmit(async (values) => {
                  await createTicket(
                    {
                      attributes: {
                        email: values.reachEmail,
                        title: `Delete feed: Feed ID ${feed.id}`,
                        message: values.message,
                        platform: values.provider,
                      },
                    },
                    {
                      onSuccess({ enqueueSnackbar }) {
                        enqueueSnackbar(`Ticket created`, {
                          variant: 'success',
                        });
                      },
                      onError({ error }) {
                        alert.setMessage(t(error));
                      },
                    }
                  ).finally(() => setOpenDeleteConfirm(false));
                })}
                sx={{ marginTop: '8px' }}
              >
                {t('send_message_for_help')}
              </Button>
            </Stack>
          </>
        )}
        {feed.feedType === 'import' && (
          <>
            Deleting feed does not revert changes made. Please make sure to{' '}
            <strong>UNDO</strong>, to revert before delete feed.
          </>
        )}
      </Dialog.Content>
      <Dialog.Actions>
        <Button
          variant="outlined"
          size="small"
          onClick={() => setOpenDeleteConfirm(false)}
        >
          {t('cancel')}
        </Button>
        <Button
          variant="contained"
          size="small"
          onClick={() => {
            setOpenDeleteConfirm(false);
            deleteFeed(
              { feedId: +feed.id },
              {
                onSuccess({ data: feed, enqueueSnackbar, navigate }) {
                  enqueueSnackbar(`Feed ${feed.profileName} deleted`, {
                    variant: 'success',
                  });
                  navigate(routes.home);
                },
                onError({ error }) {
                  alert.setMessage(t(error));
                },
              }
            );
          }}
        >
          {t('delete_anyway')}
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
};
