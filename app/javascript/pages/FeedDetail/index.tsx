import {
  faChevronDown,
  faChevronUp,
  faCircleInfo,
  faGear,
} from '@fortawesome/pro-light-svg-icons';
import {
  Box,
  Alert as MuiAlert,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import { useState } from 'react';
import Confetti from 'react-confetti';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { match } from 'ts-pattern';

import { useIsAdmin } from '@/components/Admin';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CustomAppBar } from '@/components/CustomAppBar';
import { Dialog } from '@/components/Dialog';
import { FeedNameAndType } from '@/components/FeedNameAndType';
import { FeedSettingSummary } from '@/components/FeedSettingSummary';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { Loading } from '@/components/Loading';
import { Table } from '@/components/Table';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDetailsFeed } from '@/hooks/useDetailsFeed';
import { useFeedRenderProps } from '@/hooks/useFeedRenderProps';
import { useMatches } from '@/hooks/useMatches';
import { useOnboardingStep } from '@/hooks/useOnboardingStep';
import { useWindowSize } from '@/hooks/useWindowSize';
import * as routes from '@/routes';
import { formatDate } from '@/shared/util';

import { AdminSettings } from './admin/AdminSettings';
import { ActivityLogs } from './components/ActivityLogs';
import { FileUploadSyncState } from './components/FileUploadSyncState';
import { SnappyBulkUpdate } from './components/SnappyBulkUpdate';
import { Summary } from './components/Summary';
import { useFeedActivityLogs } from './useFeedActivityLogs';

export function FeedDetail() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isAdmin } = useIsAdmin();
  const [open, setOpen] = useState(false);
  const [showSampleActivityLogs, setShowSampleActivityLogs] = useState(false);
  const [showAllDetail, setShowAllDetail] = useState(false);
  const { feed } = useDetailsFeed();
  const { currentStore, refetch } = useCurrentStore();
  const step = useOnboardingStep();
  const [showConfetti, setShowConfetti] = useState(false);

  console.log('step detail page', step);

  const lastEmailCreatedAt = feed.lastEmailCreatedAt;
  const formatLastEmailCreatedAt = lastEmailCreatedAt
    ? formatDate(lastEmailCreatedAt, currentStore.timezone).format(
        'MMM D, YYYY h:mm A'
      )
    : '';

  const matchesSm = useMatches('sm');
  const matchesMd = useMatches('md');
  const navigate = useNavigate();
  const { size } = useWindowSize();

  const feedRenderProps = useFeedRenderProps({
    humanizeFeedType: feed.humanizeFeedType,
  });
  const [logsQuery] = useFeedActivityLogs({ feed });
  const activityLogs = React.useMemo(() => {
    return logsQuery.data?.getActivityLogs?.logs ?? [];
  }, [logsQuery.data]);

  const totalSyncField =
    (feed.syncFieldSettings || []).length +
    [feed.productIdentifierSyncField].length;

  const filterOutFalseValueFromFilter = React.useMemo(
    () =>
      feed.storeFilters.filter(
        (a) => typeof a.conditions[0].value !== 'boolean'
      ),
    [feed.storeFilters]
  );

  const feedTypeValue = {
    update: feed.feedFilters.length + filterOutFalseValueFromFilter.length,
    import: feed.feedFilters.length,
    export: feed.feedFilters.length + filterOutFalseValueFromFilter.length,
    remove: feed.feedFilters.length + filterOutFalseValueFromFilter.length,
  }[feed.humanizeFeedType];

  const totalAdvancedSettingSetupCount = {
    shopify: {
      remove: [
        feed.updateDuplicateProductKey,
        feed.removeProductWhenAllLocationsNil,
        feed.deleteMode === 1 ? true : false,
      ].filter((value) => value).length,
      update: [
        feed.hideUnmatchProducts,
        feed.autoResetQuantity,
        feed.publishedApplyToAll,
        feed.publishedApplyMatchingProducts,
      ].filter((value) => value).length,
      import: [feed.publishedApplyToAll, feed.importTags !== ''].filter(
        (value) => value
      ).length,
      export: 0,
    },
    wix: {
      remove: 0,
      update: [
        feed.hideUnmatchProducts,
        feed.autoResetQuantity,
        feed.publishedApplyToAll,
        feed.publishedApplyMatchingProducts,
      ].filter((value) => value).length,
      import: [feed.publishedApplyToAll].filter((value) => value).length,
      export: 0,
    },
    squarespace: {
      remove: 0,
      update: 0,
      import: 0,
      export: 0,
    },
    quickbooks: {
      remove: 0,
      update: 0,
      import: 0,
      export: 0,
    },
    bigcommerce: {
      remove: [feed.deleteMode === 1 ? true : false].filter((value) => value)
        .length,
      update: [
        feed.bigcommerceRetainAvailability,
        feed.updateDuplicateProductKey,
        feed.updateProductLevel,
        feed.hideUnmatchProducts,
        feed.publishedApplyToAll,
        feed.publishedApplyMatchingProducts,
      ].filter((value) => value).length,
      import: [feed.publishedApplyToAll, feed.importTags !== ''].filter(
        (value) => value
      ).length,
      export: 0,
    },
    woocommerce: {
      remove: [feed.deleteMode === 1 ? true : false].filter((value) => value)
        .length,
      update: [
        feed.hideUnmatchProducts,
        feed.publishedApplyToAll,
        feed.publishedApplyMatchingProducts,
      ].filter((value) => value).length,
      import: [feed.publishedApplyToAll, feed.importTags !== ''].filter(
        (value) => value
      ).length,
      export: 0,
    },
    ekm: {
      remove: 0,
      update: [
        feed.hideUnmatchProducts,
        feed.publishedApplyToAll,
        feed.publishedApplyMatchingProducts,
      ].filter((value) => value).length,
      import: 0,
      export: 0,
    },
    square: {
      remove: 0,
      update: 0,
      import: 0,
      export: 0,
    },
    prestashop: {
      remove: 0,
      update: 0,
      import: 0,
      export: 0,
    },
  };

  React.useEffect(() => {
    const successLog = activityLogs.find((al) => al.status === true);
    if (!successLog) return;
    setShowConfetti(true);
  }, [activityLogs]);

  return (
    <>
      <CustomAppBar
        title={t('feed_details')}
        redirectUrl={routes.home}
        sx={{ marginBottom: 0 }}
        btnGroup={
          feed.profileName !== 'Snappy' && (
            <Stack direction="row" spacing="18px">
              {isAdmin && <AdminSettings />}
              <Button
                size="small"
                variant="contained"
                startIcon={!matchesSm && <Icon type="default" icon={faGear} />}
                onClick={() => navigate(routes.feedManagerRoute(feed.id))}
              >
                {matchesSm ? (
                  <Icon type="default" icon={faGear} />
                ) : (
                  t('feed_settings')
                )}
              </Button>
            </Stack>
          )
        }
      />
      <FeedNameAndType
        feedRenderProps={feedRenderProps}
        feedId={feed.id}
        feedName={feed.profileName}
      />

      {currentStore.isShowQuickGuide &&
      (step.currentStep === 2 || step.currentStep === 3) ? (
        <Box>
          <Card sx={{ padding: '16px', maxWidth: '500px' }}>
            <Stack
              direction={matchesSm ? 'column' : 'row'}
              spacing={2}
              sx={{
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              {match({
                step: step.currentStep,
              })
                .with({ step: 2 }, () => (
                  <>
                    <Typography
                      variant="h6"
                      sx={{ width: '100%', fontWeight: 600 }}
                    >
                      {t('up_next')} {t('manage_feed')}
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => navigate(routes.feedManagerRoute(feed.id))}
                    >
                      {t('resume_guide')}
                    </Button>
                  </>
                ))
                .with({ step: 3 }, () => (
                  <>
                    <Typography
                      variant="h6"
                      sx={{ width: '100%', fontWeight: 600 }}
                    >
                      {t('up_next')} {t('update_quantity_or_price')}
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => navigate(routes.mappingRoute(feed.id))}
                    >
                      {t('resume_guide')}
                    </Button>
                  </>
                ))
                .otherwise(() => (
                  <></>
                ))}
            </Stack>
          </Card>
        </Box>
      ) : (
        <></>
      )}
      {currentStore.isShowQuickGuide && showConfetti && (
        <>
          <Confetti width={size.width} height={size.height} />
          <Dialog
            open={showConfetti}
            maxWidth="xs"
            onClose={() => {
              refetch();
              setShowConfetti(false);
            }}
          >
            <Dialog.Content>{t('feed_created')}</Dialog.Content>
            <Dialog.Actions>
              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  refetch();
                  navigate(routes.newFeed);
                  setShowConfetti(false);
                }}
              >
                {t('go_to_create_new_feed')}
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  refetch();
                  navigate(routes.home);
                  setShowConfetti(false);
                }}
              >
                {t('back_dashboard')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </>
      )}

      {feed.fastTrack && <SnappyBulkUpdate />}
      {!feed.fastTrack && (
        <>
          <Typography
            variant="h5"
            sx={{
              margin: '0 0 16px 0px',
              paddingTop: '16px',
              fontWeight: 600,
            }}
          >
            {t('overview_last_process')}
          </Typography>
          <Table
            sx={{
              minWidth: 700,
              overflow: 'hidden',
              borderRadius: '8px',
            }}
          >
            <Table.Head>
              <Table.Row>
                <Table.Cell
                  width={matchesMd ? '280px' : 'auto'}
                  sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    borderBottom: 'none',
                  }}
                >
                  <Box sx={{ display: 'flex' }}>
                    <Icon
                      type="default"
                      onClick={() => {
                        setOpen(!open);
                        setShowAllDetail(!showAllDetail);
                      }}
                      icon={open ? faChevronUp : faChevronDown}
                      style={{
                        cursor: 'pointer',
                        color: theme.palette.link,
                        transform: 'translateY(2px)',
                        marginLeft: '8px',
                        marginTop: '2px',
                      }}
                    />
                    <Box sx={{ padding: '0px 8px' }}>
                      {t('matching_column')} ({totalSyncField})
                    </Box>
                    <LinkToSetting
                      linkTo={routes.mappingRoute(feed.id)}
                      identifier="feed-detail-to-mapping-page"
                    />
                  </Box>
                </Table.Cell>
                <Table.Cell
                  width={matchesMd ? '170px' : 'auto'}
                  sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    borderBottom: 'none',
                  }}
                >
                  <Box sx={{ display: 'flex' }}>
                    <Box sx={{ padding: '0px 8px' }}>
                      {t('filter')} ({feedTypeValue})
                    </Box>
                    <LinkToSetting
                      linkTo={routes.filterRoute(feed.id)}
                      identifier="feed-detail-to-filter-page"
                    />
                  </Box>
                </Table.Cell>
                <Table.Cell
                  sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    borderBottom: 'none',
                  }}
                >
                  <Box sx={{ display: 'flex' }}>
                    <Box sx={{ padding: '0px 8px' }}>
                      {t('advanced_settings')} (
                      {
                        totalAdvancedSettingSetupCount[currentStore.provider][
                          feed.humanizeFeedType
                        ]
                      }
                      )
                    </Box>
                    <LinkToSetting
                      linkTo={routes.advancedSettingsRoute(feed.id)}
                      identifier="feed-detail-to-advance-setting-page"
                    />
                  </Box>
                </Table.Cell>
              </Table.Row>
            </Table.Head>
            {open && (
              <Table.Body>
                <FeedSettingSummary
                  feedId={feed.id}
                  showAllDetail={showAllDetail}
                />
              </Table.Body>
            )}
          </Table>
          <Box sx={{ marginBottom: '20px' }} />
        </>
      )}

      <Grid container spacing="16px">
        {/* Left column */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Summary
            syncState={
              !['email_in', 'email_link'].includes(feed.combinedSourceType) ? (
                <Card sx={{ boxShadow: 'none', padding: '0 24px' }}>
                  <FileUploadSyncState />
                </Card>
              ) : ['email_in', 'email_link'].includes(
                  feed.combinedSourceType
                ) ? (
                <Box sx={{ margin: '24px 0px' }}>
                  {lastEmailCreatedAt === null &&
                  formatLastEmailCreatedAt === '' ? (
                    <MuiAlert
                      icon={
                        <Icon
                          type="default"
                          icon={faCircleInfo}
                          style={{ fontSize: '18px' }}
                        />
                      }
                      severity="warning"
                      sx={{ fontSize: '14px' }}
                    >
                      {t('please_send_email')}
                    </MuiAlert>
                  ) : (
                    <Card
                      sx={{
                        backgroundColor: '#0C7FE80F',
                        padding: '16px',
                        boxShadow: 0,
                      }}
                    >
                      <Stack
                        direction="row"
                        spacing="8px"
                        sx={{
                          alignItems: 'flex-start',
                        }}
                      >
                        <Box sx={{ paddingRight: '8px' }}>
                          <Icon
                            type="default"
                            icon={faCircleInfo}
                            style={{ color: '#00527C', fontSize: '16px' }}
                          />
                        </Box>
                        <Typography variant="body1" sx={{ color: '#00527C' }}>
                          Last email received on &nbsp;
                          <strong>{formatLastEmailCreatedAt}</strong>
                        </Typography>
                        <br />
                      </Stack>
                      <FileUploadSyncState />
                    </Card>
                  )}
                </Box>
              ) : (
                <></>
              )
            }
          />
        </Grid>
        {/* Right column */}
        <Grid size={{ xs: 12, md: 6 }}>
          <React.Suspense
            fallback={
              <>
                <Card sx={{ padding: '24px' }}>
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: 600, paddingBottom: '8px' }}
                  >
                    {t('activity_logs')}
                  </Typography>
                  <Loading sx={{ maxHeight: '33vh' }} />
                </Card>
              </>
            }
          >
            <ActivityLogs
              activityLogs={activityLogs}
              showSampleActivityLogs={showSampleActivityLogs}
              setShowSampleActivityLogs={setShowSampleActivityLogs}
            />
          </React.Suspense>
        </Grid>
      </Grid>
    </>
  );
}

// TODO: type props
function LinkToSetting({ linkTo, identifier }) {
  const { t } = useTranslation();

  return (
    <Link sx={{ marginLeft: '4px' }} to={linkTo} id={identifier}>
      {t('edit')}
    </Link>
  );
}
