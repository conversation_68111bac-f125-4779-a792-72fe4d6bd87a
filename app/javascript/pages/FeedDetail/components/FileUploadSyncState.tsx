import {
  faCircle,
  faCircleInfo,
  faCircleNotch,
  faCircleQuestion,
  faXmark,
} from '@fortawesome/pro-light-svg-icons';
import {
  AlertTitle,
  Box,
  Chip,
  Alert as MuiAlert,
  Stack,
  Typography,
  useTheme,
  type SxProps,
} from '@mui/material';
import posthog from 'posthog-js';
import * as React from 'react';
import { useForm, type UseFormReturn } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { match } from 'ts-pattern';

import { Button, type ButtonProps } from '@/components/Button';
import { Checkbox } from '@/components/Checkbox';
import { Dialog } from '@/components/Dialog';
import { FileUploader } from '@/components/FileUploader';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { LinearProgress } from '@/components/LinearProgress';
import { Link } from '@/components/Link';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDetailsFeed } from '@/hooks/useDetailsFeed';
import { useIntroJs } from '@/hooks/useIntroJs';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import {
  CancelProcess,
  ExecuteFeed,
  RunNowSample,
  UpdatePreviewMode,
} from '@/queries/FeedSettings';
import * as routes from '@/routes';
import { previewSync } from '@/shared/images';
import { numberFormat } from '@/shared/util';

interface FormValues {
  processNow: boolean;
  runAsSample: boolean;
  previewSync: boolean;
  forceStart: boolean;
}

export function FileUploadSyncState() {
  const { t } = useTranslation();
  const theme = useTheme();
  const smAndDown = useMatches('sm');
  const { feed } = useDetailsFeed();
  const { currentStore } = useCurrentStore();
  const [previewSyncDialog, setPreviewSyncDialog] = React.useState(false);

  const { control, watch, setValue } = useForm<FormValues>({
    values: {
      processNow: true,
      runAsSample:
        currentStore.importLimitSkus === 0 && feed.humanizeFeedType === 'import'
          ? true
          : false,
      previewSync: feed.preview ?? false,
      forceStart: false,
    },
  });
  useIntroJs({
    key: 'intro_feed_details',
    enabled: currentStore.isShowQuickGuide,
  });

  return (
    <>
      <Box sx={{ margin: '24px 0px' }}>
        {!feed.running && feed.sourceType === 'uploaded_file' && (
          <>
            <Stack
              direction="column"
              spacing={1}
              sx={{
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <FileUploader
                feed={feed}
                processNow={watch('processNow')}
                runAsSample={watch('runAsSample')}
                showHelptext={false}
                onUploadSuccess={({ status }) => feed.setStatus(status)}
                onUploadAccepted={({ status }) => feed.setStatus(status)}
                onUploadError={({ status }) => feed.setStatus(status)}
              />
              <Checkbox
                control={control}
                name="processNow"
                label={t('process_after_upload')}
              />
            </Stack>
          </>
        )}
      </Box>
      {feed.feedType === 'import' && !feed.running && (
        <>
          <Box
            sx={{
              background: '#6e6d7a0d',
              padding: '16px',
              borderRadius: '8px',
              width: '100%',
              marginBottom: '16px',
            }}
          >
            <Box>
              <Typography variant="h6">
                {currentStore.monthlyCreditPlan && (
                  <Box sx={{ marginBottom: '16px' }}>
                    <Typography
                      component="span"
                      variant="body1"
                      sx={{ color: theme.palette.grey[300] }}
                    >
                      {t('total_monthly_credits')}
                    </Typography>
                    <Typography variant="h6">
                      {numberFormat(Number(currentStore.monthlyCredit))}
                    </Typography>{' '}
                  </Box>
                )}
                <Box>
                  <Typography
                    component="span"
                    variant="body2"
                    sx={{ color: theme.palette.grey[300] }}
                  >
                    {t('credit_remaining_create_feed')}
                  </Typography>
                  <Stack
                    direction="row"
                    spacing={1}
                    sx={{
                      justifyContent: 'center',
                      alignItems: 'flex-start',
                      marginTop: '8px',
                    }}
                  >
                    <Stack direction="row" spacing={1}>
                      <Typography variant="h5">
                        {numberFormat(currentStore.importLimitSkus)}{' '}
                      </Typography>
                      {currentStore.emailSubscriptions.includes(
                        'low_credit_alert'
                      ) ||
                        (currentStore.importLimitSkus <= 10 && (
                          <Chip
                            variant="outlined"
                            color="warning"
                            size="small"
                            label={t('low_credit')}
                            sx={{
                              marginLeft: '8px',
                              borderRadius: '8px',
                              background: theme.palette.yellow[50],
                              borderColor: theme.palette.yellow[100],
                              '.MuiChip-label': { color: 'black' },
                            }}
                          />
                        ))}
                    </Stack>

                    <Link
                      underline="always"
                      to={routes.billingBuyCredit}
                      sx={{ width: '100%' }}
                    >
                      {t('buy_here')}
                    </Link>
                  </Stack>{' '}
                </Box>
              </Typography>
            </Box>
          </Box>
          <Stack
            direction="row"
            spacing="5px"
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: '32px',
            }}
          >
            <Link
              sx={{ marginTop: '1px' }}
              to="https://help.stock-sync.com/en/article/does-adding-products-create-duplicate-products-in-the-store-3ex4ku/"
            >
              <Icon type="default" icon={faCircleQuestion} />
            </Link>
            <Typography variant="body1">
              {t('add_without_create_product')}
            </Typography>
          </Stack>
        </>
      )}

      {(['email_in', 'email_link'].includes(feed.combinedSourceType) ||
        feed.running) && (
        <Box sx={{ marginBottom: '16px' }}>
          <SyncState />
        </Box>
      )}
      <Stack
        direction="column"
        spacing={1}
        sx={{
          justifyContent: 'center',
          alignItems: smAndDown ? 'unset' : 'center',
        }}
      >
        <Box>
          {!['email_in', 'email_link'].includes(feed.combinedSourceType) ? (
            <>
              {feed.running ? (
                <StopProcessButton />
              ) : feed.humanizeFeedType !== 'export' && !feed.running ? (
                <>
                  <div data-hint="Click here to begin processing your product feed. The system will import and update your products according to your settings.">
                    <StartProcessButton
                      variant="contained"
                      runAsSample={watch('runAsSample')}
                      watch={watch}
                      setFormValue={() => setValue('previewSync', false)}
                      disable={!feed.enabled}
                      sx={{
                        width: '100%',
                      }}
                    >
                      {
                        {
                          update: t('process_now'),
                          import: t('add_products'),
                          remove: t('process_now'),
                        }[feed.feedType ?? '']
                      }
                    </StartProcessButton>
                  </div>
                  {feed.feedType === 'import' && (
                    <Checkbox
                      control={control}
                      name="runAsSample"
                      label={
                        <Typography component="span">
                          {t('import_three_products')}
                        </Typography>
                      }
                      disabled={feed.running || !feed.enabled}
                    />
                  )}
                  {currentStore.provider === 'shopify' && (
                    <Stack
                      direction={smAndDown ? 'column' : 'row'}
                      spacing={2}
                      sx={{
                        justifyContent: 'center',
                        paddingTop: '16px',
                      }}
                    >
                      <div data-hint="Preview what changes will be made before running the full update. This helps you verify your settings are correct.">
                        <StartProcessButton
                          variant="outlined"
                          runAsSample={watch('runAsSample')}
                          watch={watch}
                          setFormValue={() => setValue('previewSync', true)}
                          disable={!feed.enabled || feed.schedulerEnabled}
                        >
                          {t('preview_log')}
                        </StartProcessButton>
                      </div>
                      <Button
                        variant="text"
                        onClick={() => setPreviewSyncDialog(true)}
                        sx={{ paddingLeft: '24px' }}
                      >
                        <Typography variant="body1" sx={{ fontWeight: 400 }}>
                          {t('what_is_preview_sync')}
                        </Typography>
                      </Button>
                      <Dialog
                        open={previewSyncDialog}
                        onClose={() => setPreviewSyncDialog(false)}
                      >
                        <Dialog.Title
                          sx={{ fontSize: '16px', fontWeight: 600 }}
                        >
                          {t('what_is_preview_sync')}
                          <IconButton
                            sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                            }}
                            onClick={() => setPreviewSyncDialog(false)}
                          >
                            <Icon type="default" icon={faXmark} fontSize={21} />
                          </IconButton>
                        </Dialog.Title>
                        <Dialog.Content>
                          <MuiAlert severity="info">
                            <Stack
                              direction="row"
                              spacing="8px"
                              sx={{
                                alignItems: 'flex-start',
                              }}
                            >
                              <Typography
                                variant="button"
                                sx={{
                                  fontWeight: 400,
                                }}
                              >
                                <Trans i18nKey="preview_sync_display">
                                  Preview sync to display the
                                  <span
                                    style={{ fontSize: '14', fontWeight: 600 }}
                                  >
                                    differences between store and feed file
                                    value
                                  </span>
                                  without making any updates.
                                </Trans>
                                &nbsp;
                                <Link to="https://help.stock-sync.com/en/article/is-stock-sync-able-to-preview-sync-without-any-updates-51f6wk/">
                                  {t('learn_more_here')}
                                </Link>
                              </Typography>
                            </Stack>
                          </MuiAlert>
                          <Box sx={{ paddingTop: '16px' }} />
                          <MuiAlert
                            severity="warning"
                            icon={
                              <Icon
                                type="default"
                                icon={faCircleInfo}
                                style={{
                                  color: '#5E4200',
                                  fontSize: '20px',
                                }}
                              />
                            }
                          >
                            <Typography
                              variant="button"
                              sx={{ color: '#5E4200', fontWeight: 400 }}
                            >
                              {t('preview_sync_schedule')}
                            </Typography>
                          </MuiAlert>
                          <img
                            src={previewSync}
                            width={500}
                            style={{
                              display: 'block',
                              marginLeft: 'auto',
                              marginRight: 'auto',
                            }}
                          />
                        </Dialog.Content>
                      </Dialog>
                    </Stack>
                  )}
                </>
              ) : (
                <StartProcessButton
                  variant="contained"
                  runAsSample={watch('runAsSample')}
                  watch={watch}
                  setFormValue={() => setValue('previewSync', false)}
                  disable={!feed.enabled}
                  sx={{ width: smAndDown ? '220px' : '350px' }}
                >
                  {
                    {
                      update: t('process_now'),
                      import: t('add_products'),
                      remove: t('process_now'),
                    }[feed.feedType ?? '']
                  }
                </StartProcessButton>
              )}
            </>
          ) : (
            // email feed behave different
            <>
              {['start', 'queuing'].includes(feed.status) ? (
                <StartProcessButton
                  variant="contained"
                  runAsSample={watch('runAsSample')}
                  watch={watch}
                  setFormValue={() => {
                    setValue('previewSync', false);
                    setValue('forceStart', true);
                  }}
                  disable={!feed.enabled}
                >
                  {t('force_start')}
                </StartProcessButton>
              ) : feed.status === 'processing' ? (
                <StopProcessButton />
              ) : (
                <></>
              )}
            </>
          )}
        </Box>
      </Stack>
    </>
  );
}

interface StartProcessButtonProps {
  runAsSample: boolean;
  setFormValue: () => void;
  watch: UseFormReturn<FormValues>['watch'];
  children: React.ReactNode;
  variant: ButtonProps['variant'];
  disable: ButtonProps['disabled'];
  sx?: SxProps;
}

function StartProcessButton({
  runAsSample = false,
  setFormValue,
  watch,
  children,
  variant,
  disable,
  sx,
}: StartProcessButtonProps) {
  const { t } = useTranslation();
  const { feed: liveFeed } = useDetailsFeed();
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null
  );
  const alert = useAlert();

  const [executeFeedState, executeFeed] = useMutation(ExecuteFeed, {
    dataKey: 'runNow',
  });
  const [runNowSampleState, runNowSample] = useMutation(RunNowSample, {
    dataKey: 'runNowSample',
  });

  const [, updatePreviewMode] = useMutation(UpdatePreviewMode, {
    dataKey: 'updatePreviewMode',
  });

  const isFetching = executeFeedState.fetching || runNowSampleState.fetching;
  return (
    <>
      <Button
        variant={variant}
        loading={isFetching}
        disabled={disable}
        onClick={(event) => setAnchorEl(event.currentTarget)}
        sx={{ ...sx }}
      >
        {children}
      </Button>
      <PopoverConfirm
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        cancelButtonProps={{
          children: t('cancel'),
          onClick: () => setAnchorEl(null),
        }}
        okButtonProps={{
          children: t('yes'),
          onClick: async () => {
            setAnchorEl(null);
            setFormValue();
            posthog.capture('Start Process', { start: 'process' });
            const executeFn = runAsSample ? runNowSample : executeFeed;
            await executeFn(
              {
                feedId: +liveFeed.id,
                preview: !runAsSample ? watch('previewSync') : undefined,
                forceStart:
                  !runAsSample &&
                  ['email_in', 'email_link'].includes(
                    liveFeed.combinedSourceType
                  )
                    ? watch('forceStart')
                    : undefined,
              },
              {
                onSuccess({ enqueueSnackbar, data: feed }) {
                  enqueueSnackbar(t('feed_process_started'), {
                    variant: 'success',
                  });
                  liveFeed.setStatus(feed.status);
                },
                onError: ({ error }) => {
                  alert.setMessage(t(error));
                },
              }
            );
            {
              !runAsSample &&
                updatePreviewMode(
                  {
                    feedId: parseInt(liveFeed.id),
                    preview: watch('previewSync'),
                  },
                  {}
                );
            }
          },
        }}
      >
        <Typography variant="h6">{t('confirm_to_proceed')}</Typography>
      </PopoverConfirm>
    </>
  );
}

function StopProcessButton() {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const onClose = () => setOpen(false);
  const { feed: liveFeed } = useDetailsFeed();
  const [cancelProcessState, cancelProcess] = useMutation(CancelProcess, {
    dataKey: 'cancelProcess',
  });

  return (
    <div>
      <Button
        disabled={liveFeed.isCanceling}
        danger
        size="small"
        onClick={() => setOpen(true)}
        sx={{ width: '100%' }}
      >
        {t('cancel_process')}
      </Button>

      <Dialog open={open} onClose={onClose} maxWidth="xs">
        <Dialog.Title>{t('attention')}</Dialog.Title>
        <Dialog.Content>{t('are_you_sure')}</Dialog.Content>
        <Dialog.Actions>
          <Button
            variant="outlined"
            size="small"
            onClick={onClose}
            disabled={cancelProcessState.fetching}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="contained"
            size="small"
            onClick={async () => {
              await cancelProcess(
                { feedId: +liveFeed.id },
                {
                  onSuccess({ enqueueSnackbar, data: feed }) {
                    enqueueSnackbar(t('feed_process_cancelled'), {
                      variant: 'success',
                    });
                    liveFeed.setStatus(feed.status);
                  },
                }
              );
              onClose();
            }}
            loading={cancelProcessState.fetching}
          >
            {t('proceed_stop_process')}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </div>
  );
}

type SyncStatus = 'queuing' | 'processing' | 'completed';

const syncSteps = [
  'load_feed_and_products',
  'scan_for_matching',
  'performing_update',
  'adding_product',
  'performing_export',
  'performing_remove',
] as const;

type SyncStep = (typeof syncSteps)[number];

function SyncState() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const smAndDown = useMatches('sm');
  const { feed: liveFeed } = useDetailsFeed();
  const status = liveFeed.status;
  const syncProgress = Number(liveFeed.progress);
  const syncSteps: Array<{ step: SyncStep; syncStatus: SyncStatus }> = [
    {
      step: 'load_feed_and_products',
      syncStatus:
        (syncProgress >= 12 && 'completed') ||
        (syncProgress > 0 && 'processing') ||
        'queuing',
    },
    {
      step: 'scan_for_matching',
      syncStatus:
        (syncProgress >= 14 && 'completed') ||
        (syncProgress >= 12 && 'processing') ||
        'queuing',
    },
    {
      step: match(liveFeed.humanizeFeedType)
        .returnType<SyncStep>()
        .with('export', () => 'performing_export')
        .with('import', () => 'adding_product')
        .with('remove', () => 'performing_remove')
        .otherwise(() => 'performing_update'),
      syncStatus: (syncProgress >= 14 && 'processing') || 'queuing',
    },
  ];
  return (
    <>
      {['email_in', 'email_link'].includes(liveFeed.combinedSourceType) ? (
        <Box
          sx={{
            width: smAndDown ? '250px' : 'auto',
            backgroundColor: 'white',
            padding: '24px',
          }}
        >
          <Stack
            sx={{
              alignItems: 'center',
            }}
          >
            {liveFeed.isCanceling ? (
              t('canceling')
            ) : (
              <>
                <ProgressTypography />
                {status === 'processing' && (
                  <>
                    <LinearProgress
                      sx={{ width: '50%', marginTop: '8px' }}
                      variant="determinate"
                      value={syncProgress}
                    />

                    <Stack spacing="5px" sx={{ marginTop: '16px' }}>
                      {syncSteps.map(({ step, syncStatus }) => (
                        <Stack
                          key={step}
                          direction="row"
                          spacing="5px"
                          sx={{
                            alignItems: 'center',
                          }}
                        >
                          <Box
                            sx={{
                              display: 'inline-flex',
                              color: {
                                queuing: 'text.disabled',
                                processing: 'primary.main',
                                completed: 'primary.main',
                              }[syncStatus],
                            }}
                          >
                            {['queuing', 'processing'].includes(syncStatus) && (
                              <Icon
                                type="default"
                                spin
                                fontSize={18}
                                icon={faCircleNotch}
                              />
                            )}
                            {['completed'].includes(syncStatus) && (
                              <Icon
                                type="default"
                                fontSize={18}
                                icon={faCircle}
                              />
                            )}
                          </Box>
                          <Typography
                            variant="caption"
                            sx={{
                              whiteSpace: 'nowrap',
                              color: {
                                queuing: 'text.disabled',
                                processing: 'text.disabled',
                                completed: 'text.primary',
                              }[syncStatus],
                            }}
                          >
                            {t(step)}
                          </Typography>
                        </Stack>
                      ))}
                    </Stack>
                  </>
                )}
                {['start', 'queuing'].includes(status) && (
                  <Stack spacing={1} sx={{ paddingTop: '16px' }}>
                    <Typography>
                      Next run is <strong>{currentStore.minHour}</strong> hours
                      from now
                    </Typography>
                    <Link
                      to="https://help.stock-sync.com/en/article/why-email-process-only-runs-at-a-certain-time-period-1c7nmh1/?bust=1724210411608"
                      sx={{ marginLeft: '8px', alignSelf: 'center' }}
                    >
                      <Button
                        variant="text"
                        size="small"
                        sx={{ padding: 0 }}
                        startIcon={
                          <Icon type="default" icon={faCircleQuestion} />
                        }
                      >
                        {t('why_on_queuing')}
                      </Button>
                    </Link>
                  </Stack>
                )}
              </>
            )}
          </Stack>
        </Box>
      ) : (
        <Box
          sx={{
            width: '100%',
            border: '0.5px solid',
            borderColor: theme.palette.grey[300],
            padding: '24px',
          }}
        >
          <Stack
            sx={{
              alignItems: 'center',
            }}
          >
            {liveFeed.isCanceling ? (
              t('canceling')
            ) : (
              <>
                <ProgressTypography />

                {status === 'processing' && (
                  <LinearProgress
                    sx={{ width: '50%', marginTop: '8px' }}
                    variant="determinate"
                    value={syncProgress}
                  />
                )}
                <Stack spacing="5px" sx={{ marginTop: '16px' }}>
                  {syncSteps.map(({ step, syncStatus }) => (
                    <Stack
                      key={step}
                      direction="row"
                      spacing="5px"
                      sx={{
                        alignItems: 'center',
                      }}
                    >
                      <Box
                        sx={{
                          display: 'inline-flex',
                          color: {
                            queuing: 'text.disabled',
                            processing: 'primary.main',
                            completed: 'primary.main',
                          }[syncStatus],
                        }}
                      >
                        {['queuing', 'processing'].includes(syncStatus) && (
                          <Icon
                            type="default"
                            spin
                            fontSize={18}
                            icon={faCircleNotch}
                          />
                        )}
                        {['completed'].includes(syncStatus) && (
                          <Icon type="default" fontSize={18} icon={faCircle} />
                        )}
                      </Box>
                      <Typography
                        variant="caption"
                        sx={{
                          whiteSpace: 'nowrap',
                          color: {
                            queuing: 'text.disabled',
                            processing: 'text.disabled',
                            completed: 'text.primary',
                          }[syncStatus],
                        }}
                      >
                        {t(step)}
                      </Typography>
                    </Stack>
                  ))}
                </Stack>
                {['email', 'uploaded_file'].includes(
                  liveFeed.sourceType ?? ''
                ) &&
                  liveFeed.sourceFileFileName && (
                    <Typography
                      variant="body2"
                      sx={{ marginTop: '24px' }}
                    >{`${t(
                      'current_file'
                    )} ${liveFeed.sourceFileFileName}`}</Typography>
                  )}
                {['processing'].includes(status) &&
                  ['import'].includes(liveFeed.humanizeFeedType) && (
                    <MuiAlert
                      severity="info"
                      icon={false}
                      sx={{ marginTop: '16px' }}
                    >
                      <AlertTitle>{t('note')} </AlertTitle>
                      {t('note_for_image_and_desc')}
                    </MuiAlert>
                  )}
              </>
            )}
          </Stack>
        </Box>
      )}
    </>
  );
}

function ProgressTypography() {
  const { t } = useTranslation();
  const { feed: liveFeed } = useDetailsFeed();
  const status = liveFeed.status;
  const syncProgress = Number(liveFeed.progress);
  return (
    <Typography variant="h6">
      {liveFeed.bulkLoading ? t('processing') : `${t(status)}...`}
      {status === 'processing' && (
        <Box component="span" sx={{ marginLeft: '6px', fontWeight: 600 }}>
          {syncProgress}%
        </Box>
      )}
    </Typography>
  );
}
