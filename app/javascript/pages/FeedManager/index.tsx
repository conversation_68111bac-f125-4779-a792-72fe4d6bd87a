import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import * as React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { FeedEditAppBar, type OnSubmit } from '@/components/FeedEditAppBar';
import { useCurrentFeed } from '@/hooks/useCurrentFeed';
import * as routes from '@/routes';
import type { UserProfile } from '@/types';

import * as SourceTypes from './components/SourceTypes';
import { formInSchema, formSchema, type FeedManagerFormValues } from './schema';
import { useUpdateFeedManagerSource } from './useUpdateFeedManagerSource';

export function FeedManager() {
  const { currentFeed } = useCurrentFeed();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { updateFeedSource } = useUpdateFeedManagerSource();

  const formProps = useForm<FeedManagerFormValues>({
    mode: 'onSubmit',
    values: React.useMemo(() => formInSchema.parse(currentFeed), [currentFeed]),
    resolver: zodResolver(formSchema),
  });
  console.warn(`combinedSourceType:`, currentFeed.combinedSourceType);
  console.log(`currentFeed:`, currentFeed);
  console.log(`formProps watch:`, formProps.watch());
  console.log(`errors:`, JSON.stringify(formProps.formState.errors, null, 2));

  // https://react-hook-form.com/docs/useform/formstate
  // ❌ formProps.formState.isDirty is accessed conditionally, does not subscribe to changes of that state
  // ✅ read formState values to subscribe to changes:
  const { isDirty } = formProps.formState;

  const submitHandler: OnSubmit<FeedManagerFormValues> =
    ({ onSuccess, onError, onSkip }) =>
    async (values) => {
      if (isDirty === false) return onSkip({ data: values });
      return updateFeedSource({ values, onSuccess, onError });
    };

  const onSubmit = formProps.handleSubmit(
    submitHandler({
      onSuccess({ data: feed, enqueueSnackbar }) {
        enqueueSnackbar(`${feed.profileName} settings updated`, {
          variant: 'success',
        });
        navigate(routes.mappingRoute(feed.id));
      },
      onSkip({ data: feed }) {
        navigate(routes.mappingRoute(feed.id));
      },
    })
  );

  return (
    <FormProvider {...formProps}>
      <form onSubmit={onSubmit}>
        <FeedEditAppBar
          title={t('feed_manager_title')}
          redirectUrl={routes.feedDetailRoute(currentFeed.id)}
          backButton={
            <FeedEditAppBar.BackButton
              variant="outlined"
              onClick={() =>
                navigate(routes.selectConnectionRoute(currentFeed.id))
              }
            >
              {t('change_connection')}
            </FeedEditAppBar.BackButton>
          }
          nextButton={
            <FeedEditAppBar.NextButton
              type="submit"
              loading={formProps.formState.isSubmitting}
            >
              {t('next')}
            </FeedEditAppBar.NextButton>
          }
        />
        {/**
         * instead of a huge chunk with shared conditions,
         * divert to component and compose there
         */}
        {React.useMemo(() => {
          const ComponentBySourceType =
            (
              {
                aapd: SourceTypes.Aapd,
                agis: SourceTypes.Agis,
                airtable: SourceTypes.Airtable,
                airtable_v2: SourceTypes.AirtableV2,
                aliexpress_in: SourceTypes.AliexpressIn,
                aliexpress_out: SourceTypes.AliexpressOut,
                aliexpress_scraper: SourceTypes.AliexpressScraper,
                amazon_in: SourceTypes.Amazon,
                amazon_out: SourceTypes.Amazon,
                amrod: SourceTypes.Amrod,
                ascolour: SourceTypes.Ascolour,
                asi: SourceTypes.Asi,
                atelier_api: SourceTypes.AtelierAPI,
                axiz: SourceTypes.Axiz,
                azure_blob: SourceTypes.AzureBlob,
                azure_sql_db: SourceTypes.AzureSQLDatabase,
                backblaze_b2: SourceTypes.BackblazeB2,
                backmarket: SourceTypes.Backmarket,
                banggood: SourceTypes.Banggood,
                banned_apparel: SourceTypes.BannedApparel,
                beautyfort: SourceTypes.Beautyfort,
                bems: SourceTypes.Bems,
                bewicked: SourceTypes.Bewicked,
                bigcommerce_feed: SourceTypes.BigcommerceFeed,
                bigcommerce_store: SourceTypes.BigcommerceStore,
                billiet: SourceTypes.Billiet,
                bling: SourceTypes.Bling,
                boards_and_more: SourceTypes.BoardsAndMore,
                boards_and_more_v2: SourceTypes.BoardsAndMore,
                box: SourceTypes.Box,
                brands_distribution: SourceTypes.BrandsDistribution,
                brightpoint_soap: SourceTypes.BrightpointSoap,
                bsale_chile: SourceTypes.BsaleChile,
                bts_wholesaler: SourceTypes.BtsWholesaler,
                busyx: SourceTypes.Busyx,
                amrod_v2: SourceTypes.AmrodV2,
                chattanooga_shooting: SourceTypes.ChattanoogaShooting,
                cj: SourceTypes.Cj,
                clf: SourceTypes.Clf,
                coasteramer: SourceTypes.Coasteramer,
                combisteel: SourceTypes.Combisteel,
                controlport: SourceTypes.Controlport,
                copper_bay_digital: SourceTypes.CopperBayDigital,
                custom_login: SourceTypes.CustomLogin,
                depasquale_salon_systems: SourceTypes.DepasqualaSalonSystems,
                dicker_data: SourceTypes.DickerData,
                dresscode_cloud: SourceTypes.DresscodeCloud,
                dropbox: SourceTypes.Dropbox,
                dropbox_api: SourceTypes.DropboxApi,
                dropship_clothes: SourceTypes.DropshipClothes,
                dropshipping_b2b: SourceTypes.DropshippingB2b,
                dropshipping_b2b_v2: SourceTypes.DropshippingB2b,
                dropshipping_b2b_api: SourceTypes.DropshippingB2bApi,
                dropshipzone: SourceTypes.Dropshipzone,
                ebay_in: SourceTypes.EbayIn,
                ebay_out: SourceTypes.EbayOut,
                ebay_v2_in: SourceTypes.EbayV2In,
                ebay_v2_out: SourceTypes.EbayV2Out,
                ebihr: SourceTypes.Ebihr,
                ecomdash: SourceTypes.Ecomdash,
                eldorado: SourceTypes.Eldorado,
                elegant_moments: SourceTypes.ElegantMoments,
                hofman_animal_care: SourceTypes.HofmanAnimalCare,
                efashion: SourceTypes.Efashion,
                emag_marketplace: SourceTypes.EmagMarketplace,
                email_in: SourceTypes.EmailIn,
                email_out: SourceTypes.EmailOut,
                email_link: SourceTypes.EmailLink,
                encompass: SourceTypes.Encompass,
                engel_dropship: SourceTypes.EngelDropship,
                erply: SourceTypes.Erply,
                etsy_in: SourceTypes.EtsyIn,
                etsy_out: SourceTypes.EtsyOut,
                digital_ocean: SourceTypes.DigitalOcean,
                feed: SourceTypes.Feed,
                feed_4akid_shopify_store: SourceTypes.Feed4akidShopifyStore,
                football_souvenir: SourceTypes.FootballSouvenir,
                forge_motorsport: SourceTypes.ForgeMotorsport,
                files_api: SourceTypes.FilesAPI,
                ftp_in: SourceTypes.FtpIn,
                ftp_out: SourceTypes.FtpOut,
                ftps: SourceTypes.Ftps,
                ftpsimplicit: SourceTypes.FtpsImplicit,
                ftp_multiple: SourceTypes.FtpMultiple,
                ftps_multiple: SourceTypes.FtpsMultiple,
                ftp_houzz_out: SourceTypes.FtpHouzzOut,
                fulfillrite: SourceTypes.Fulfillrite,
                furnicher: SourceTypes.Furnicher,
                gaia_suite: SourceTypes.GaiaSuite,
                gateway_nwg: SourceTypes.GatewayNwg,
                giga_cloud: SourceTypes.GigaCloud,
                grupo_logi: SourceTypes.GrupoLogi,
                google_drive: SourceTypes.GoogleDrive,
                google_drive_v3: SourceTypes.GoogleDriveV3,
                google_sheet_published: SourceTypes.GoogleSheetPublished,
                google_shopping_in: SourceTypes.GoogleShopping,
                google_shopping_out: SourceTypes.GoogleShopping,
                google_spreadsheet_in: SourceTypes.GoogleSpreadsheetIn,
                google_spreadsheet_out: SourceTypes.GoogleSpreadsheetOut,
                heo: SourceTypes.Heo,
                heo_api: SourceTypes.HeoApi,
                heo_csv: SourceTypes.HeoCsv,
                hlc: SourceTypes.Hlc,
                horizon_hobby: SourceTypes.HorizonHobby,
                hypercel: SourceTypes.Hypercel,
                hyundai_nl: SourceTypes.HyundaiNl,
                ingram_content: SourceTypes.IngramContent,
                ingram_content_ftp: SourceTypes.IngramContentFtp,
                intcomex: SourceTypes.Intcomex,
                internet_bikes: SourceTypes.InternetBikes,
                ital_trading: SourceTypes.ItalTrading,
                jdm_products: SourceTypes.JdmProducts,
                keno: SourceTypes.Keno,
                kevro: SourceTypes.Kevro,
                killerdeal: SourceTypes.Killerdeal,
                kotryna: SourceTypes.Kotryna,
                likewise_floors: SourceTypes.LikewiseFloors,
                logsta: SourceTypes.Logsta,
                magento: SourceTypes.Magento,
                malabs: SourceTypes.Malabs,
                marathon_leisure: SourceTypes.MarathonLeisure,
                maropost: SourceTypes.Maropost,
                matas: SourceTypes.Matas,
                matterhorn: SourceTypes.Matterhorn,
                maxevan: SourceTypes.Maxevan,
                maween_trading: SourceTypes.MaweenTrading,
                microsoft_sharepoint: SourceTypes.MicrosoftSharepoint,
                midocean: SourceTypes.Midocean,
                mstgolf: SourceTypes.Mstgolf,
                mustek: SourceTypes.Mustek,
                mysale_api_out: SourceTypes.MysaleApiOut,
                my_bertus: SourceTypes.MyBertus,
                naturaldispensary: SourceTypes.Naturaldispensary,
                net13: SourceTypes.MetriQuadri,
                nod: SourceTypes.Nod,
                novaengel: SourceTypes.Novaengel,
                nrdata: SourceTypes.Nrdata,
                one_drive_file: SourceTypes.OneDriveFile,
                one_on_one_wholesale: SourceTypes.OneOnOneWholeSale,
                online_file: SourceTypes.OnlineFile,
                onshopfront: SourceTypes.Onshopfront,
                orso: SourceTypes.Orso,
                overnight_mountings: SourceTypes.OvernightMountings,
                parts_unlimited: SourceTypes.PartsUnlimited,
                premier_wd: SourceTypes.PremierWD,
                project_verte: SourceTypes.ProjectVerte,
                prv: SourceTypes.Prv,
                puckator_dropship: SourceTypes.PuckatorDropship,
                puckator_api: SourceTypes.PuckatorApi,
                qbp: SourceTypes.Qbp,
                qbp_v2: SourceTypes.QbpV2,
                qbp_pos: SourceTypes.Qbp,
                quickbooks: SourceTypes.Quickbooks,
                rct_data_feed: SourceTypes.RectronDataFeed,
                rekman: SourceTypes.Rekman,
                rest_api: SourceTypes.RestApi,
                restlet: SourceTypes.Restlet,
                retail_edge: SourceTypes.RetailEdge,
                richmond_interiors: SourceTypes.RichmondInteriors,
                s3: SourceTypes.S3,
                sanmar: SourceTypes.Sanmar,
                schake: SourceTypes.Schake,
                sex_toy_distributing: SourceTypes.SexToyDistributing,
                sex_toy_distributing_images:
                  SourceTypes.SexToyDistributingImages,
                sftp_in: SourceTypes.SftpIn,
                sftp_out: SourceTypes.SftpOut,
                sftp_multiple: SourceTypes.Sftp,
                shipnetwork: SourceTypes.ShipNetwork,
                sherco_network: SourceTypes.ShercoNetwork,
                shopify_feed: SourceTypes.ShopifyFeed,
                shopify_public: SourceTypes.ShopifyPublic,
                shopify_store: SourceTypes.ShopifyStore,
                shooting_warehouse_inventory:
                  SourceTypes.ShootingWarehouseInventory,
                silverbene: SourceTypes.Silverbene,
                seagull: SourceTypes.Seagull,
                marashoes: SourceTypes.Marashoes,
                phorest: SourceTypes.Phorest,
                prenta: SourceTypes.Prenta,
                sap_business_one_sl_api: SourceTypes.SapBusinessOneSlApi,
                simpro: SourceTypes.Simpro,
                siigo: SourceTypes.Siigo,
                smiffys: SourceTypes.Smiffys,
                smiffys_api: SourceTypes.SmiffysApi,
                soap: SourceTypes.Soap,
                spm_network: SourceTypes.SpmNetwork,
                square_store: SourceTypes.SquareStore,
                ss_active_wear: SourceTypes.SsActiveWear,
                stileo_out: SourceTypes.StileoOut,
                storeden: SourceTypes.Storeden,
                strawberry_net: SourceTypes.StrawberryNet,
                stricker_europe: SourceTypes.StrickerEurope,
                stuller: SourceTypes.Stuller,
                sunkys: SourceTypes.Sunkys,
                svs_vetchannel: SourceTypes.SvsVetchannel,
                swift_stock_flat: SourceTypes.SwiftStock,
                swift_stock_nested: SourceTypes.SwiftStock,
                swift_stock_removal: SourceTypes.SwiftStock,
                sysco: SourceTypes.Sysco,
                syscom: SourceTypes.Syscom,
                synnex: SourceTypes.Synnex,
                synnex_ftp: SourceTypes.SynnexFtp,
                tarsus_distribution: SourceTypes.TarsusDistribution,
                timco_api: SourceTypes.TimcoApi,
                tiktokshop: SourceTypes.Tiktokshop,
                tme: SourceTypes.Tme,
                toptex: SourceTypes.TopTex,
                tpl_central: SourceTypes.TplCentral,
                tuscany_leather: SourceTypes.TuscanyLeather,
                trendcollection: SourceTypes.Trendcollection,
                tsigaridas_books: SourceTypes.TsigaridasBooks,
                treasure_house: SourceTypes.TreasureHouse,
                turn14: SourceTypes.Turn14,
                twhouse: SourceTypes.Twhouse,
                ulefone: SourceTypes.Ulefone,
                uk_distributor: SourceTypes.UKDistributor,
                unleashed: SourceTypes.Unleashed,
                uploaded_file: SourceTypes.UploadedFile,
                vend: SourceTypes.Vend,
                veloconnect: SourceTypes.Veloconnect,
                verifone: SourceTypes.Verifone,
                vidaxl: SourceTypes.Vidaxl,
                vietti: SourceTypes.RestApi,
                walmart_api_out: SourceTypes.WalmartApiOut,
                wayfair_out: SourceTypes.WayfairOut,
                western_power_sports: SourceTypes.WesternPowerSports,
                wholecell: SourceTypes.WholeCell,
                wholesalesportwear: SourceTypes.Wholesalesportwear,
                windsor: SourceTypes.Windsor,
                wix_store: SourceTypes.WixStore,
                woocommerce: SourceTypes.Woocommerce,
                wos_action_sports: SourceTypes.WosActionSports,
                xero: SourceTypes.Xero,
                xtrader: SourceTypes.XTrader,
                zoho_inventory_out: SourceTypes.ZohoInventoryOut,
                zoho_sheet: SourceTypes.ZohoSheet,
              } satisfies Partial<
                Record<
                  UserProfile['combinedSourceType'],
                  React.FunctionComponent
                >
              >
            )[currentFeed.combinedSourceType ?? ''] ??
            SourceTypes.SupplierWithoutSetting;

          return <ComponentBySourceType />;
        }, [currentFeed])}
        <Stack
          direction="row"
          spacing="8px"
          sx={{
            alignSelf: 'center',
            justifyContent: 'flex-end',
            marginTop: '16px',
            paddingBottom: '60px',
          }}
        >
          <FeedEditAppBar.BackButton
            variant="outlined"
            onClick={() =>
              navigate(routes.selectConnectionRoute(currentFeed.id))
            }
          >
            {t('change_connection')}
          </FeedEditAppBar.BackButton>
          <FeedEditAppBar.NextButton
            loading={formProps.formState.isSubmitting}
            onClick={onSubmit}
          >
            {t('next')}
          </FeedEditAppBar.NextButton>
        </Stack>
      </form>
    </FormProvider>
  );
}
