import { faSquareArrowRight } from '@fortawesome/pro-light-svg-icons';
import {
  CardContent,
  Chip,
  Stack,
  Typography,
  useTheme,
  type PopoverProps,
} from '@mui/material';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';

import { Alert } from '@/components/Alert';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CardActionArea } from '@/components/CardActionArea';
import { Checkbox } from '@/components/Checkbox';
import { Dialog } from '@/components/Dialog';
import { Icon } from '@/components/Icon';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { Tooltip } from '@/components/Tooltip';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useMutation } from '@/hooks/useMutation';
import { useOnboardingStep } from '@/hooks/useOnboardingStep';
import { CreateNewFeed, ResetFeed } from '@/queries/FeedSettings';
import * as routes from '@/routes';
import type { Supplier, UserProfile } from '@/types';

interface ConnectionProps {
  connection: Supplier;
  feedType: UserProfile['feedType'];
  feedName: string;
  onSelect: (connection: Supplier) => void;
  isActive?: boolean;
  extraInfomation?: React.ReactNode;
}

export function Connection({
  connection,
  feedType,
  feedName,
  onSelect,
  isActive = false,
  extraInfomation,
}: ConnectionProps) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>();
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Card sx={{ padding: 0, height: '100%' }}>
      <CardActionArea
        onClick={(event) => {
          onSelect(connection);
          setAnchorEl(event.currentTarget);
        }}
        sx={{
          height: '100%',
          '[data-icon]': {
            color: '#8898aa33',
          },
          '&:hover': {
            backgroundColor: '#8898aa1a',
            '[data-icon]': {
              color: '#303030',
            },
          },
          ...(isActive
            ? {
                backgroundColor: '#8898aa1a',
                borderColor: theme.palette.primary.main,
                border: '2px solid',
              }
            : {}),
        }}
      >
        <CardContent data-card sx={{ height: '100%' }}>
          <Stack
            direction="column"
            sx={{
              justifyContent: 'space-between',
              height: '100%',
            }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{
                justifyContent: 'space-between',
                alignItems: 'flex-start',
              }}
            >
              <Stack
                direction="row"
                sx={{
                  alignItems: 'center',
                }}
              >
                {connection.supplierType === 'supplier' ? (
                  <Tooltip
                    variant="default"
                    placement="top"
                    title={t(connection.countryCode)}
                  >
                    <img
                      src={`https://cdn.stock-sync.com/images/${connection.countryCode}.svg`}
                      width="30px"
                      style={{ marginRight: '16px' }}
                      alt={connection.countryCode}
                    />
                  </Tooltip>
                ) : (
                  <img
                    src={connection.imageUrl ?? ''}
                    width="30px"
                    style={{ marginRight: '16px' }}
                  />
                )}
                <Stack spacing={0}>
                  <Typography variant="h6">
                    {connection.name}{' '}
                    {connection.popular && (
                      <Chip
                        label={t('popular')}
                        size="small"
                        sx={{
                          display: 'block',
                          width: '63px',
                          color: '#0C7FE8',
                          backgroundColor: '#0C7FE80F',
                        }}
                      />
                    )}
                  </Typography>
                </Stack>
              </Stack>
              {/* When select the connection, the icon is showing black color same as the hover. */}
              {isActive ? (
                <Icon
                  type="default"
                  icon={faSquareArrowRight}
                  style={{ color: 'black', fontSize: '32px' }}
                />
              ) : (
                <Icon
                  data-icon
                  type="default"
                  icon={faSquareArrowRight}
                  fontSize={32}
                />
              )}
            </Stack>
            <div>{extraInfomation}</div>
          </Stack>
        </CardContent>
      </CardActionArea>
      <ConfirmNewFeedPopover
        anchorEl={anchorEl}
        feedName={feedName}
        feedType={feedType}
        handleClose={() => setAnchorEl(null)}
        connection={connection}
      />
    </Card>
  );
}

interface ConfirmNewFeedPopoverProps
  extends Pick<UserProfile, 'feedType'>,
    Pick<PopoverProps, 'anchorEl'> {
  feedName: UserProfile['profileName'];
  connection: Supplier;
  handleClose: () => void;
}

const ConfirmNewFeedPopover = ({
  anchorEl,
  feedName,
  feedType,
  handleClose,
  connection: { id: supplierId, redirectTo },
}: ConfirmNewFeedPopoverProps) => {
  const { pathname } = useLocation();
  const { refetch } = useCurrentStore();
  const step = useOnboardingStep();

  const { t } = useTranslation();
  const alert = useAlert();

  const [saving, setSaving] = useState(false);
  const [reselecting, setReselecting] = useState(false);

  const [, createNewFeed] = useMutation(CreateNewFeed, {
    dataKey: 'createNewFeed',
  });

  const open = Boolean(anchorEl);
  const id = open ? 'connection-popconfirm' : undefined;

  const preCheckFeed = async () => {
    handleClose();
    if (pathname === routes.newConnection) {
      await createFromTemplate();
    } else {
      setReselecting(true);
    }
  };

  // Only for new profiles' creation
  const createFromTemplate = async () => {
    setSaving(true);
    createNewFeed(
      {
        supplierId,
        feedName,
        feedType,
      },
      {
        onSuccess({ data: feed, enqueueSnackbar, navigate }) {
          enqueueSnackbar(`Feed ${feed.profileName} created`, {
            variant: 'success',
          });
          refetch();
          step.setCurrentStep(2);
          // wait for new feed after cache update
          setTimeout(() => {
            // some template only need to redirect to dashboard (no settings required)
            // TODO: refetch from dashboard feed query
            if (redirectTo === 'dashboard') {
              navigate(routes.feedDetailRoute(feed.id));
            } else {
              navigate(routes.feedManagerRoute(feed.id));
            }
          }, 100);
        },
        onError({ error }) {
          alert.setMessage(t(error));
        },
      }
    ).finally(() => setSaving(false));
  };

  const closeResettingDialog = () => setReselecting(false);

  return (
    <>
      <PopoverConfirm
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        slotProps={{ paper: { sx: { width: 280 } } }}
        cancelButtonProps={{
          children: t('cancel'),
          onClick: handleClose,
        }}
        okButtonProps={{
          children: t('yes'),
          onClick: () => preCheckFeed(),
          loading: saving,
        }}
      >
        {alert.message.length > 0 && <Alert>{alert.message}</Alert>}
        <Typography variant="h6">{t('confirm_to_proceed')}</Typography>
      </PopoverConfirm>
      <ReselectConnectionDialog
        t={t}
        open={reselecting}
        handleClose={closeResettingDialog}
        supplierId={supplierId}
      />
    </>
  );
};

const ReselectConnectionDialog = ({ t, open, supplierId, handleClose }) => {
  const [saving, setSaving] = useState(false);
  const alert = useAlert();
  const { control, watch } = useForm({
    values: {
      removeExistingMapping: false,
    },
  });
  const [, resetFeed] = useMutation(ResetFeed, {
    dataKey: 'resetFeed',
  });
  const params = useParams();

  const updateConnection = async (value) => {
    setSaving(true);
    resetFeed(
      {
        supplierId,
        feedId: params.id && parseInt(params.id),
        removeExistingMapping: value,
      },
      {
        onSuccess({ data: feed, navigate }) {
          navigate(routes.feedManagerRoute(feed.id));
        },
        onError({ error }) {
          alert.setMessage(t(error));
        },
      }
    ).finally(() => {
      handleClose();
      setSaving(false);
    });
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      {alert.message.length > 0 && <Alert>{alert.message}</Alert>}
      <Dialog.Title>{t('change_connection_method')}</Dialog.Title>
      <Dialog.Content>{t('change_connection_content')}</Dialog.Content>
      <Dialog.Content sx={{ paddingTop: 0 }}>
        <Checkbox
          control={control}
          name="removeExistingMapping"
          label={t('reset_matching_column')}
          sx={{ paddingLeft: 0 }}
        />
      </Dialog.Content>
      <Dialog.Actions>
        <Button
          variant="outlined"
          size="small"
          disabled={saving}
          onClick={handleClose}
        >
          {t('cancel')}
        </Button>
        <Button
          variant="contained"
          size="small"
          loading={saving}
          onClick={() => updateConnection(watch('removeExistingMapping'))}
        >
          {t('proceed')}
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
};
