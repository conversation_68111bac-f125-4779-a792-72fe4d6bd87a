import {
  faArrowLeft,
  faCircleXmark,
  faMagnifyingGlass,
} from '@fortawesome/pro-light-svg-icons';
import {
  Box,
  Checkbox,
  Chip,
  Divider,
  Drawer,
  FormGroup,
  Skeleton,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import { Hints } from 'intro.js-react';
import debounce from 'lodash/debounce';
import * as React from 'react';
import { useForm, type UseFormResetField } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { match } from 'ts-pattern';
import { gql, useQuery } from 'urql';

import { Button } from '@/components/Button';
import { CustomAppBar } from '@/components/CustomAppBar';
import { Dialog } from '@/components/Dialog';
import { FeedNameAndType } from '@/components/FeedNameAndType';
import { FormControlLabel } from '@/components/FormControlLabel';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Tab } from '@/components/Tab';
import { TextField } from '@/components/TextField';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useFeedRenderProps } from '@/hooks/useFeedRenderProps';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { GetSupplierCategories } from '@/queries/GetSupplierCategories';
import { GetSupplierCountries } from '@/queries/GetSupplierCountries';
import { GetSuppliers } from '@/queries/GetSuppliers';
import { ContactSupplier } from '@/queries/StoreMutations';
import * as routes from '@/routes';
import type {
  Supplier,
  SupplierCategory,
  SupplierCountry,
  SupplierType,
  UserProfile,
} from '@/types';

import { Connection } from './components/Connection';

const MAX_DISPLAY_CONNECTIONS = 12;

export function SelectConnection() {
  const { currentStore } = useCurrentStore();
  const { t } = useTranslation();
  const theme = useTheme();
  const { id: feedId } = useParams();
  const matchesSm = useMatches('sm');

  const [query] = useQuery({
    query: gql<{
      getFeedById: Pick<
        UserProfile,
        'feedType' | 'id' | 'ioMode' | 'profileName'
      > & {
        supplier: Pick<Supplier, 'id'>;
      };
    }>`
      query SelectConnection($feedId: Int!) {
        getFeedById(feedId: $feedId) {
          __typename
          feedType
          id
          ioMode
          profileName
          supplier {
            __typename
            id
          }
        }
      }
    `,
    variables: { feedId: Number(feedId) },
    pause: !feedId,
  });
  const feed = query.data?.getFeedById;

  const [searchText, setSearchText] = React.useState('');

  const tabs: Array<{ value: SupplierType; text: string }> = [
    { value: 'connection', text: t('connections') },
    { value: 'platform', text: t('platform') },
    { value: 'supplier', text: t('suppliers') },
    { value: 'marketplace', text: t('marketplace') },
    { value: 'bulk', text: t('shopify_self') },
  ];

  const ioMode = feed?.ioMode;
  const feedType = feed?.feedType;
  const { state: locState } = useLocation();

  const selectedFeedType = React.useMemo(() => {
    if (locState) {
      if (locState.action) {
        return locState.action;
      } else {
        if (feedId) {
          return ioMode === 'out' ? 'export' : feedType;
        } else {
          return 'update';
        }
      }
    } else {
      if (feedId) {
        return ioMode === 'out' ? 'export' : feedType;
      } else {
        // default to 'update' but technically, it shouldn't fall into this else block
        return 'update';
      }
    }
  }, [feedId, locState, ioMode, feedType]);

  const [selectedTabs, setSelectedTabs] = React.useState<SupplierType[]>(
    match<{ type: string }, SupplierType[]>({ type: locState.type }) // for onboarding page locState
      .with({ type: 'connection' }, () => ['connection', 'platform'])
      .with({ type: 'supplier' }, () => ['supplier', 'marketplace'])
      .otherwise(() => [
        'supplier',
        'connection',
        'marketplace',
        'platform',
        'bulk',
      ])
  );

  const handleTabSelect = (tab: SupplierType) => {
    setSelectedTabs((prev) => {
      if (prev.includes(tab)) {
        return prev.length > 1 ? prev.filter((t) => t !== tab) : prev;
      } else {
        return [...prev, tab];
      }
    });
  };

  const [feedName, setFeedName] = React.useState(
    feed?.profileName ?? `Profile ${currentStore.profilesCount + 1}`
  );
  const [openDrawer, setOpenDrawer] = React.useState(false);

  const [suppliersQuery] = useQuery({
    query: GetSuppliers,
    variables: { feedType: selectedFeedType },
  });

  const [selectedCountry, setSelectedCountry] = React.useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = React.useState<string[]>([]);
  const suppliers = React.useMemo(() => {
    if (selectedTabs.includes('supplier') === false) {
      return [];
    }
    let suppliers = suppliersQuery.fetching
      ? []
      : suppliersQuery.data?.getSuppliers.filter(
          (supplier) => supplier.supplierType === 'supplier'
        ) ?? [];

    if (searchText.length > 0) {
      suppliers = suppliers.filter(({ name, countryCode, categories }) =>
        categories
          .concat([name, countryCode ?? ''])
          .find((item) => item.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    if (selectedCountry.length > 0) {
      suppliers = suppliers.filter((supplier) =>
        selectedCountry.includes(supplier.countryCode)
      );
    }

    if (selectedCategory.length > 0) {
      suppliers = suppliers.filter(
        (supplier) =>
          supplier.categories.length > 0 &&
          selectedCategory.find((a) =>
            a.includes(supplier.categories.toString())
          )
      );
    }

    return suppliers;
  }, [
    suppliersQuery,
    searchText,
    selectedCountry,
    selectedCategory,
    selectedTabs,
  ]);

  const connections = React.useMemo(() => {
    if (selectedTabs.includes('connection') === false) {
      return [];
    }
    let connections = suppliersQuery.fetching
      ? []
      : suppliersQuery.data?.getSuppliers.filter(
          (supplier) => supplier.supplierType === 'connection'
        ) ?? [];

    if (searchText.length > 0) {
      connections = connections.filter(({ name }) =>
        name.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    return connections;
  }, [suppliersQuery, searchText, selectedTabs]);

  const platforms = React.useMemo(() => {
    if (selectedTabs.includes('platform') === false) {
      return [];
    }
    let platforms = suppliersQuery.fetching
      ? []
      : suppliersQuery.data?.getSuppliers.filter(
          (supplier) => supplier.supplierType === 'platform'
        ) ?? [];

    if (searchText.length > 0) {
      platforms = platforms.filter(({ name }) =>
        name.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    return platforms;
  }, [suppliersQuery, searchText, selectedTabs]);

  const marketplaces = React.useMemo(() => {
    if (selectedTabs.includes('marketplace') === false) {
      return [];
    }
    let marketplaces = suppliersQuery.fetching
      ? []
      : suppliersQuery.data?.getSuppliers.filter(
          (supplier) => supplier.supplierType === 'marketplace'
        ) ?? [];

    if (searchText.length > 0) {
      marketplaces = marketplaces.filter(({ name }) =>
        name.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    return marketplaces;
  }, [suppliersQuery, searchText, selectedTabs]);
  const bulkEdit = React.useMemo(() => {
    if (selectedTabs.includes('bulk') === false) {
      return [];
    }
    let bulkEdit = suppliersQuery.fetching
      ? []
      : suppliersQuery.data?.getSuppliers.filter(
          (supplier) => supplier.supplierType === 'bulk'
        ) ?? [];

    if (searchText.length > 0) {
      bulkEdit = bulkEdit.filter(({ name }) =>
        name.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    return bulkEdit;
  }, [suppliersQuery, searchText, selectedTabs]);

  const [selectedSupplierId, setSelectedSupplierId] = React.useState(
    feed?.supplier?.id
  );

  const feedRenderProps = useFeedRenderProps({
    humanizeFeedType: selectedFeedType,
  });

  const { control, resetField, watch, setFocus } = useForm({
    defaultValues: {
      searchSupplier: '',
      searchFilter: '',
    },
  });

  React.useEffect(() => {
    // watch also accepts a callback, doesn't rerender
    const subscription = watch(
      debounce(({ searchSupplier }) => setSearchText(searchSupplier), 300)
    );
    return () => subscription.unsubscribe(); // unsubscribe when done
  }, [watch]);

  const showBasedOnCurrentTab = React.useMemo(() => {
    return {
      supplier: selectedTabs.includes('supplier') ? suppliers : [],
      connection: selectedTabs.includes('connection') ? connections : [],
      marketplace: selectedTabs.includes('marketplace') ? marketplaces : [],
      platform: selectedTabs.includes('platform') ? platforms : [],
      bulk: selectedTabs.includes('bulk') ? bulkEdit : [],
    } satisfies Record<SupplierType, Array<Supplier>>;
  }, [selectedTabs, suppliers, connections, marketplaces, platforms, bulkEdit]);

  const stepsConfig = [
    {
      element: '#step-one',

      hint: 'Search for your preferred connection method or supplier. Use keywords like "Upload file", "FTP", or your supplier name',
    },
    {
      element: '#step-two',
      hint: 'Select how you want to import your product data. Choose from upload files, APIs, or third-party integrations.',
    },
    {
      element: '#step-three',
      hint: 'Connect with dropshipping suppliers to automatically sync their product catalogs to your store.',
    },
  ];
  const [enabled, setEnabled] = React.useState(
    () => localStorage.getItem('intro_select_connection') !== 'true'
  );

  const handleClose = React.useCallback(() => {
    localStorage.setItem('intro_select_connection', 'true');
    setEnabled(false);
  }, []);

  return (
    <>
      <CustomAppBar
        title={t('select_connection')}
        redirectUrl={
          locState.type === 'connection' || locState.type === 'supplier'
            ? routes.home
            : routes.newFeed
        }
        sx={{ marginBottom: 0 }}
      />
      {/* Note: feed name can be changed but will only be saved when the feed is created */}
      <FeedNameAndType
        feedRenderProps={feedRenderProps}
        feedId={feed?.id}
        feedName={feed?.profileName ?? feedName}
        onSubmit={(profileName) => setFeedName(profileName)}
      />
      <Hints enabled={enabled} hints={stepsConfig} onClose={handleClose} />
      <Box sx={{ background: 'white', padding: '24px', borderRadius: '8px' }}>
        <Box sx={{ margin: '8px 16px 40px 0' }}>
          <Typography variant="h5" sx={{ fontWeight: 500 }}>
            {t('connection_select')}
          </Typography>
          <Typography variant="h6" sx={{ color: theme.palette.grey[300] }}>
            {t('retrieve_feed_file')}
          </Typography>
        </Box>
        <Box sx={{ margin: '16px 0' }}>
          <TextField
            id="step-one"
            control={control}
            name="searchSupplier"
            variant="outlined"
            fullWidth
            label="Search"
            placeholder={t('search_for_more')}
            size="small"
            slotProps={{
              input: {
                sx: {
                  borderRadius: '8px',
                  paddingLeft: '16px',
                  paddingRight: '8px',
                },
                startAdornment: (
                  <Box sx={{ marginRight: '8px' }}>
                    <Icon
                      type="default"
                      icon={faMagnifyingGlass}
                      fontSize={18}
                    />
                  </Box>
                ),
                endAdornment: (
                  <IconButton
                    onClick={() => {
                      resetField('searchSupplier');
                      setFocus('searchSupplier');
                    }}
                    disableRipple
                  >
                    <Icon
                      type="default"
                      icon={faCircleXmark}
                      fontSize={20}
                      color={theme.palette.grey[300]}
                    />
                  </IconButton>
                ),
              },
            }}
          />
        </Box>
        <Stack
          direction={matchesSm ? 'column' : 'row'}
          spacing={1}
          sx={{ marginBottom: '24px', justifyContent: 'space-between' }}
        >
          <Stack direction={matchesSm ? 'column' : 'row'} spacing={2}>
            {tabs.map((tab) => (
              <Chip
                id={
                  tab.value === 'connection'
                    ? 'step-two'
                    : tab.value === 'supplier'
                      ? 'step-three'
                      : ''
                }
                key={tab.value}
                label={
                  <Typography>
                    {tab.text}
                    &nbsp;({showBasedOnCurrentTab[tab.value].length})
                  </Typography>
                }
                variant={
                  selectedTabs.includes(tab.value) ? 'filled' : 'outlined'
                }
                onClick={() => handleTabSelect(tab.value)}
                color={selectedTabs.includes(tab.value) ? 'primary' : 'default'}
                sx={{
                  '&:hover': { backgroundColor: 'black' },
                  fontSize: '16px',
                }}
              />
            ))}
          </Stack>
          {selectedTabs.includes('supplier') && (
            <Button
              variant="outlined"
              onClick={() => setOpenDrawer(!openDrawer)}
            >
              {t('filter')}
            </Button>
          )}
        </Stack>
        <Divider />
        <Box sx={{ paddingTop: '24px' }} />
        {showBasedOnCurrentTab.connection.length > 0 && (
          <>
            <Typography variant="h6" sx={{ color: theme.palette.grey[300] }}>
              {t('connections')}
            </Typography>
            <Box sx={{ paddingBottom: '24px' }} />
            <Grid container rowSpacing={2} columnSpacing={2}>
              {showBasedOnCurrentTab.connection.map((connection, index) => (
                <Grid key={index} size={{ xs: 12, md: 6, lg: 4 }}>
                  <Connection
                    connection={connection}
                    feedType={selectedFeedType}
                    feedName={feedName}
                    isActive={selectedSupplierId === connection.id}
                    onSelect={(connection) => {
                      setSelectedSupplierId(connection.id);
                      setFeedName(
                        `${connection.name} - ${currentStore.profilesCount + 1}`
                      );
                    }}
                    extraInfomation={
                      selectedTabs.includes('connection') && (
                        <Box sx={{ paddingTop: '16px' }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: theme.palette.grey[300],
                            }}
                          >
                            {connection.description ?? ''}
                          </Typography>
                        </Box>
                      )
                    }
                  />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ paddingBottom: '24px' }} />
          </>
        )}

        {showBasedOnCurrentTab.platform.length > 0 && (
          <>
            <Typography variant="h6" sx={{ color: theme.palette.grey[300] }}>
              {t('platform')}
            </Typography>
            <Box sx={{ paddingBottom: '24px' }} />
            <Grid container rowSpacing={2} columnSpacing={2}>
              {showBasedOnCurrentTab.platform.map((platform, index) => (
                <Grid key={index} size={{ xs: 12, md: 6, lg: 4 }}>
                  <Connection
                    connection={platform}
                    feedType={selectedFeedType}
                    feedName={feedName}
                    isActive={selectedSupplierId === platform.id}
                    extraInfomation={
                      selectedTabs.includes('platform') && (
                        <Box sx={{ paddingTop: '16px' }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: theme.palette.grey[300],
                            }}
                          >
                            {platform.description ?? ''}
                          </Typography>
                        </Box>
                      )
                    }
                    onSelect={(connection) => {
                      setSelectedSupplierId(connection.id);
                      setFeedName(
                        `${connection.name} - ${currentStore.profilesCount + 1}`
                      );
                    }}
                  />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ paddingBottom: '24px' }} />
          </>
        )}

        {showBasedOnCurrentTab.supplier.length > 0 && (
          <>
            <Typography variant="h6" sx={{ color: theme.palette.grey[300] }}>
              {t('suppliers')}
            </Typography>
            <Box sx={{ paddingBottom: '24px' }} />
            <Grid container rowSpacing={2} columnSpacing={2}>
              {showBasedOnCurrentTab.supplier.map((supplier, index) => (
                <Grid key={index} size={{ xs: 12, md: 6, lg: 4 }}>
                  <Connection
                    connection={supplier}
                    feedType={selectedFeedType}
                    feedName={feedName}
                    isActive={selectedSupplierId === supplier.id}
                    extraInfomation={
                      selectedTabs.includes('supplier') && (
                        <Box sx={{ paddingTop: '16px' }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: theme.palette.grey[300],
                            }}
                          >
                            {supplier.categories.length > 0
                              ? supplier.categories.map((a) => t(a)).join(', ')
                              : '-'}
                          </Typography>
                        </Box>
                      )
                    }
                    onSelect={(connection) => {
                      setSelectedSupplierId(connection.id);
                      setFeedName(
                        `${connection.name} - ${currentStore.profilesCount + 1}`
                      );
                    }}
                  />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ paddingBottom: '24px' }} />
          </>
        )}

        {showBasedOnCurrentTab.marketplace.length > 0 && (
          <>
            <Typography variant="h6" sx={{ color: theme.palette.grey[300] }}>
              {t('marketplace')}
            </Typography>
            <Box sx={{ paddingBottom: '24px' }} />
            <Grid container rowSpacing={2} columnSpacing={2}>
              {showBasedOnCurrentTab.marketplace.map((marketplace, index) => (
                <Grid key={index} size={{ xs: 12, md: 6, lg: 4 }}>
                  <Connection
                    connection={marketplace}
                    feedType={selectedFeedType}
                    feedName={feedName}
                    isActive={selectedSupplierId === marketplace.id}
                    extraInfomation={
                      selectedTabs.includes('marketplace') && (
                        <Box sx={{ paddingTop: '16px' }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: theme.palette.grey[300],
                            }}
                          >
                            {marketplace.description ?? ''}
                          </Typography>
                        </Box>
                      )
                    }
                    onSelect={(connection) => {
                      setSelectedSupplierId(connection.id);
                      setFeedName(
                        `${connection.name} - ${currentStore.profilesCount + 1}`
                      );
                    }}
                  />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ paddingBottom: '24px' }} />
          </>
        )}
        {showBasedOnCurrentTab.bulk.length > 0 && (
          <>
            <Typography variant="h6" sx={{ color: theme.palette.grey[300] }}>
              {t('shopify_self')}
            </Typography>
            <Box sx={{ paddingBottom: '24px' }} />
            <Grid container rowSpacing={2} columnSpacing={2}>
              {showBasedOnCurrentTab.bulk.map((connection, index) => (
                <Grid key={index} size={{ xs: 12, md: 6, lg: 4 }}>
                  <Connection
                    connection={connection}
                    feedType={selectedFeedType}
                    feedName={feedName}
                    isActive={selectedSupplierId === connection.id}
                    onSelect={(connection) => {
                      setSelectedSupplierId(connection.id);
                      setFeedName(
                        `${connection.name} - ${currentStore.profilesCount + 1}`
                      );
                    }}
                    extraInfomation={
                      selectedTabs.includes('connection') && (
                        <Box sx={{ paddingTop: '16px' }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: theme.palette.grey[300],
                            }}
                          >
                            {connection.description ?? ''}
                          </Typography>
                        </Box>
                      )
                    }
                  />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ paddingBottom: '24px' }} />
          </>
        )}
        {searchText.length > 0 &&
          showBasedOnCurrentTab.supplier.length === 0 &&
          showBasedOnCurrentTab.connection.length === 0 &&
          showBasedOnCurrentTab.marketplace.length === 0 &&
          showBasedOnCurrentTab.platform.length === 0 &&
          showBasedOnCurrentTab.bulk.length === 0 && (
            <ConnectionNotFound
              setSelectedTabs={setSelectedTabs}
              fetching={suppliersQuery.fetching}
              resetField={resetField}
            />
          )}
        <FiltersDrawer
          watch={watch}
          resetField={resetField}
          setFocus={setFocus}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          openDrawer={openDrawer}
          control={control}
          onClose={() => {
            setOpenDrawer(false);
          }}
        />
      </Box>
    </>
  );
}

function FiltersDrawer({
  openDrawer,
  onClose,
  control,
  selectedCountry,
  setSelectedCountry,
  selectedCategory,
  setSelectedCategory,
  watch,
  resetField,
  setFocus,
}) {
  type FilterConnection = 'country' | 'category';
  const [searchFilter, setSearchFilter] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const matchesSm = useMatches('sm');
  const [currentFilterConnectionTab, setCurrentFilterConnectionTab] =
    React.useState<FilterConnection>('country');
  const filtersTabs: Array<{ value: FilterConnection; text: string }> = [
    { value: 'country', text: t('country') },
    { value: 'category', text: t('category') },
  ];

  const [supplierCategoriesQuery] = useQuery({ query: GetSupplierCategories });
  const [supplierCountriesQuery] = useQuery({ query: GetSupplierCountries });

  const supplierCategories = React.useMemo(
    () =>
      supplierCategoriesQuery.fetching
        ? Array.from<SupplierCategory>(new Array(MAX_DISPLAY_CONNECTIONS))
        : supplierCategoriesQuery.data?.getSupplierCategories ?? [],
    [supplierCategoriesQuery]
  );

  const supplierCountries = React.useMemo(
    () =>
      supplierCountriesQuery.fetching
        ? Array.from<SupplierCountry>(new Array(MAX_DISPLAY_CONNECTIONS))
        : supplierCountriesQuery.data?.getCountries ?? [],
    [supplierCountriesQuery]
  );

  React.useEffect(() => {
    // watch also accepts a callback, doesn't rerender
    const subscription = watch(
      debounce(({ searchFilter }) => setSearchFilter(searchFilter), 300)
    );
    return () => subscription.unsubscribe(); // unsubscribe when done
  }, [watch]);

  const listOfCategory = React.useMemo(
    () =>
      currentFilterConnectionTab === 'category' && searchFilter.length > 0
        ? supplierCategories.filter(({ name }) =>
            name.toLowerCase().includes(searchFilter.toLowerCase())
          )
        : supplierCategories.map((supp) => supp),
    [searchFilter, supplierCategories, currentFilterConnectionTab]
  );

  const listOfCountries = React.useMemo(
    () =>
      currentFilterConnectionTab === 'country' && searchFilter.length > 0
        ? supplierCountries.filter(({ name }) =>
            name.toLowerCase().includes(searchFilter.toLowerCase())
          )
        : supplierCountries.map((supp) => supp),
    [searchFilter, supplierCountries, currentFilterConnectionTab]
  );
  return (
    <Drawer
      anchor="right"
      open={openDrawer}
      onClose={() => onClose()}
      sx={{
        '& .MuiDrawer-paper': {
          width: matchesSm ? '100%' : theme.spacing(54),
          p: theme.spacing(2),
          pl: theme.spacing(3),
          pb: theme.spacing(10),
        },
      }}
    >
      <Stack
        direction="row"
        spacing={2}
        sx={{
          mt: theme.spacing(4),
          mb: theme.spacing(2),
          alignItems: 'center',
        }}
      >
        <Button
          variant="outlined"
          size="small"
          onClick={() => onClose()}
          id="close-supplier-or-conncetion-filter"
        >
          <Icon type="default" icon={faArrowLeft} size="lg" />
        </Button>
      </Stack>
      <Tab.Context value={currentFilterConnectionTab}>
        <Tab.List
          borderBottom
          onChange={(_, tab) => setCurrentFilterConnectionTab(tab)}
        >
          {filtersTabs.map((tab) => (
            <Tab key={tab.value} value={tab.value} label={tab.text} />
          ))}
        </Tab.List>
        {filtersTabs.map((tab) => (
          <Tab.Panel key={tab.value} value={tab.value}>
            <TextField
              control={control}
              name="searchFilter"
              variant="outlined"
              fullWidth
              autoFocus
              label="Search"
              placeholder={
                tab.value === 'category'
                  ? t('search_for_category')
                  : t('search_for_country')
              }
              size="small"
              slotProps={{
                input: {
                  sx: {
                    borderRadius: '8px',
                    paddingLeft: '16px',
                    paddingRight: '8px',
                  },
                  startAdornment: (
                    <Box sx={{ marginRight: '8px' }}>
                      <Icon
                        type="default"
                        icon={faMagnifyingGlass}
                        fontSize={18}
                      />
                    </Box>
                  ),
                  endAdornment: (
                    <IconButton
                      onClick={() => {
                        resetField('searchFilter');
                        setFocus('searchFilter');
                      }}
                      disableRipple
                    >
                      <Icon
                        type="default"
                        icon={faCircleXmark}
                        fontSize={20}
                        color={theme.palette.grey[300]}
                      />
                    </IconButton>
                  ),
                },
              }}
            />
            <Box
              sx={{
                marginTop: '24px',
              }}
            >
              {tab.value === 'country' ? (
                <FormGroup>
                  {!supplierCountriesQuery.fetching &&
                    listOfCountries.length > 0 &&
                    listOfCountries.map((country) => (
                      <FormControlLabel
                        sx={{ width: '100%' }}
                        key={country.code}
                        control={
                          <Checkbox
                            name={country.code}
                            checked={selectedCountry.includes(country.code)}
                            onChange={(event) => {
                              if (event.target.checked) {
                                setSelectedCountry((selectedCountry) => [
                                  ...selectedCountry,
                                  event.target.name,
                                ]);
                              } else {
                                const filteredArray = selectedCountry.filter(
                                  (item) => item !== event.target.name
                                );
                                setSelectedCountry(filteredArray);
                              }
                            }}
                          />
                        }
                        label={
                          <>
                            <Stack direction="row" spacing={2}>
                              <img
                                src={`https://cdn.stock-sync.com/images/${country.code}.svg`}
                                width="30px"
                                alt={country.code}
                              />
                              <Typography variant="h6">
                                {t(country.code)}
                              </Typography>
                            </Stack>
                          </>
                        }
                      />
                    ))}
                </FormGroup>
              ) : (
                !supplierCategoriesQuery.fetching &&
                listOfCategory.length > 0 &&
                listOfCategory.map((category) => (
                  <FormControlLabel
                    sx={{ width: '100%' }}
                    key={category.code}
                    control={
                      <Checkbox
                        name={category.code}
                        checked={selectedCategory.includes(category.code)}
                        onChange={(event) => {
                          if (event.target.checked) {
                            setSelectedCategory((selectedCategory) => [
                              ...selectedCategory,
                              event.target.name,
                            ]);
                          } else {
                            const filteredArray = selectedCategory.filter(
                              (item) => item !== event.target.name
                            );
                            setSelectedCategory(filteredArray);
                          }
                        }}
                      />
                    }
                    label={t(category.code)}
                  />
                ))
              )}
            </Box>
          </Tab.Panel>
        ))}
      </Tab.Context>
    </Drawer>
  );
}

const LoadingConnection = () => {
  return <Skeleton animation="pulse" variant="rectangular" height={64} />;
};

interface ConnectionNotFoundProps {
  setSelectedTabs: (tabs: Array<SupplierType>) => void;
  fetching: boolean;
  resetField: UseFormResetField<{
    searchSupplier: string;
    searchFilter: string;
  }>;
}

const ConnectionNotFound = ({
  setSelectedTabs,
  fetching,
  resetField,
}: ConnectionNotFoundProps) => {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const handleClose = () => setOpen(false);

  return !fetching ? (
    <Grid container>
      <Grid
        size={{ xs: 12 }}
        sx={{
          display: 'flex',
          justifyContent: 'center',
          paddingBottom: '24px',
        }}
      >
        <Stack sx={{ alignItems: 'center' }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {t('no_connections_found')}
          </Typography>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {t('new_search')}
          </Typography>
        </Stack>
      </Grid>

      <Grid size={{ xs: 12 }} sx={{ justifyItems: 'center' }}>
        <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
          <Button variant="outlined" onClick={() => setOpen(true)}>
            {t('supplier')}
          </Button>
          <Button
            variant="text"
            onClick={() => {
              setSelectedTabs(['supplier']);
              resetField('searchSupplier');
            }}
          >
            {t('view_all_suppliers')}
          </Button>
        </Stack>
      </Grid>

      <RequestSupplierDialog open={open} handleClose={handleClose} />
    </Grid>
  ) : (
    <LoadingConnection />
  );
};

interface RequestSupplierDialogProps {
  open: boolean;
  handleClose: () => void;
}

const RequestSupplierDialog = ({
  open,
  handleClose,
}: RequestSupplierDialogProps) => {
  const { t } = useTranslation();
  const [, contactSupplier] = useMutation(ContactSupplier, {
    dataKey: 'contactSupplier',
  });
  const { control, handleSubmit } = useForm({
    defaultValues: {
      supplierEmail: '',
      cc_email: t('cc_email'),
      message: '',
    },
  });

  return (
    <Dialog maxWidth="xs" open={open} onClose={handleClose}>
      <Dialog.Title sx={{ padding: '20px 24px' }}>
        <Typography variant="h5" component="span">
          {t('contact_supplier')}
        </Typography>
      </Dialog.Title>
      <Dialog.Content sx={{ padding: '20px 24px' }}>
        <Stack spacing={3}>
          <Typography variant="body2" component="span" sx={{ pb: 3 }}>
            {t('contact_supplier_helptext')}
          </Typography>
          <TextField
            control={control}
            name="supplierEmail"
            fullWidth
            autoFocus
            label={t('supplier_email')}
          />
          <TextField
            control={control}
            name="cc_email"
            label={t('cc')}
            disabled
            fullWidth
          />
          <TextField
            control={control}
            name="message"
            fullWidth
            multiline
            label="Message"
            rows={7}
          />
        </Stack>
      </Dialog.Content>
      <Dialog.Actions>
        <Button variant="outlined" onClick={handleClose}>
          {t('cancel')}
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit(async (values) => {
            const { supplierEmail, message } = values;

            await contactSupplier(
              { supplierEmail, message },
              {
                onSuccess({ enqueueSnackbar }) {
                  enqueueSnackbar('Email sent', {
                    variant: 'success',
                  });
                },
              }
            ).finally(handleClose);
          })}
        >
          {t('send')}
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
};
