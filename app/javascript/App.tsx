import { Box, createTheme, type BoxProps } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider, type Theme } from '@mui/material/styles';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { Crisp } from 'crisp-sdk-web';
import { SnackbarProvider } from 'notistack';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import * as React from 'react';
import { I18nextProvider } from 'react-i18next';
import {
  createBrowserRouter,
  Outlet,
  redirect,
  RouterProvider,
  useLocation,
  useNavigation,
  type LoaderFunctionArgs,
} from 'react-router-dom';
import { Provider as UrqlProvider } from 'urql';

import { AppBridge } from '@/components/AppBridge';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { FeedLayout } from '@/components/FeedLayout';
import { Loading } from '@/components/Loading';
import { WebSocketProvider } from '@/contexts/WebSocketContext';
import { DashboardFeedsQuery } from '@/hooks/useDashboardFeeds';
import { DetailsFeedQuery } from '@/hooks/useDetailsFeed';
import { useIsEmbeddedApp } from '@/hooks/useIsEmbeddedApp';
import { useProviderSnackbar } from '@/hooks/useProviderSnackbar';
import { SettingsFeedQuery } from '@/hooks/useSettingsFeed';
import i18n from '@/i18n';
import { GetPricingPlans } from '@/queries/GetPricingPlans';
import { GetCurrentStore } from '@/queries/StoreQueries';
import * as routes from '@/routes';
import { theme } from '@/shared/theme';
import type { Store } from '@/types';
import { urqlClient } from '@/urqlClient';

async function loadStoreMayRedirect() {
  const response = await urqlClient
    .query<{ currentStore: Store }>(GetCurrentStore, undefined)
    .toPromise();

  if (response.error) {
    const { message, stack } = response.error;
    if (
      message.includes('Unauthorized') ||
      // TODO: have not seen error below, stale?
      String(message).toLowerCase().includes('unauthenticated')
    ) {
      window.location.href = '/accounts/sign_in';
    } else {
      throw new Error(message, { cause: stack });
    }
  }

  if (response.data) {
    const {
      provider,
      validWoocommerceStore,
      shopifyDomain,
      package: plan,
      id,
      isImpersonatingAdmin,
    } = response.data.currentStore;
    posthog.identify(id, { shop_id: id });
    Crisp.configure('11281aa5-407b-4ae2-816b-782107e8b431');
    Crisp.session.setData({
      store_name: shopifyDomain,
      platform: provider,
      priority:
        plan.includes('Custom Plan') || plan === 'Business' ? 'yes' : 'no',
      store_id: id,
      admin_store_link: `https://admin.stock-sync.com/admin/stores/${id}`,
      impersonate_admin_store_link: `https://admin.stock-sync.com/admin/user_profiles/view_user?user_id=${id}`,
    });
    if (Number(id) >= 74000 && isImpersonatingAdmin === false) {
      console.log('Start recording');
      posthog.startSessionRecording();
    }
    setTimeout(() => {
      posthog.stopSessionRecording();
    }, 1800000);
    if (
      provider === 'woocommerce' &&
      validWoocommerceStore === false &&
      window.location.pathname !== routes.woocommerceCredentials
    ) {
      return window.location.assign(routes.woocommerceCredentials);
    }
  }

  return response;
}

const loadSettingsFeed = async ({ params }: LoaderFunctionArgs) =>
  urqlClient
    .query(SettingsFeedQuery, { feedId: Number(params.id) })
    .toPromise();

const SideNavBar = React.lazy(() =>
  import('@/components/SideNavBar').then((m) => ({
    default: m.SideNavBar,
  }))
);

function Main(props: Pick<BoxProps, 'children'>) {
  return (
    <Box
      component="main"
      sx={{
        padding: '0 22px 24px 24px',
        marginLeft: 'auto',
        marginRight: 'auto',
        maxWidth: '998px',
      }}
      {...props}
    />
  );
}

function Layout() {
  const { isEmbeddedApp } = useIsEmbeddedApp();
  useProviderSnackbar();

  const navigation = useNavigation();

  return (
    <AppBridge>
      <Box sx={{ display: 'flex', position: 'relative' }}>
        {isEmbeddedApp ? <></> : <SideNavBar />}
        <Box
          sx={{
            width: '100%',
            height: '100vh',
            overflowY: 'auto',
          }}
        >
          <Main>
            <React.Suspense fallback={<Loading />}>
              {navigation.state === 'loading' ? <Loading /> : <Outlet />}
            </React.Suspense>
          </Main>
        </Box>
      </Box>
    </AppBridge>
  );
}
function PostHogPageView() {
  const location = useLocation();
  React.useEffect(() => {
    if (posthog) {
      posthog.capture('$pageview', {
        $current_url: location.pathname,
      });
      Crisp.session.pushEvent('user_clicked_chatbox', {
        page: window.location.href,
      });
    }
  }, [location]);

  return null;
}
function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <PostHogProvider
        apiKey="phc_dEX4ocByRmQJwm4op37V4QFMdAtGvusMxoa3W4s2omI"
        options={{
          api_host: 'https://us.i.posthog.com',
          disable_session_recording: true,
        }}
      >
        <UrqlProvider value={urqlClient}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <I18nextProvider i18n={i18n}>
              <SnackbarProvider
                maxSnack={3}
                autoHideDuration={5000}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
                preventDuplicate
              >
                {children}
              </SnackbarProvider>
              <PostHogPageView />
            </I18nextProvider>
          </LocalizationProvider>
        </UrqlProvider>
      </PostHogProvider>
    </ThemeProvider>
  );
}

const router = createBrowserRouter([
  {
    path: '/accounts/sign_in', // capture login route client-side
    element: <Loading />, // so user won't see error page
    loader: async () => {
      window.location.href = '/accounts/sign_in'; // force full reload
      return null;
    },
  },
  {
    // public routes, unauthenticated, no store
    path: routes.public_,
    element: (
      <Providers>
        {/* https://v5.mui.com/material-ui/customization/theming/#nesting-the-theme */}
        <ThemeProvider
          theme={(theme: Theme) =>
            createTheme({
              ...theme,
              palette: {
                ...theme.palette,
                background: {
                  default: '#fff',
                },
              },
            })
          }
        >
          {/* https://v5.mui.com/material-ui/react-css-baseline/ */}
          <CssBaseline />
          <Outlet />
        </ThemeProvider>
      </Providers>
    ),
    errorElement: (
      <Providers>
        <ErrorBoundary />
      </Providers>
    ),
    children: [
      {
        path: routes.publicPricing,
        loader: async () => {
          const plans = await (await fetch(routes.publicPricingPlans)).json();
          return plans;
        },
        async lazy() {
          const { PublicPricing } = await import('@/pages/PublicPricing');
          return { Component: PublicPricing };
        },
      },
    ],
  },

  {
    path: routes.woocommerceCredentials,
    loader: loadStoreMayRedirect,
    element: (
      <Providers>
        <Main>
          <Outlet />
        </Main>
      </Providers>
    ),
    errorElement: (
      <Providers>
        <ErrorBoundary />
      </Providers>
    ),
    children: [
      {
        index: true,
        async lazy() {
          const { WoocommerceCredentials } = await import(
            '@/pages/WoocommerceCredentials'
          );
          return { Component: WoocommerceCredentials };
        },
      },
    ],
  },
  {
    path: routes.home,
    loader: loadStoreMayRedirect,
    element: (
      <Providers>
        <WebSocketProvider>
          <Layout />
        </WebSocketProvider>
      </Providers>
    ),
    errorElement: (
      <Providers>
        <ErrorBoundary />
      </Providers>
    ),
    children: [
      {
        index: true,
        loader: async () => {
          const storeQuery = await urqlClient
            .query(GetCurrentStore, undefined)
            .toPromise();

          if (storeQuery.data) {
            // vital for new customers, urqlClient cache has feeds [].
            // after createNewFeed, cache concat new feed to []
            const feedsQuery = await urqlClient
              .query(DashboardFeedsQuery, undefined)
              .toPromise();
            const userProfiles = feedsQuery.data?.getAllFeeds ?? [];
            const hasOnlySnappy =
              userProfiles.filter(({ fastTrack }) => !fastTrack).length === 0;

            if (hasOnlySnappy) {
              return redirect(routes.quickGuide);
            }
          }
          return storeQuery;
        },
        async lazy() {
          const { Dashboard } = await import('@/pages/Dashboard');
          return { Component: Dashboard };
        },
      },
      {
        path: routes.productChangesHistory,
        async lazy() {
          const { ProductChangesHistory } = await import(
            '@/pages/ProductChangesHistory'
          );
          return { Component: ProductChangesHistory };
        },
      },
      {
        path: routes.newFeed,
        async lazy() {
          const { CreateFeed } = await import('@/pages/CreateFeed');
          return { Component: CreateFeed };
        },
      },
      {
        path: routes.newConnection,
        async lazy() {
          const { SelectConnection } = await import('@/pages/SelectConnection');
          return { Component: SelectConnection };
        },
      },
      {
        path: routes.feedDetail,
        element: <FeedLayout />,
        children: [
          {
            index: true,
            loader: async ({ params }: LoaderFunctionArgs) =>
              urqlClient
                .query(DetailsFeedQuery, { feedId: Number(params.id) })
                .toPromise(),
            async lazy() {
              const { FeedDetail } = await import('@/pages/FeedDetail');
              return { Component: FeedDetail };
            },
          },
          {
            path: 'select_connection',
            async lazy() {
              const { SelectConnection } = await import(
                '@/pages/SelectConnection'
              );
              return { Component: SelectConnection };
            },
          },
          {
            path: 'edit/feed_manager',
            loader: loadSettingsFeed,
            async lazy() {
              const { FeedManager } = await import('@/pages/FeedManager');
              return { Component: FeedManager };
            },
          },
          {
            path: 'edit/mapping',
            loader: loadSettingsFeed,
            async lazy() {
              const { FeedMapping } = await import('@/pages/FeedMapping');
              return { Component: FeedMapping };
            },
          },
          {
            path: 'edit/filter',
            loader: loadSettingsFeed,
            async lazy() {
              const { FeedFilter } = await import('@/pages/FeedFilter');
              return { Component: FeedFilter };
            },
          },
          {
            path: 'edit/advanced_settings',
            loader: loadSettingsFeed,
            async lazy() {
              const { FeedAdvancedSettings } = await import(
                '@/pages/FeedAdvancedSettings'
              );
              return { Component: FeedAdvancedSettings };
            },
          },
          {
            path: routes.productTroubleshoot,
            async lazy() {
              const { ProductTroubleshoot } = await import(
                '@/pages/ProductTroubleshoot'
              );
              return { Component: ProductTroubleshoot };
            },
          },
        ],
      },
      {
        path: routes.notifications,
        async lazy() {
          const { Notification } = await import('@/pages/Notification');
          return { Component: Notification };
        },
      },
      {
        path: routes.advancePreferences,
        async lazy() {
          const { AdvancedPreference } = await import(
            '@/pages/AdvancedPreference'
          );
          return { Component: AdvancedPreference };
        },
      },
      {
        path: routes.feedback,
        async lazy() {
          const { Feedback } = await import('@/pages/Feedback');
          return { Component: Feedback };
        },
      },
      {
        path: routes.billing,
        children: [
          {
            index: true,
            loader: async () => {
              const storeQuery = await urqlClient
                .query(GetCurrentStore, undefined)
                .toPromise();

              if (storeQuery.data) {
                const { isExpired, status } = storeQuery.data.currentStore;

                if (isExpired && status === 'trial') {
                  return redirect(routes.billingSubscription);
                }
              }
              return storeQuery;
            },
            async lazy() {
              const { Billing } = await import('@/pages/Billing');
              return { Component: Billing };
            },
          },
          {
            path: routes.billingSubscription,
            loader: async () => {
              const plansQuery = await urqlClient
                .query(GetPricingPlans, undefined)
                .toPromise();
              return plansQuery.data?.getPricingPlans.packages;
            },
            async lazy() {
              const { Subscription } = await import(
                '@/pages/Billing/V5/Subscription'
              );
              return { Component: Subscription };
            },
          },
          {
            path: routes.billingBuyCredit,
            async lazy() {
              const { PurchaseCredit } = await import(
                '@/pages/Billing/V5/PurchaseCredit'
              );
              return { Component: PurchaseCredit };
            },
          },
        ],
      },
      {
        path: routes.quickGuide,
        async lazy() {
          const { QuickGuide } = await import('@/pages/QuickGuide');
          return { Component: QuickGuide };
        },
      },
      {
        path: `${routes.storyBook}/*`,
        loader: async () => {
          // local dev only, redirect home if prod
          return process.env.NODE_ENV === 'production'
            ? redirect(routes.home)
            : null;
        },
        async lazy() {
          const { Storybook } = await import('@/pages/Storybook');
          return { Component: Storybook };
        },
      },
    ],
  },
  {
    path: '*',
    loader: async () => {
      return redirect(routes.home);
    },
  },
]);

export function App() {
  return <RouterProvider router={router} fallbackElement={<Loading />} />;
}
