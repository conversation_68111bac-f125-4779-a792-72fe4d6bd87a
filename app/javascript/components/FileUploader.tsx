import { faArrowUpFromBracket } from '@fortawesome/pro-light-svg-icons';
import { Box, Stack, Typography, useTheme } from '@mui/material';
import { useSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import { useDropzone, type FileError } from 'react-dropzone';
import { useTranslation } from 'react-i18next';

import { Alert } from '@/components/Alert';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { Link } from '@/components/Link';
import { useAlert } from '@/hooks/useAlert';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useMutation } from '@/hooks/useMutation';
import { UploadFile } from '@/queries/FeedSettings';
import { getMaxFileSize } from '@/shared/util';
import type { UserProfile, UserProfileStatus } from '@/types';

interface FileUploaderProps {
  feed: Pick<
    UserProfile,
    'enabled' | 'feedFileLocation' | 'id' | 'sourceFileFileName'
  >;
  showHelptext?: boolean;
  processNow?: boolean;
  runAsSample?: boolean;
  onUploadSuccess?: (props: { file: File; status: UserProfileStatus }) => void;
  onUploadAccepted?: (props: { status: UserProfileStatus }) => void;
  onUploadError?: (props: { status: UserProfileStatus }) => void;
}

export function FileUploader({
  showHelptext = true,
  processNow = false,
  runAsSample = false,
  onUploadSuccess,
  onUploadAccepted,
  onUploadError,
  feed,
}: FileUploaderProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  const { currentStore } = useCurrentStore();
  const { currentPlan } = currentStore;
  const [, uploadFile] = useMutation(UploadFile, {
    dataKey: 'uploadFile',
  });
  const [currentFile, setCurrentFile] = useState(feed.sourceFileFileName);
  const maxFileSize = getMaxFileSize(currentPlan.key);
  const alert = useAlert();

  const { fileRejections, getRootProps, getInputProps } = useDropzone({
    maxSize: maxFileSize * 1048576, // MB to bytes
    maxFiles: 1,
    multiple: false,
    disabled: !feed.enabled,
    // https://react-dropzone.js.org/#section-accepting-specific-file-types
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'application/vnd.ms-excel': ['.xls'],
      'application/xml': ['.xml'],
      'text/xml': ['.xml'],
      'application/zip': ['.zip'],
      'text/plain': ['.txt'],
      'application/json': ['.json'],
    },
    // `accept` whitelist narrows to .ext above, validator further checks for multiple .ext occurrences
    validator: (file) => {
      const regexMultiExtensions = /(\.[a-z]+)\.[a-z]+/i; // catches multiple exts e.g. dump.php.csv
      if (regexMultiExtensions.test(file.name)) {
        return {
          code: 'file-multiple-ext',
          message: `Invalid file type: ${file.name}`,
        } satisfies FileError;
      }

      return null;
    },
    onDropAccepted: async (files) => {
      const [file] = files;
      console.debug(file);
      onUploadAccepted?.({ status: 'queuing' });
      uploadFile(
        { file, feedId: +feed.id, processNow, runAsSample },
        {
          onSuccess({ data, enqueueSnackbar }) {
            onUploadSuccess?.({ file, status: data.status });
            setCurrentFile(file.name);
            enqueueSnackbar(t('file_upload_success'), {
              variant: 'success',
            });
          },
          onError({ error }) {
            onUploadError?.({ status: 'start' });
            alert.setMessage(t(error));
          },
        }
      );
    },
  });

  useEffect(() => {
    if (fileRejections && fileRejections.length > 0) {
      const [{ errors }] = fileRejections;
      const [firstError] = errors;

      let message = '';
      switch (firstError.code) {
        case 'file-multiple-ext':
          message = `${t('File has multiple extensions')}: ${firstError.message}`;
          break;
        case 'file-invalid-type':
          message = `${t('file_upload_invalid')}`;
          break;
        case 'file-too-large':
          message = `${t(
            'file_upload_limit'
          )} ${maxFileSize} MB. Please try a smaller file size <NAME_EMAIL> for assistance.`;
          break;
        default:
          message = `${t('file_upload_fail')}. ${t('assistance_contact')}`;
      }
      alert.setMessage(message);
    }
  }, [t, enqueueSnackbar, fileRejections, maxFileSize, alert]);

  return (
    <>
      {alert.message.length > 0 && <Alert>{alert.message}</Alert>}
      <Grid container sx={{ width: '100%' }}>
        {/* File upload component */}
        <Grid size={{ xs: 12 }}>
          <Box
            sx={{
              border: `1px solid ${theme.palette.grey[300]}`,
              borderRadius: '8px',
              borderStyle: 'dashed',
            }}
          >
            <div
              {...getRootProps({
                className: 'dropzone',
                style: {
                  padding: '16px',
                  textAlign: 'center',
                  cursor: 'pointer',
                  width: '100%',
                },
              })}
            >
              <input {...getInputProps()} />
              <Stack
                direction="row"
                sx={{
                  justifyContent: 'center',
                  alignItems: 'flex-start',
                }}
              >
                <Icon
                  type="default"
                  icon={faArrowUpFromBracket}
                  style={{
                    fontSize: '14px',
                    color: theme.palette.blue[100],
                    margin: '4px 8px 0px',
                  }}
                />
                <Typography
                  variant="body1"
                  sx={{ color: theme.palette.grey[300] }}
                >
                  {t('click_or_drop_upload')}
                </Typography>
              </Stack>
            </div>

            {/* Show current file name */}
          </Box>
          {currentFile && (
            <Typography
              variant="body2"
              sx={{ margin: '16px 0 0 0', textAlign: 'center' }}
            >
              {t('current_file')}{' '}
              <Link
                to={feed.feedFileLocation ?? ''}
                sx={{ wordWrap: 'break-word' }}
              >
                {currentFile}
              </Link>
            </Typography>
          )}
          {/* Show help text (only in feed manager) */}
          <Grid
            size={{ xs: 12 }}
            sx={{ textAlignLast: 'center', paddingTop: '16px' }}
          >
            {showHelptext && (
              <Typography variant="body2" sx={{ mt: theme.spacing(2) }}>
                {t('file_upload_can_done_later')}
              </Typography>
            )}
          </Grid>
        </Grid>
      </Grid>
    </>
  );
}
