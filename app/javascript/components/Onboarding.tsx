import {
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faChevronUp,
} from '@fortawesome/pro-light-svg-icons';
import {
  Box,
  CardContent,
  CardHeader,
  Chip,
  CircularProgress,
  Collapse,
  Divider,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { match } from 'ts-pattern';
import { useQuery } from 'urql';

import { Alert } from '@/components/Alert';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { CustomAppBar } from '@/components/CustomAppBar';
import { FeedLimitDialog } from '@/components/FeedLimitDialog';
import { Grid } from '@/components/Grid';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { Tab } from '@/components/Tab';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDashboardFeeds } from '@/hooks/useDashboardFeeds';
import { useMatches } from '@/hooks/useMatches';
import { useMutation } from '@/hooks/useMutation';
import { useOnboardingStep } from '@/hooks/useOnboardingStep';
import { CreateNewFeed } from '@/queries/FeedSettings';
import { GetSuppliers } from '@/queries/GetSuppliers';
import { PreCheck } from '@/queries/StoreMutations';
import * as routes from '@/routes';
import { renderPropsByFeedType } from '@/shared/feedTypes';
import {
  connectQuickbooks,
  exportQuickguide,
  importQuickguide,
  removeQuickguide,
  updateQuickguide,
} from '@/shared/images';
import type { HumanizedFeedType } from '@/types';

type ConnectionLevel = 'beginner' | 'advanced';

export function Onboarding() {
  const { t } = useTranslation();
  const { currentStore } = useCurrentStore();
  const navigate = useNavigate();
  const { feeds } = useDashboardFeeds();

  const theme = useTheme();
  const [, preCheck] = useMutation(PreCheck, {
    dataKey: 'preCheck',
  });
  const ref = React.useRef<HTMLDivElement | null>(null);
  const matchesSm = useMatches('sm');
  const [feedLimitError, setFeedLimitError] = React.useState({
    openDialog: false,
    errorMessage: '',
  });
  const step = useOnboardingStep();

  console.log('step dashboard page', step);

  const [connectionLevel, setConnectionLevel] =
    React.useState<ConnectionLevel>('beginner');
  const [selectedFeedType, setSelectedFeedType] =
    React.useState<HumanizedFeedType>('update');
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0);

  console.log('connectionLevel', connectionLevel);
  console.log('selectedFeedType', selectedFeedType);

  const quickGuides = [
    {
      url: 'https://www.youtube.com/watch?v=n2OLgMGsgL0',
      image: updateQuickguide,
      label: 'Update Guide',
      title: 'Update',
      type: 'update',
      description: 'update_description',
      iconProps: { color: '#ac9af1', className: 'fak fa-update1' },
    },
    {
      url: 'https://www.youtube.com/watch?v=kYSQRFaGmfw',
      image: exportQuickguide,
      label: 'Export Guide',
      title: 'Export',
      type: 'export',
      description: 'export_description',
      iconProps: { color: '#8dcfd0', className: 'fak fa-export2' },
    },
    {
      url: 'https://www.youtube.com/watch?v=UX_aGwEM9X0',
      image: importQuickguide,
      label: 'Import Guide',
      title: 'Add',
      type: 'import',
      description: 'import_description',
      iconProps: { color: '#fed27b', className: 'fak fa-add1' },
    },
    {
      url: 'https://www.youtube.com/watch?v=ze4wXrpnuJU',
      image: removeQuickguide,
      label: 'Remove Guide',
      title: 'Remove',
      type: 'remove',
      description: 'remove_description',
      iconProps: { color: '#e8af94', className: 'fak fa-cleanup1' },
    },
  ];

  const feedType = React.useMemo(() => {
    const updateFeedOnly = [renderPropsByFeedType['update']];
    const ekmAvailableFeedType = [
      renderPropsByFeedType['update'],
      renderPropsByFeedType['import'],
      renderPropsByFeedType['remove'],
    ];
    const squareAvailableFeedType = [
      renderPropsByFeedType['update'],
      renderPropsByFeedType['import'],
    ];
    const feedTypeForOtherPlatform = [
      renderPropsByFeedType['update'],
      renderPropsByFeedType['export'],
      renderPropsByFeedType['import'],
      renderPropsByFeedType['remove'],
    ];
    if (['squarespace', 'quickbooks'].includes(currentStore.provider)) {
      return updateFeedOnly;
    } else if (['ekm'].includes(currentStore.provider)) {
      return ekmAvailableFeedType;
    } else if (['square', 'prestashop'].includes(currentStore.provider)) {
      return squareAvailableFeedType;
    } else {
      return feedTypeForOtherPlatform;
    }
  }, [currentStore.provider]);

  return (
    <>
      <CustomAppBar
        ref={ref}
        title={t('what_you_like_to_achieve')}
        description={t('complete_first_feed')}
        iconProps={{ type: 'kit', className: 'fak fa-home2' }}
        btnGroup={
          currentStore.provider === 'quickbooks' &&
          !currentStore.quickbooksRealmId && (
            <Button
              variant="text"
              sx={{ padding: '0px' }}
              onClick={() =>
                (window.location.href = routes.quickbooksAuthenticate(
                  currentStore.id
                ))
              }
            >
              <img src={connectQuickbooks} style={{ height: '35px' }} />
            </Button>
          )
        }
      />
      <Stack
        direction={matchesSm ? 'column' : 'row'}
        spacing={2}
        sx={{ marginBottom: '40px' }}
      >
        <Card
          sx={{
            padding: 0,
            maxWidth: '100%',
          }}
        >
          <CardHeader
            title={
              <Box sx={{ display: 'flex', fontSize: '14px' }}>
                {step.currentStep} of 5 &nbsp;
                <Box sx={{ color: theme.palette.grey[300] }}>task complete</Box>
              </Box>
            }
          />

          {[
            { step: 0, text: 'connect_product_feed' },
            { step: 1, text: 'select_data_source' },
            { step: 2, text: 'manage_feed' },
            { step: 3, text: 'update_quantity_or_price' },
            { step: 4, text: 'set_up_automation' },
          ].map((a) => (
            <OnboardingCard
              key={a.step}
              title={t(a.text)}
              collapseChildren={match({
                step: a.step,
              })
                .with({ step: 0 }, () => (
                  <>
                    <Typography
                      variant="body1"
                      sx={{ color: theme.palette.grey[300] }}
                    >
                      {t('start_by_selecting')}
                    </Typography>
                    <Stack
                      direction="row"
                      spacing="8px"
                      sx={{ marginTop: '20px' }}
                    >
                      <Grid container spacing={2}>
                        {feedType.map((ft) => (
                          <Grid size={matchesSm ? 12 : 6} key={ft.type}>
                            <Button
                              sx={{ width: '100%' }}
                              variant={
                                selectedFeedType === ft.type
                                  ? 'contained'
                                  : 'outlined'
                              }
                              onClick={async () => {
                                preCheck(
                                  {
                                    feedType:
                                      ft.type === 'export' ? 'update' : ft.type,
                                  },
                                  {
                                    onSuccess() {
                                      setSelectedFeedType(ft.type);
                                      step.setCurrentStep(1);
                                    },
                                    onError({ error }) {
                                      setFeedLimitError({
                                        openDialog: true,
                                        errorMessage: error,
                                      });
                                    },
                                  }
                                );
                              }}
                            >
                              {ft.title} products
                            </Button>
                          </Grid>
                        ))}
                      </Grid>
                    </Stack>
                  </>
                ))
                .with({ step: 1 }, () => (
                  <React.Suspense
                    fallback={
                      <>
                        <Typography
                          variant="body1"
                          sx={{
                            color: theme.palette.grey[300],
                            marginBottom: '20px',
                          }}
                        >
                          {t('how_does_your_supplier_share_data')}
                        </Typography>
                        <CircularProgress color="inherit" size={20} />
                      </>
                    }
                  >
                    <SelectDataSource
                      selectedFeedType={selectedFeedType}
                      connectionLevel={connectionLevel}
                      setConnectionLevel={setConnectionLevel}
                    />
                  </React.Suspense>
                ))
                .with({ step: 2 }, () => (
                  <>
                    <Typography
                      variant="body1"
                      sx={{
                        color: theme.palette.grey[300],
                        marginBottom: '20px',
                      }}
                    >
                      {t('setting_of_the_feed')}
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() =>
                        navigate(routes.feedManagerRoute(feeds[0].id))
                      }
                    >
                      {t('go_setting')}
                    </Button>
                  </>
                ))
                .with({ step: 3 }, () => (
                  <>
                    <Typography
                      variant="body1"
                      sx={{
                        color: theme.palette.grey[300],
                        marginBottom: '20px',
                      }}
                    >
                      {t('map_field_update_quantity_and_price')}
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => navigate(routes.mappingRoute(feeds[0].id))}
                    >
                      {t('go_setting')}
                    </Button>
                  </>
                ))
                .otherwise(() => (
                  <>
                    <Typography
                      variant="body1"
                      sx={{
                        color: theme.palette.grey[300],
                        marginBottom: '20px',
                      }}
                    >
                      {t('review_settings_set_schedule')}
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() =>
                        navigate(routes.feedDetailRoute(feeds[0].id))
                      }
                    >
                      {t('go_setting')}
                    </Button>
                  </>
                ))}
              open={step.currentStep === a.step}
            />
          ))}
        </Card>
        <Box sx={{ maxWidth: '400px', width: '100%' }}>
          {step.currentStep === 0 ? (
            <Card>
              <Stack
                direction="row"
                spacing="8px"
                sx={{
                  alignItems: 'flex-start',
                  marginBottom: '16px',
                }}
              >
                <Box
                  sx={{
                    color: quickGuides[currentImageIndex].iconProps.color,
                    paddingTop: '8px',
                  }}
                >
                  <Icon
                    type="kit"
                    fontSize={24}
                    className={
                      quickGuides[currentImageIndex].iconProps.className
                    }
                  />
                </Box>
                <Box sx={{ width: '100%' }}>
                  <Typography sx={{ fontSize: '16px' }} component="div">
                    {t(quickGuides[currentImageIndex].title)}
                  </Typography>
                  {!matchesSm && (
                    <Typography
                      variant="body1"
                      sx={{
                        color: theme.palette.grey[300],
                        fontWeight: 400,
                      }}
                    >
                      {match([
                        currentStore.provider,
                        quickGuides[currentImageIndex].type,
                      ])
                        .with(['woocommerce', 'remove'], () =>
                          t('hide_unwanted_products')
                        )
                        .with(['bigcommerce', 'remove'], () =>
                          t('archive_description_new_feed')
                        )
                        .with(['wix', 'remove'], () =>
                          t('archive_description_new_feed')
                        )
                        .otherwise(() =>
                          t(quickGuides[currentImageIndex].description)
                        )}
                    </Typography>
                  )}
                </Box>
              </Stack>
              <Stack
                direction="row"
                spacing={2}
                sx={{
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <IconButton
                  onClick={() =>
                    setCurrentImageIndex(
                      (prev) =>
                        (prev - 1 + quickGuides.length) % quickGuides.length
                    )
                  }
                >
                  <Icon type="default" icon={faChevronLeft} />
                </IconButton>
                <Box sx={{ position: 'relative' }}>
                  <Link to={quickGuides[currentImageIndex].url} target="_blank">
                    <img
                      src={quickGuides[currentImageIndex].image}
                      width="250"
                      alt={quickGuides[currentImageIndex].label}
                    />
                  </Link>
                </Box>
                <IconButton
                  onClick={() =>
                    setCurrentImageIndex(
                      (prev) => (prev + 1) % quickGuides.length
                    )
                  }
                >
                  <Icon type="default" icon={faChevronRight} />
                </IconButton>
              </Stack>
            </Card>
          ) : (
            <></>
          )}
        </Box>
      </Stack>

      <FeedLimitDialog
        open={feedLimitError.openDialog}
        handleClose={() => {
          setFeedLimitError({ openDialog: false, errorMessage: '' });
        }}
        errorMessage={feedLimitError.errorMessage}
      />
    </>
  );
}

interface SelectDataSourceProps {
  selectedFeedType: HumanizedFeedType;
  connectionLevel: ConnectionLevel;
  setConnectionLevel: (a: ConnectionLevel) => void;
}

const SelectDataSource = ({
  selectedFeedType,
  connectionLevel,
  setConnectionLevel,
}: SelectDataSourceProps) => {
  const [collapseConnection, setCollapseConnection] = React.useState(false);
  const [selectedConnection, setSelectedConnection] = React.useState('');
  const [saving, setSaving] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState('');
  const theme = useTheme();
  const navigate = useNavigate();
  const matchesSm = useMatches('sm');
  const { t } = useTranslation();
  const { currentStore, refetch } = useCurrentStore();
  const step = useOnboardingStep();

  const [, createNewFeed] = useMutation(CreateNewFeed, {
    dataKey: 'createNewFeed',
  });

  const [suppliersQuery] = useQuery({
    query: GetSuppliers,
    variables: { feedType: selectedFeedType, connectionLevel: connectionLevel },
  });

  const suppliers = React.useMemo(() => {
    const connections = suppliersQuery.fetching
      ? []
      : suppliersQuery.data?.getSuppliers;

    if (connections) {
      setSelectedConnection(connections[0].id);
    }

    return connections;
  }, [suppliersQuery]);

  // Function to create feed from template
  const createFromTemplate = async (
    supplierId?: string,
    sourceName?: string
  ) => {
    const connectionId = supplierId || selectedConnection;
    if (!connectionId) return;

    setSaving(true);
    const feedName = `${sourceName} - ${currentStore.profilesCount + 1}`;

    createNewFeed(
      {
        supplierId: connectionId,
        feedName,
        feedType: selectedFeedType,
      },
      {
        onSuccess({ data: feed, enqueueSnackbar, navigate }) {
          enqueueSnackbar(`Feed ${feed.profileName} created`, {
            variant: 'success',
          });
          refetch();

          // wait for new feed after cache update
          setTimeout(() => {
            step.setCurrentStep(2);
            // Navigate to feed manager for configuration
            navigate(routes.feedManagerRoute(feed.id));
          }, 100);
        },
        onError({ error }) {
          setErrorMessage(t(error));
          setTimeout(() => {
            setErrorMessage('');
          }, 5000);
        },
      }
    ).finally(() => setSaving(false));
  };

  console.log('suppliers', suppliers);
  console.log('selectedConnection', selectedConnection);

  return (
    <>
      <Typography
        variant="body1"
        sx={{
          color: theme.palette.grey[300],
          marginBottom: '20px',
        }}
      >
        {t('how_does_your_supplier_share_data')}
      </Typography>
      <Tab.Context value={connectionLevel}>
        <Tab.List
          sx={{ paddingLeft: 0 }}
          borderBottom
          onChange={(_, tab) => {
            setConnectionLevel(tab);
          }}
        >
          {[
            { value: 'beginner', text: t('beginner') },
            { value: 'advanced', text: t('advanced') },
          ].map((tab) => (
            <Tab
              key={tab.value}
              value={tab.value}
              size="small"
              label={tab.text}
            />
          ))}
        </Tab.List>
        <Tab.Panel value={connectionLevel}>
          {(suppliers || []).map((source) => (
            <Chip
              key={source.id}
              label={source.name}
              variant={selectedConnection === source.id ? 'filled' : 'outlined'}
              color={selectedConnection === source.id ? 'primary' : 'default'}
              disabled={saving}
              onClick={async () => {
                setSelectedConnection(source.id);
                await createFromTemplate(source.id, source.name);
              }}
              sx={{
                '&:hover': { background: 'black' },
                fontSize: '16px',
                margin: '8px',
              }}
            />
          ))}
        </Tab.Panel>
      </Tab.Context>
      {errorMessage && (
        <Alert severity="error" sx={{ marginTop: '16px' }}>
          {errorMessage}
        </Alert>
      )}
      <Divider />
      <Typography variant="h6" sx={{ marginTop: '20px', fontWeight: 600 }}>
        Not sure? Find more as{' '}
        <Button
          variant="text"
          sx={{ padding: '0px' }}
          endIcon={
            <Icon
              type="default"
              icon={collapseConnection ? faChevronUp : faChevronDown}
              fontSize={16}
            />
          }
          onClick={() => setCollapseConnection(!collapseConnection)}
        >
          below
        </Button>
      </Typography>
      <Collapse in={collapseConnection}>
        <Stack
          direction={matchesSm ? 'column' : 'row'}
          spacing="8px"
          sx={{ marginTop: '16px' }}
        >
          <Button
            onClick={() =>
              navigate(routes.newConnection, {
                state: {
                  action: selectedFeedType,
                  type: 'connection',
                },
              })
            }
          >
            {t('search_connection')}
          </Button>

          <Button
            variant="outlined"
            onClick={() =>
              navigate(routes.newConnection, {
                state: {
                  action: selectedFeedType,
                  type: 'supplier',
                },
              })
            }
          >
            {t('find_a_suppliers')}
          </Button>
        </Stack>
      </Collapse>
    </>
  );
};

interface OnboardingCardProps {
  title: string;
  collapseChildren: React.ReactNode;
  open: boolean;
}

const OnboardingCard = ({
  title,
  collapseChildren,
  open,
}: OnboardingCardProps) => {
  const theme = useTheme();

  return (
    <>
      <Box
        sx={{
          borderRadius: '8px',
          padding: '8px',
          margin: '0',
          border: '2px solid transparent',
          '[data-icon]': {
            color: theme.palette.link,
          },
          ...(open
            ? {
                backgroundColor: '#8898aa1a',
                borderColor: theme.palette.primary.main,
                border: '2px solid',
              }
            : {}),
        }}
      >
        <CardContent
          sx={{
            padding: '0',
            height: '100%',
            '&:last-child': {
              paddingBottom: '0px',
              borderBottom: 'unset',
            },
          }}
        >
          <Box sx={{ padding: '16px 8px' }}>
            <Stack
              direction="row"
              spacing="16px"
              sx={{
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}
            >
              <Icon
                data-icon
                type="default"
                icon={open ? faChevronUp : faChevronDown}
                style={{ marginTop: '2px', fontSize: '20px' }}
              />
              <Box sx={{ width: '100%' }}>
                <Typography
                  variant="h6"
                  sx={{
                    color: theme.palette.common.black,
                    fontWeight: 600,
                    marginBottom: '4px',
                  }}
                >
                  {title}
                </Typography>
                <Collapse in={open}>{collapseChildren}</Collapse>
              </Box>
            </Stack>
          </Box>
        </CardContent>
      </Box>
    </>
  );
};
