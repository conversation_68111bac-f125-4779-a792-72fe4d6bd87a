import {
  DatePicker as MuiDatePicker,
  type DatePickerProps as MuiDatePickerProps,
} from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';
import {
  useController,
  useFormContext,
  type FieldValues,
  type UseControllerProps,
} from 'react-hook-form';

import { ErrorHelperText } from '@/components/ErrorHelperText';

export interface DatePickerProps<TFieldValues extends FieldValues = FieldValues>
  extends Pick<UseControllerProps<TFieldValues>, 'name' | 'control'>,
    Omit<MuiDatePickerProps<Dayjs>, 'name' | 'ref' | 'disabled'> {}

export function DatePicker<TFieldValues extends FieldValues>({
  name,
  control,
  label,
  onChange,
  ...props
}: DatePickerProps<TFieldValues>) {
  const { field, formState: controllerFormState } = useController({
    name,
    control,
  });

  const { getFieldState, formState: contextFormState } =
    useFormContext<TFieldValues>() || {};

  const { errors } = contextFormState || controllerFormState;

  const hasNestedError = getFieldState?.(name)?.invalid ?? false;

  const hasNormalError = Boolean(errors[name]);

  return (
    <MuiDatePicker
      {...props}
      name={name}
      sx={{ width: '100%' }}
      label={label}
      disableFuture
      value={dayjs(field.value)}
      onChange={(value, context) => {
        field.onChange(dayjs(value), context);
        onChange?.(dayjs(value), context);
      }}
      slotProps={{
        textField: {
          variant: 'outlined',
          error: hasNestedError || hasNormalError,
          helperText: <ErrorHelperText name={name} errors={errors} />,
        },
      }}
    />
  );
}
