import {
  faArrowUpRightAndArrowDownLeftFromCenter,
  faArrowUpRightFromSquare,
  faPlus,
  faTrash,
} from '@fortawesome/pro-light-svg-icons';
import { Box, MenuItem, Stack, Typography, useTheme } from '@mui/material';
import differenceBy from 'lodash/differenceBy';
import * as React from 'react';
import {
  useFieldArray,
  useFormContext,
  type UseFieldArrayReturn,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'urql';

import { Autocomplete } from '@/components/Autocomplete';
import { Button } from '@/components/Button';
import { Icon } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { PopoverConfirm } from '@/components/PopoverConfirm';
import { QuickEditDialog } from '@/components/QuickEditDialog';
import { Switch } from '@/components/Switch';
import { Tags } from '@/components/Tags';
import { TextField } from '@/components/TextField';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import {
  GetBigcommerceBrands,
  GetCollections,
  GetCollectionsFromWix,
  GetWoocommerceCategories,
  GetWoocommerceTags,
} from '@/queries/Collections';
import { GetCategories, GetVendors } from '@/queries/Users';
import { commaNotInsideQuotes, compareRegexAsString } from '@/shared/util';
import type { StoreFilterCondition, StoreFilterKey } from '@/types';

import { type StoreFiltersFormValues } from './schema';

type FilterOptions = Array<{
  key: StoreFilterKey;
  value: StoreFilterKey;
}>;

interface CollectionOption {
  id: string;
  title: string;
}

type UseFieldArrayReturnProps = UseFieldArrayReturn<
  StoreFiltersFormValues,
  'storeFilters'
>;

const wixfilterOptions: FilterOptions = [
  { key: 'product_type', value: 'product_type' },
  { key: 'collection', value: 'collection' },
  { key: 'published', value: 'published' },
  { key: 'sku', value: 'sku' },
  { key: 'brand', value: 'brand' },
];

const shopifyfilterOptions: FilterOptions = [
  { key: 'vendor', value: 'vendor' },
  { key: 'tags', value: 'tags' },
  { key: 'product_type', value: 'product_type' },
  { key: 'title', value: 'title' },
  { key: 'barcode', value: 'barcode' },
  { key: 'collection', value: 'collection' },
  { key: 'published', value: 'published' },
  { key: 'sku', value: 'sku' },
  { key: 'status', value: 'status' },
  { key: 'inventory_quantity', value: 'inventory_quantity' },
  { key: 'product_category_id', value: 'product_category_id' },
];

const woocommercefilterOptions: FilterOptions = [
  { key: 'tags', value: 'tags' },
  { key: 'category', value: 'category' },
  { key: 'sku', value: 'sku' },
  { key: 'brand', value: 'brand' },
];

const bigcommercefilterOptions: FilterOptions = [
  { key: 'brand', value: 'brand' },
  { key: 'sku', value: 'sku' },
  { key: 'bpn', value: 'bpn' },
  { key: 'published', value: 'published' },
];

const squarespacefilterOptions: FilterOptions = [
  { key: 'tags', value: 'tags' },
  { key: 'product_type', value: 'product_type' },
];

const filterByPlatform = {
  shopify: shopifyfilterOptions,
  wix: wixfilterOptions,
  woocommerce: woocommercefilterOptions,
  bigcommerce: bigcommercefilterOptions,
  squarespace: squarespacefilterOptions,
};

const equalsOption: StoreFilterCondition = {
  operator: 'equals',
  value: '',
};

const notEqualsOption: StoreFilterCondition = {
  operator: 'not_equals',
  value: '',
};

const quantityOption: StoreFilterCondition = {
  operator: 'greater_than',
  value: '',
};

const wixDefaultStoreFilters: Partial<
  Record<StoreFilterKey, StoreFilterCondition[]>
> = {
  product_type: [equalsOption, notEqualsOption],
  collection: [
    { ...equalsOption, _value: [] },
    { ...notEqualsOption, _value: [] },
  ],
  published: [equalsOption],
  sku: [equalsOption, notEqualsOption],
  brand: [equalsOption, notEqualsOption],
};

const woocommerceDefaultStoreFilters: Partial<
  Record<StoreFilterKey, StoreFilterCondition[]>
> = {
  tags: [
    { ...equalsOption, _value: [] },
    { ...notEqualsOption, _value: [] },
  ],
  category: [
    { ...equalsOption, _value: [] },
    { ...notEqualsOption, _value: [] },
  ],
  sku: [equalsOption, notEqualsOption],
  brand: [equalsOption, notEqualsOption],
};

const bigcommerceDefaultStoreFilters: Partial<
  Record<StoreFilterKey, StoreFilterCondition[]>
> = {
  brand: [
    { ...equalsOption, _value: [] },
    { ...notEqualsOption, _value: [] },
  ],
  sku: [equalsOption, notEqualsOption],
  bpn: [equalsOption, notEqualsOption],
  published: [equalsOption],
};

const squarespaceDefaultStoreFilters: Partial<
  Record<StoreFilterKey, StoreFilterCondition[]>
> = {
  tags: [equalsOption, notEqualsOption],
  product_type: [equalsOption, notEqualsOption],
};

const defaultStoreFilters: Partial<
  Record<StoreFilterKey, StoreFilterCondition[]>
> = {
  vendor: [
    { ...equalsOption, _value: [] },
    { ...notEqualsOption, _value: [] },
  ],
  tags: [equalsOption, notEqualsOption],
  product_type: [equalsOption, notEqualsOption],
  title: [equalsOption, notEqualsOption],
  // barcode: [equalsOption, notEqualsOption],
  collection: [
    { ...equalsOption, _value: [] },
    { ...notEqualsOption, _value: [] },
  ],
  published: [equalsOption],
  sku: [equalsOption, notEqualsOption],
  status: [{ ...equalsOption, _value: [] }],
  barcode: [equalsOption, notEqualsOption],
  inventory_quantity: [quantityOption],
  product_category_id: [{ ...equalsOption, _value: [] }],
};

const platformDefaultStoreFilters = {
  shopify: defaultStoreFilters,
  wix: wixDefaultStoreFilters,
  woocommerce: woocommerceDefaultStoreFilters,
  bigcommerce: bigcommerceDefaultStoreFilters,
  squarespace: squarespaceDefaultStoreFilters,
};

const platformSupportMultipleValues = {
  shopify: ['collection'],
  wix: ['collection'],
  woocommerce: ['category', 'tags'],
  bigcommerce: ['brand'],
  squarespace: [],
};

// we have 2 forms - StoreProductDialog & FeedFilter
// we want consistent form values, enforced by this function
export const useFormValuesStoreFilters = (
  filters: StoreFiltersFormValues['storeFilters'] = []
) => {
  const { currentStore } = useCurrentStore();

  const returnFilters = React.useMemo(
    () =>
      filters.map((filter) => {
        filter.conditions = filter.conditions
          // TODO: move into zod schema transform
          // .filter(Boolean)
          .map((condition) => {
            condition.value = // template issue to make it as empty string
              currentStore.provider === 'woocommerce' &&
              filter.key === 'tags' &&
              condition.value === 'Windsor'
                ? ''
                : condition.value;

            condition._value =
              ((currentStore.provider === 'woocommerce' &&
                filter.key === 'tags') ||
                ['category', 'collection', 'status'].includes(filter.key) ||
                (currentStore.provider !== 'woocommerce' &&
                  filter.key === 'brand')) &&
              typeof condition.value === 'string'
                ? condition.value
                    .split(',')
                    .filter(Boolean)
                    .map((o) => ({
                      id:
                        // TODO: why need Number(o)?
                        filter.key === 'status' || o.includes('-')
                          ? o
                          : Number(o),
                    })) // wix collection using string example 7b25e060-9b2e-1ee0-b08a-ef15bb4fcbfe
                : { id: condition.value }; // object shape to match options
            return condition;
          }) as typeof filter.conditions;

        return filter;
      }),
    [filters, currentStore.provider]
  );
  return returnFilters;
};

export function StoreProduct() {
  const { t } = useTranslation();
  const { currentStore } = useCurrentStore();
  const { control } = useFormContext<StoreFiltersFormValues>();

  const { fields, prepend, remove, update } = useFieldArray({
    control,
    name: 'storeFilters',
  });

  const filterAvailable = filterByPlatform[currentStore.provider];

  const availableFilterOptions: FilterOptions = React.useMemo(
    () => differenceBy(filterAvailable, fields, 'key'),
    [fields, filterAvailable]
  );

  const newFilter =
    availableFilterOptions.length === 1
      ? {
          key: availableFilterOptions[0].key,
          conditions:
            platformDefaultStoreFilters[currentStore.provider][
              availableFilterOptions[0].key
            ] ?? [],
        }
      : {
          key: '' as const,
          conditions: [],
        };

  return (
    <Box>
      <Button
        variant="outlined"
        startIcon={<Icon type="default" icon={faPlus} />}
        onClick={() => prepend(newFilter)}
        disabled={availableFilterOptions.length === 0}
      >
        {t('add_filter')}
      </Button>
      <Box sx={{ marginTop: '32px' }} />
      {fields.map((field, fieldIndex) => {
        return (
          <React.Fragment key={field.id}>
            {(field.key === 'ignore_dont_track_inventory' && (
              <>
                {currentStore.provider === 'shopify' &&
                  field.conditions.map((_, conditionIndex) => (
                    <Switch
                      key={`${field.id}-${conditionIndex}`}
                      name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                      control={control}
                      label={
                        <Typography variant="h6" component="span">
                          {t('ignore_variant_track_quantity')}
                          <Link to="https://help.shopify.com/en/manual/products/inventory">
                            {t('more_info')}
                            <Icon
                              type="default"
                              icon={faArrowUpRightFromSquare}
                              style={{ marginLeft: '4px' }}
                            />
                          </Link>
                        </Typography>
                      }
                    />
                  ))}
              </>
            )) ||
              (field.key === 'ignore_zero_quantity' && (
                <Box sx={{ marginTop: '8px' }}>
                  {(currentStore.provider === 'shopify' ||
                    currentStore.provider === 'bigcommerce') &&
                    field.conditions.map((_, conditionIndex) => (
                      <Switch
                        key={`${field.id}-${conditionIndex}`}
                        name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                        control={control}
                        label={t('ignore_zero_quantity')}
                      />
                    ))}
                </Box>
              )) ||
              (field.key === 'update_only_when_zero_qty' && (
                <Box sx={{ marginTop: '8px' }}>
                  {currentStore.provider === 'shopify' &&
                    field.conditions.map((_, conditionIndex) => (
                      <Switch
                        key={`${field.id}-${conditionIndex}`}
                        name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                        control={control}
                        label={t('update_only_when_zero_qty')}
                      />
                    ))}
                </Box>
              )) || (
                <StoreField
                  fieldIndex={fieldIndex}
                  field={field}
                  availableFilterOptions={availableFilterOptions}
                  remove={remove}
                  update={update}
                />
              )}
          </React.Fragment>
        );
      })}
    </Box>
  );
}

interface StoreFieldProps
  extends Pick<UseFieldArrayReturnProps, 'remove' | 'update'> {
  fieldIndex: number;
  field: UseFieldArrayReturnProps['fields'][0];
  availableFilterOptions: FilterOptions;
}

function StoreField({
  fieldIndex,
  field,
  availableFilterOptions,
  remove,
  update,
}: StoreFieldProps) {
  const { currentStore } = useCurrentStore();
  const { t } = useTranslation();
  const theme = useTheme();

  const { control, setValue, getValues } =
    useFormContext<StoreFiltersFormValues>();
  const {
    published_status_filters: publishedStatusFilters,
    is_visible: isVisibleOptions,
    status_filters: statusFilters,
    quantity_filters: quantityFilters,
  } = currentStore.feedConstants;
  const [removeIndex, setRemoveIndex] = React.useState(-1);
  const [open, setOpen] = React.useState(false);
  const [openIndex, setOpenIndex] = React.useState<number | null>(null);
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null
  );

  const [{ data }] = useQuery({
    query: GetCategories,
    pause: currentStore.provider !== 'shopify',
    requestPolicy: 'network-only',
  });

  const [{ data: vendors }] = useQuery({
    query: GetVendors,
    pause: currentStore.provider !== 'shopify',
    requestPolicy: 'network-only',
  });

  const categories = React.useMemo(() => data?.getCategories || [], [data]);
  const shopifyVendors = React.useMemo(
    () => vendors?.getVendors || [],
    [vendors]
  );

  return (
    <>
      <Stack direction="row" spacing="5px" sx={{ marginBottom: '32px' }}>
        <IconButton
          edge="start"
          disableRipple
          onClick={(event) => {
            setRemoveIndex(fieldIndex);
            setAnchorEl(event.currentTarget);
          }}
          id="delete-store-filter"
          sx={{ color: theme.palette.grey[300], marginLeft: '0px' }}
        >
          <Icon type="default" icon={faTrash} fontSize={16} />
        </IconButton>
        <Stack sx={{ width: '100%' }}>
          <Autocomplete
            variant="standard"
            name={`storeFilters.${fieldIndex}.key`}
            control={control}
            label={t('store_field_name')}
            onChange={(_, key) => {
              if (!key) return;
              const conditions =
                platformDefaultStoreFilters[currentStore.provider][key] ?? [];
              update(fieldIndex, { ...field, key, conditions });
            }}
            getOptionLabel={(option) => t(option)}
            options={React.useMemo(
              () =>
                [
                  filterByPlatform[currentStore.provider].find(
                    (fo) => fo.key === field.key
                  ),
                ]
                  .filter(Boolean)
                  .concat(availableFilterOptions)
                  .map((option) => option?.key ?? ''),
              [currentStore, field, availableFilterOptions]
            )}
            openOnFocus={availableFilterOptions.length > 1}
          />
        </Stack>
      </Stack>
      {field.conditions.filter(Boolean).map((condition, conditionIndex) => (
        <Stack
          key={`${field.id} - ${conditionIndex}`}
          direction="row"
          spacing="5px"
          sx={{
            marginBottom: '32px',
            height: 'auto',
            alignItems: 'flex-start',
          }}
        >
          {platformSupportMultipleValues[currentStore.provider].includes(
            field.key
          ) ||
          field.key === 'published' ||
          field.key === 'status' ? (
            <Box />
          ) : (
            <>
              <IconButton
                onClick={() => {
                  setOpen(true);
                  setOpenIndex(conditionIndex);
                }}
                sx={{
                  color: theme.palette.grey[300],
                  textAlign: 'center',
                  marginTop: '8px',
                }}
              >
                <Icon
                  type="default"
                  icon={faArrowUpRightAndArrowDownLeftFromCenter}
                  fontSize={16}
                />
              </IconButton>
              <QuickEditDialog
                value={getValues(
                  `storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`
                )?.toString()}
                open={open && openIndex === conditionIndex}
                onClose={() => {
                  setOpen(false);
                  setOpenIndex(null);
                }}
                onSubmit={(value) => {
                  setValue(
                    `storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`,
                    value
                  );
                }}
              />
            </>
          )}
          {field.key === 'inventory_quantity' ? (
            <TextField
              select
              control={control}
              name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.operator`}
              label={t('operator')}
            >
              {quantityFilters.map((option) => (
                <MenuItem key={option.key} value={option.key}>
                  {t(option.value)}
                </MenuItem>
              ))}
            </TextField>
          ) : (
            <Box
              sx={{
                width: '100%',
                maxWidth: '100px',
                paddingLeft: '16px',
                height: '56px',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {field.key === 'published' || field.key === 'status'
                ? t('contains')
                : t(condition.operator)}
            </Box>
          )}
          <>
            {(currentStore.provider === 'bigcommerce' &&
              field.key === 'published' && (
                <TextField
                  select
                  control={control}
                  name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                  label={t('value')}
                >
                  {isVisibleOptions.map((option) => (
                    <MenuItem key={option.key} value={option.key}>
                      {t(option.value)}
                    </MenuItem>
                  ))}
                </TextField>
              )) ||
              (field.key === 'published' && (
                <TextField
                  select
                  control={control}
                  name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                  label={t('value')}
                >
                  {publishedStatusFilters.map((option) => (
                    <MenuItem key={option.key} value={option.key}>
                      {t(option.value)}
                    </MenuItem>
                  ))}
                </TextField>
              )) ||
              (field.key === 'status' && (
                <Autocomplete
                  multiple
                  openOnFocus
                  control={control}
                  name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}._value`}
                  label={t('value')}
                  onChange={(_, selectedOptions) => {
                    setValue(
                      `storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`,
                      selectedOptions.map((o) => o.id).join(',')
                    );
                  }}
                  options={statusFilters.map(({ key: id, value: title }) => ({
                    id,
                    title,
                  }))}
                  getOptionLabel={(option) => {
                    const label =
                      // if user selects
                      option.title ||
                      // API only returns id, find matching title
                      statusFilters.find((c) => `${c.key}` === `${option.id}`)
                        ?.value ||
                      // default to selected value
                      option.id;

                    return t(label);
                  }}
                  isOptionEqualToValue={(option, value) => {
                    return `${option?.id}` === `${value?.id}`;
                  }}
                />
              )) ||
              (field.key === 'inventory_quantity' && (
                <TextField
                  type="number"
                  control={control}
                  name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                  label={t('value')}
                />
              )) ||
              (field.key === 'product_category_id' && (
                <Autocomplete
                  openOnFocus
                  control={control}
                  name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}._value`}
                  label={t('value')}
                  options={categories.map(({ id, name: title }) => ({
                    id,
                    title,
                  }))}
                  onChange={(_, selectedOptions) => {
                    if (selectedOptions) {
                      setValue(
                        `storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`,
                        selectedOptions.id
                      );
                    }
                  }}
                  getOptionLabel={(option) =>
                    // if user selects
                    typeof option === 'string'
                      ? categories.find((c) => `${c.id}` === `${option}`)
                          ?.name || ''
                      : option.title ||
                        // API only returns id, find matching title
                        categories.find((c) => `${c.id}` === `${option.id}`)
                          ?.name ||
                        // default to selected value
                        option.id ||
                        ''
                  }
                  isOptionEqualToValue={(option, value) => {
                    return `${option?.id}` === `${value?.id}`;
                  }}
                />
              )) ||
              (field.key === 'vendor' && (
                <Tags
                  whitelist={shopifyVendors.map((data) =>
                    compareRegexAsString(data)
                  )}
                  name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                  control={control}
                  label={t('value')}
                  settings={{
                    // parses API response correctly: "1,5,6,\"Nemo Lighting (Illuminating Experiences, MacMaster, Nemo)\",9"
                    delimiters: commaNotInsideQuotes,
                    enforceWhitelist: true, // only select from dropdown
                    dropdown: {
                      enabled: 0, // a;ways show suggestions dropdown
                      maxItems: 50,
                      caseSensitive: true,
                      placeAbove: false,
                    },
                  }}
                  renderOption={(props, item) => (
                    <Box {...props}>
                      <Typography component="div" variant="body1">
                        {item.value}
                      </Typography>
                    </Box>
                  )}
                />
              )) ||
              (platformSupportMultipleValues[currentStore.provider].includes(
                field.key
              ) && (
                <React.Suspense
                  fallback={<Autocomplete name="" options={[]} loading />}
                >
                  <StoreFieldCollections
                    fieldIndex={fieldIndex}
                    conditionIndex={conditionIndex}
                    field={field}
                  />
                </React.Suspense>
              )) || (
                <React.Suspense
                  fallback={
                    <Tags
                      name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                      loading
                    />
                  }
                >
                  <Tags
                    name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`}
                    control={control}
                    label={t('value')}
                    settings={{
                      // parses API response correctly: "1,5,6,\"Nemo Lighting (Illuminating Experiences, MacMaster, Nemo)\",9"
                      delimiters: commaNotInsideQuotes,
                    }}
                  />
                </React.Suspense>
              )}
          </>
        </Stack>
      ))}
      <DeletePopover
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        onConfirm={() => {
          remove(removeIndex);
          setAnchorEl(null);
        }}
      />
    </>
  );
}

interface StoreFieldCollectionsProps {
  fieldIndex: number;
  conditionIndex: number;
  field: UseFieldArrayReturnProps['fields'][0];
}

function StoreFieldCollections({
  fieldIndex,
  conditionIndex,
  field,
}: StoreFieldCollectionsProps) {
  const { t } = useTranslation();
  const { currentStore } = useCurrentStore();
  const { control, setValue } = useFormContext<StoreFiltersFormValues>();

  const [{ data, fetching }] = useQuery({
    query: GetCollections,
    pause: currentStore.provider !== 'shopify',
  });
  const [{ data: wixCollection, fetching: wixCollectionFetching }] = useQuery({
    query: GetCollectionsFromWix,
    pause: currentStore.provider !== 'wix',
  });
  const [{ data: woocommerceCategory, fetching: woocommerceCategoryFetching }] =
    useQuery({
      query: GetWoocommerceCategories,
      pause: currentStore.provider !== 'woocommerce',
    });

  const [{ data: woocommerceTag }] = useQuery({
    query: GetWoocommerceTags,
    pause: currentStore.provider !== 'woocommerce',
  });
  const [{ data: bigcommerceBrand, fetching: bigcommerceBrandFetching }] =
    useQuery({
      query: GetBigcommerceBrands,
      pause: currentStore.provider !== 'bigcommerce',
    });

  const collections = React.useMemo(() => data?.getCollections || [], [data]);
  const wixCollections = React.useMemo(
    () => wixCollection?.getCollectionsFromWix || [],
    [wixCollection]
  );
  const woocommerceCategories = React.useMemo(
    () => woocommerceCategory?.getWoocommerceCategories || [],
    [woocommerceCategory]
  );
  const woocommerceTags = React.useMemo(
    () => woocommerceTag?.getWoocommerceTags || [],
    [woocommerceTag]
  );
  const bigcommerceBrands = React.useMemo(
    () => bigcommerceBrand?.getBigcommerceBrands || [],
    [bigcommerceBrand]
  );

  const platformCollectionLoadingState = {
    shopify: fetching,
    wix: wixCollectionFetching,
    woocommerce: woocommerceCategoryFetching,
    bigcommerce: bigcommerceBrandFetching,
  };
  const platformCollectionOption = React.useMemo(() => {
    return {
      shopify: collections,
      wix: wixCollections.map(({ key: id, value: title }) => ({
        id,
        title,
      })),
      woocommerce: woocommerceCategories.map(({ key: id, value: title }) => ({
        id,
        title,
      })),
      bigcommerce: bigcommerceBrands.map(({ key: id, value: title }) => ({
        id,
        title,
      })),
    };
  }, [bigcommerceBrands, woocommerceCategories, wixCollections, collections]);

  const collectionOptions: CollectionOption[] = React.useMemo(() => {
    return currentStore.provider === 'woocommerce' && field.key === 'tags'
      ? woocommerceTags.map(({ key: id, value: title }) => ({
          id,
          title,
        }))
      : platformCollectionOption[currentStore.provider];
  }, [currentStore, field, platformCollectionOption, woocommerceTags]);

  return (
    <Autocomplete
      multiple
      allowBlank={false}
      openOnFocus
      disabled={platformCollectionLoadingState[currentStore.provider]}
      loading={platformCollectionLoadingState[currentStore.provider]}
      control={control}
      name={`storeFilters.${fieldIndex}.conditions.${conditionIndex}._value`}
      label={t('value')}
      onChange={(_, selectedOptions) => {
        setValue(
          `storeFilters.${fieldIndex}.conditions.${conditionIndex}.value`,
          selectedOptions.map((o) => o.id).join(',')
        );
      }}
      options={collectionOptions}
      getOptionLabel={(option) =>
        // if user selects
        option.title ||
        // API only returns id, find matching title
        collectionOptions.find((c) => `${c.id}` === `${option.id}`)?.title ||
        // default to selected value
        option.id
      }
      isOptionEqualToValue={(option, value) =>
        `${option?.id}` === `${value?.id}`
      }
    />
  );
}

function DeletePopover({ onClose, onConfirm, anchorEl }) {
  const { t } = useTranslation();
  return (
    <PopoverConfirm
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      cancelButtonProps={{ children: t('no'), onClick: onClose }}
      okButtonProps={{ children: t('yes'), onClick: onConfirm }}
    >
      {t('confirm_delete')}
    </PopoverConfirm>
  );
}
