import { faArrowRight, faCircleCheck } from '@fortawesome/pro-light-svg-icons';
import {
  Box,
  CardContent,
  CardHeader,
  Stack,
  Typography,
  useTheme,
  type TypographyProps,
} from '@mui/material';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { match } from 'ts-pattern';
import { useQuery } from 'urql';

import { Card } from '@/components/Card';
import { Collapse } from '@/components/Collapse';
import { Icon } from '@/components/Icon';
import { Loading } from '@/components/Loading';
import { Table } from '@/components/Table';
import { useCurrentStore } from '@/hooks/useCurrentStore';
import { useDisplayLanguages } from '@/hooks/useDisplayLanguages';
import { useSettingsFeed } from '@/hooks/useSettingsFeed';
import {
  GetBigcommerceBrands,
  GetCollections,
  GetCollectionsFromWix,
  GetWoocommerceCategories,
  GetWoocommerceTags,
} from '@/queries/Collections';
import type { UserProfile } from '@/types';

interface FeedSettingSummaryProps {
  feedId: UserProfile['id'];
  showAllDetail: boolean;
}

export function FeedSettingSummary({
  feedId,
  showAllDetail,
}: FeedSettingSummaryProps) {
  return (
    <React.Suspense
      fallback={
        <Table.Row sx={{ verticalAlign: 'baseline' }}>
          <Table.Cell colSpan={3}>
            <Loading sx={{ maxHeight: '33vh' }} />
          </Table.Cell>
        </Table.Row>
      }
    >
      <Table.Row sx={{ verticalAlign: 'baseline' }}>
        <Table.Cell sx={{ borderBottom: 'none' }}>
          <FeedMappingSummary feedId={feedId} showAllDetail={showAllDetail} />
        </Table.Cell>
        <Table.Cell sx={{ borderBottom: 'none' }}>
          <FilterSummary feedId={feedId} showAllDetail={showAllDetail} />
        </Table.Cell>
        <Table.Cell sx={{ borderBottom: 'none' }}>
          <AdvancedSettingSummary
            feedId={feedId}
            showAllDetail={showAllDetail}
          />
        </Table.Cell>
      </Table.Row>
    </React.Suspense>
  );
}

function FeedMappingSummary({ feedId, showAllDetail }) {
  const feedQuery = useSettingsFeed({ id: Number(feedId) });
  const feed = feedQuery.feed!;
  const [openCollapse, setOpenCollapse] = React.useState('');
  const { t } = useTranslation();
  const theme = useTheme();
  const { languages } = useDisplayLanguages();
  const {
    currentStore: {
      feedConstants: {
        case_convert_options: caseConvertOptions,
        weight_formula_options: weightFormulaOptions,
        price_round_options: priceRoundOptions,
        price_delimiter_options: priceDelimiterOptions,
        compare_at_price_round_options: compareAtPriceRoundOptions,
        cost_round_options: costRoundOptions,
        wrap_tag_options: wrapTagOptions,
        quantity_delimiter_options: quantityDelimiterOptions,
      },
    },
    currentStore,
  } = useCurrentStore();

  const filterFromSyncFieldSettings = [
    'product_id',
    'variant_group',
    'option1',
    'option2',
    'option3',
  ];

  const syncFieldSettingFilterMetafield = feed.syncFieldSettings?.filter(
    (a) =>
      !a.field_name.includes('metafield_') &&
      !a.field_name.includes('metadata_') &&
      !a.field_name.includes('custom_field_') &&
      !a.field_name.includes('product_attribute_') &&
      !filterFromSyncFieldSettings.includes(a.field_name)
  );

  return (
    <Box>
      <FieldAndColumnName
        open={openCollapse === feed.shopifyProductKey || showAllDetail}
        onClick={() =>
          openCollapse !== feed.shopifyProductKey
            ? setOpenCollapse(feed.shopifyProductKey ?? '')
            : setOpenCollapse('')
        }
        fieldName={t(feed.shopifyProductKey ?? '')}
        columnName={feed.productIdentifierSyncField?.field_mapping ?? ''}
      >
        {feed.storePrefix && (
          <LabelAndValue
            label={t('label_store_prefix')}
            value={feed.storePrefix}
          />
        )}
        {feed.prefix && (
          <LabelAndValue label={t('feed_prefix')} value={feed.prefix} />
        )}
        {feed.postfix && (
          <LabelAndValue label={t('feed_postfix')} value={feed.postfix} />
        )}
        {feed.productKeySeparator && (
          <LabelAndValue
            label={t('label_product_key_separator')}
            value={feed.productKeySeparator}
          />
        )}
        {feed.caseSensitive && (
          <OptionEnabled>{t('case_sensitive')}</OptionEnabled>
        )}
        {feed.updateDuplicateProductKey && (
          <OptionEnabled>
            {t('update_with_same_product_identifier')}
          </OptionEnabled>
        )}
      </FieldAndColumnName>
      {syncFieldSettingFilterMetafield?.map((sfs, index) => {
        const caseConvert = caseConvertOptions.filter(
          (a) => a.key === sfs.extra_attributes.case_convert
        )[0];
        const weightFormula = weightFormulaOptions.filter(
          (a) => a.key === sfs.extra_attributes.weight_formula
        )[0];
        const priceDelimiter = priceDelimiterOptions.filter(
          (a) => a.key === sfs.extra_attributes.price_delimiter
        )[0];
        const quantityDelimiter = quantityDelimiterOptions.filter(
          (a) => a.key === sfs.extra_attributes.quantity_delimiter
        )[0];
        const priceRound = priceRoundOptions.filter(
          (a) => a.key === sfs.extra_attributes.price_round?.toString() || ''
        )[0];
        const wrapTag = wrapTagOptions.filter(
          (a) => a.key === sfs.extra_attributes.wrap_tag
        )[0];

        return (
          <React.Fragment key={index}>
            <Box sx={{ m: theme.spacing(2, 0) }} />
            <FieldAndColumnName
              open={openCollapse === sfs.field_name || showAllDetail}
              onClick={() =>
                openCollapse !== sfs.field_name
                  ? setOpenCollapse(sfs.field_name)
                  : setOpenCollapse('')
              }
              fieldName={match({
                provider: currentStore.provider,
                name: sfs.field_name,
                quantityUseOnHand: sfs.extra_attributes.quantity_use_on_hand,
              })
                .with({ provider: 'bigcommerce', name: 'quantity' }, () =>
                  t('bigcommerce_quantity')
                )
                .with({ provider: 'woocommerce', name: 'quantity' }, () =>
                  t('stock_quantity')
                )
                .with(
                  {
                    provider: 'shopify',
                    name: 'quantity',
                    quantityUseOnHand: true,
                  },
                  () => t('quantity_on_hand')
                )
                .with(
                  {
                    provider: 'shopify',
                    name: 'quantity',
                    quantityUseOnHand: false,
                  },
                  () => t('quantity_available')
                )
                .otherwise(() => t(sfs.field_name))}
              columnName={sfs.field_mapping ?? ''}
            >
              {{
                barcode: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                product_type: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                template_suffix: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                country_code_of_origin: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                fulfillment_service: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                brand: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                category: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {feed.feedType !== 'import' &&
                      sfs.extra_attributes.override_categories && (
                        <OptionEnabled>{t('label_categories')}</OptionEnabled>
                      )}
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                    {currentStore.provider === 'bigcommerce' &&
                      sfs.extra_attributes.parent_category_id && (
                        <LabelAndValue
                          label={t('parent_category_id')}
                          value={sfs.extra_attributes.parent_category_id}
                        />
                      )}
                    {currentStore.provider === 'wix' && (
                      <>
                        {sfs.extra_attributes.original_language && (
                          <LabelAndValue
                            label={t('translate_from')}
                            value={
                              languages.find(
                                (a) =>
                                  a.key ===
                                  sfs.extra_attributes.original_language
                              )?.value
                            }
                          />
                        )}
                        {sfs.extra_attributes.returned_language && (
                          <LabelAndValue
                            label={t('translate_to')}
                            value={
                              languages.find(
                                (a) =>
                                  a.key ===
                                  sfs.extra_attributes.returned_language
                              )?.value
                            }
                          />
                        )}
                      </>
                    )}
                  </Box>
                ),
                bin_picking_number: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                mpn: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                images: (
                  <>
                    {feed.humanizeFeedType === 'import' && (
                      <Box>
                        {feed.variantImageLink && (
                          <OptionEnabled>
                            {t('variant_image_link')}
                          </OptionEnabled>
                        )}
                        {feed.assignVariantsToFirstImage && (
                          <OptionEnabled>
                            {t('assign_first_image_to_all_variant')}
                          </OptionEnabled>
                        )}
                      </Box>
                    )}
                    {feed.humanizeFeedType === 'update' && (
                      <OptionEnabled>
                        {sfs.extra_attributes.force_override &&
                        currentStore.provider !== 'wix'
                          ? t('skip_image_import_new_image')
                          : t('no_image_append_image')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.url_prefix && (
                      <LabelAndValue
                        label={t('label_images_url_prefix')}
                        value={sfs.extra_attributes.url_prefix}
                      />
                    )}
                    {feed.ioMode === 'in' && (
                      <Box>
                        {sfs.extra_attributes.alt_mapping && (
                          <LabelAndValue
                            label={t('label_images_alt_mapping')}
                            value={sfs.extra_attributes.alt_mapping}
                          />
                        )}
                        {sfs.extra_attributes.default_image_url && (
                          <LabelAndValue
                            label={t('label_default_image_url')}
                            value={sfs.extra_attributes.default_image_url}
                          />
                        )}
                        {sfs.extra_attributes.col_sep && (
                          <LabelAndValue
                            label={t('label_images_col_sep')}
                            value={sfs.extra_attributes.col_sep}
                          />
                        )}
                        {sfs.extra_attributes.url_unescape && (
                          <LabelAndValue
                            label={t('url_option')}
                            value={
                              sfs.extra_attributes.url_unescape
                                ? 'Unescape URL https%3A// to https://'
                                : t('raw_url')
                            }
                          />
                        )}
                        {sfs.extra_attributes.image_cdn && (
                          <OptionEnabled>{t('modify_url')}</OptionEnabled>
                        )}
                        {feed.feedType !== 'import' &&
                          sfs.extra_attributes.clear_current_images && (
                            <OptionEnabled>
                              {t('clear_image_before_import')}
                            </OptionEnabled>
                          )}
                      </Box>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </>
                ),
                weight: (
                  <Box>
                    {feed.humanizeFeedType !== 'export' && (
                      <LabelAndValue
                        value={
                          sfs.extra_attributes.static_flag
                            ? t('static')
                            : t('mappings')
                        }
                      />
                    )}
                    {currentStore.provider !== 'bigcommerce' && (
                      <LabelAndValue
                        label={t('label_weight')}
                        value={
                          sfs.extra_attributes.weight_unit === ' '
                            ? 'No changes'
                            : sfs.extra_attributes.weight_unit
                        }
                      />
                    )}
                    <LabelAndValue
                      label={t('label_weight_formula')}
                      value={
                        sfs.extra_attributes.weight_formula
                          ? weightFormula?.value
                          : 'No changes'
                      }
                    />
                    <LabelAndValue
                      label={t('label_weight_delimiter')}
                      value={sfs.extra_attributes.weight_delimiter}
                    />
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                product_title: (
                  <Box>
                    <LabelAndValue
                      label={t('label_title_separator')}
                      value={sfs.extra_attributes.title_separator}
                    />
                    <LabelAndValue
                      label={t('label_case_convert')}
                      value={
                        sfs.extra_attributes.case_convert
                          ? caseConvert?.value
                          : 'No changes'
                      }
                    />
                    {sfs.extra_attributes.ignore_words && (
                      <LabelAndValue
                        label={t('label_ignore_words')}
                        value={sfs.extra_attributes.ignore_words}
                      />
                    )}
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                    {sfs.extra_attributes.auto_ignore_option_words && (
                      <OptionEnabled>
                        {t('label_auto_ignore_option_words')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.original_language && (
                      <LabelAndValue
                        label={t('translate_from')}
                        value={
                          languages.find(
                            (a) =>
                              a.key === sfs.extra_attributes.original_language
                          )?.value
                        }
                      />
                    )}
                    {sfs.extra_attributes.returned_language && (
                      <LabelAndValue
                        label={t('translate_to')}
                        value={
                          languages.find(
                            (a) =>
                              a.key === sfs.extra_attributes.returned_language
                          )?.value
                        }
                      />
                    )}
                  </Box>
                ),
                body_html: (
                  <>
                    <LabelAndValue
                      label={t('label_case_convert')}
                      value={
                        sfs.extra_attributes.case_convert
                          ? caseConvert?.value
                          : 'No changes'
                      }
                    />
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                    <LabelAndValue
                      label={t('wrap_tag')}
                      value={wrapTag?.value}
                    />
                    {sfs.extra_attributes.labels && (
                      <LabelAndValue
                        label={t('label_body_labels')}
                        value={sfs.extra_attributes.labels}
                      />
                    )}
                    {sfs.extra_attributes.original_language && (
                      <LabelAndValue
                        label={t('translate_from')}
                        value={
                          languages.find(
                            (a) =>
                              a.key === sfs.extra_attributes.original_language
                          )?.value
                        }
                      />
                    )}
                    {sfs.extra_attributes.returned_language && (
                      <LabelAndValue
                        label={t('translate_to')}
                        value={
                          languages.find(
                            (a) =>
                              a.key === sfs.extra_attributes.returned_language
                          )?.value
                        }
                      />
                    )}
                    {feed.humanizeFeedType === 'update' &&
                      currentStore.provider === 'shopify' &&
                      sfs.extra_attributes.update_only_if_nil && (
                        <OptionEnabled>{t('update_only_if_nil')}</OptionEnabled>
                      )}
                    {sfs.extra_attributes.convert_line_break && (
                      <OptionEnabled>
                        {t('label_convert_line_break')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.force_override_description && (
                      <OptionEnabled>
                        {t('label_force_override_description')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_zero_blank_labels && (
                      <OptionEnabled>
                        {t('label_skip_zero_blank_labels')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </>
                ),
                tags: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.field_prefix && (
                      <LabelAndValue
                        label={t('label_field_prefix')}
                        value={sfs.extra_attributes.field_prefix}
                      />
                    )}
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                    {sfs.extra_attributes.ignore_tags && (
                      <LabelAndValue
                        label={t('ignore_tags')}
                        value={sfs.extra_attributes.ignore_tags}
                      />
                    )}
                    {sfs.extra_attributes.col_sep && (
                      <LabelAndValue
                        label={t('label_tags_col_sep')}
                        value={sfs.extra_attributes.col_sep}
                      />
                    )}
                    {feed.humanizeFeedType === 'update' && (
                      <Box>
                        {sfs.extra_attributes.override_tags && (
                          <OptionEnabled>{t('label_tags')}</OptionEnabled>
                        )}
                        {sfs.extra_attributes.add_tags && (
                          <OptionEnabled>{t('label_add_tags')}</OptionEnabled>
                        )}
                      </Box>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                published: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.eq_show_value && (
                      <LabelAndValue
                        label={t('label_eq_show_value')}
                        value={sfs.extra_attributes.eq_show_value}
                      />
                    )}
                    {sfs.extra_attributes.eq_hide_value && (
                      <LabelAndValue
                        label={t('label_eq_hide_value')}
                        value={sfs.extra_attributes.eq_hide_value}
                      />
                    )}
                    {sfs.extra_attributes.override_publish && (
                      <OptionEnabled>
                        {t('label_override_publish')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                policy: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.policy_continue_value && (
                      <LabelAndValue
                        label="Enable continue selling when out of stock feed value is"
                        value={sfs.extra_attributes.policy_continue_value}
                      />
                    )}
                    {sfs.extra_attributes.policy_deny_value && (
                      <LabelAndValue
                        label="Disable continue selling when out of stock feed value is"
                        value={sfs.extra_attributes.policy_deny_value}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                harmonized_system_code: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.hs_code_suffix && (
                      <LabelAndValue
                        label={t('label_hs_code_suffix')}
                        value={sfs.extra_attributes.hs_code_suffix}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                taxable: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.not_taxable_flag && (
                      <LabelAndValue
                        label={t('label_not_taxable_flag')}
                        value={sfs.extra_attributes.not_taxable_flag}
                      />
                    )}
                    {sfs.extra_attributes.taxable_flag && (
                      <LabelAndValue
                        label={t('label_taxable_flag')}
                        value={sfs.extra_attributes.taxable_flag}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                vendor: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.ignore_words && (
                      <LabelAndValue
                        label={t('label_ignore_words')}
                        value={sfs.extra_attributes.ignore_words}
                      />
                    )}
                    <LabelAndValue
                      label={t('label_case_convert')}
                      value={
                        sfs.extra_attributes.case_convert
                          ? caseConvert?.value
                          : 'No changes'
                      }
                    />
                  </Box>
                ),
                standardized_product_type: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.language_code && (
                      <LabelAndValue
                        label={t('label_language_code')}
                        value={sfs.extra_attributes.language_code}
                      />
                    )}
                  </Box>
                ),
                status: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.status_active_flag && (
                      <LabelAndValue
                        label={t('label_status_active_flag')}
                        value={sfs.extra_attributes.status_active_flag}
                      />
                    )}
                    {sfs.extra_attributes.status_draft_flag && (
                      <LabelAndValue
                        label={t('label_status_draft_flag')}
                        value={sfs.extra_attributes.status_draft_flag}
                      />
                    )}
                    {sfs.extra_attributes.status_archived_flag && (
                      <LabelAndValue
                        label={t('label_status_archived_flag')}
                        value={sfs.extra_attributes.status_archived_flag}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                backorders: (
                  <Box>
                    {sfs.extra_attributes.disallow_backorder && (
                      <LabelAndValue
                        label={t('allow_backorder_extra_attributes')}
                        value={sfs.extra_attributes.disallow_backorder}
                      />
                    )}
                    {sfs.extra_attributes.allow_but_notify && (
                      <LabelAndValue
                        label={t('do_not_allow_backorder')}
                        value={sfs.extra_attributes.allow_but_notify}
                      />
                    )}
                    {sfs.extra_attributes.allow_backorder && (
                      <LabelAndValue
                        label={t('allow_but_notify_customer_backorder')}
                        value={sfs.extra_attributes.allow_backorder}
                      />
                    )}
                  </Box>
                ),
                requires_shipping: (
                  <Box>
                    {sfs.extra_attributes.false_values && (
                      <LabelAndValue
                        label={t('label_false_values')}
                        value={sfs.extra_attributes.false_values}
                      />
                    )}
                  </Box>
                ),
                quantity: (
                  <Box>
                    {currentStore.provider !== 'wix' && (
                      <LabelAndValue
                        label={t('location')}
                        value={feed.locationName}
                      />
                    )}
                    {feed.runWebhookMode &&
                      feed.humanizeFeedType === 'update' &&
                      currentStore.provider === 'shopify' &&
                      sfs.extra_attributes.create_location && (
                        <OptionEnabled>
                          {t('create_location_preview')}
                        </OptionEnabled>
                      )}
                    {(sfs.extra_attributes.rules_json || []).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('label_rules_json')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.rules_json || []).map(
                      (rj, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={rj.operator}
                          />
                          <LabelAndValue label={t('key')} value={rj.key} />
                          <LabelAndValue label={t('value')} value={rj.value} />
                        </Stack>
                      )
                    )}
                    {feed.feedType === 'import' &&
                      feed.skipImportWithZeroQty && (
                        <OptionEnabled>
                          {t('skip_product_zero_quantity')}
                        </OptionEnabled>
                      )}
                    {feed.feedType === 'update' && (
                      <Box>
                        {feed.humanizeFeedType === 'update' && (
                          <LabelAndValue
                            label={t('low_stock_level')}
                            value={feed.lowStockLevel}
                          />
                        )}
                        {!sfs.extra_attributes.only_deduct_quantity &&
                          !sfs.extra_attributes.add_to_init_quantity && (
                            <OptionEnabled>
                              {t('overwrite_quantity')}
                            </OptionEnabled>
                          )}
                        {!sfs.extra_attributes.only_deduct_quantity &&
                          sfs.extra_attributes.add_to_init_quantity && (
                            <OptionEnabled>
                              {t('add_or_deduct_quantity')}
                            </OptionEnabled>
                          )}
                        {sfs.extra_attributes.only_deduct_quantity &&
                          !sfs.extra_attributes.add_to_init_quantity && (
                            <OptionEnabled>
                              {t('deduct_quantity')}
                            </OptionEnabled>
                          )}
                        {sfs.extra_attributes.reset_init_quantity && (
                          <OptionEnabled>
                            {t('quantity_set_to_zero')}
                          </OptionEnabled>
                        )}
                        {feed.shopifyTrackInventory && (
                          <OptionEnabled>{t('track_quantity')}</OptionEnabled>
                        )}
                      </Box>
                    )}
                    {sfs.extra_attributes.quantity_delimiter && (
                      <LabelAndValue
                        label={t('quantity_delimiter')}
                        value={t(quantityDelimiter?.value)}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                price: (
                  <Box>
                    {feed.feedType === 'import' &&
                      sfs.extra_attributes.language_code && (
                        <LabelAndValue
                          label={t('label_language_code')}
                          value={sfs.extra_attributes.language_code}
                        />
                      )}
                    {(sfs.extra_attributes.pricing_conditions || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.pricing_conditions || []).map(
                      (pc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={pc.condition}
                          />
                          <LabelAndValue
                            label={t('formula')}
                            value={pc.formula}
                          />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={priceRound?.value}
                      />
                    )}
                    {sfs.extra_attributes.currency_converter && (
                      <Box>
                        <LabelAndValue
                          label={t('currency_from')}
                          value={sfs.extra_attributes.default_currency}
                        />
                        <LabelAndValue
                          label={t('currency_to')}
                          value={sfs.extra_attributes.new_currency}
                        />
                      </Box>
                    )}
                    {(sfs.extra_attributes.restrict_conditions || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.restrict_conditions || []).map(
                      (rc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('column_name')}
                            value={rc.attribute}
                          />
                          <LabelAndValue label={t('value')} value={rc.value} />
                        </Stack>
                      )
                    )}
                  </Box>
                ),
                compare_price_at: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {(sfs.extra_attributes.compare_at_pricing_conditions || [])
                      .length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes.compare_at_pricing_conditions || []
                    ).map((pc, index) => (
                      <Stack
                        key={index}
                        direction="row"
                        sx={{
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                        spacing={2}
                      >
                        <LabelAndValue
                          sx={{ marginTop: '0px' }}
                          label={t('condition')}
                          value={pc.condition}
                        />
                        <LabelAndValue
                          label={t('formula')}
                          value={pc.formula}
                        />
                      </Stack>
                    ))}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={
                          compareAtPriceRoundOptions.filter(
                            (a) =>
                              a.key ===
                                sfs.extra_attributes.price_round?.toString() ||
                              ''
                          )[0]?.value
                        }
                      />
                    )}
                    {sfs.extra_attributes.currency_converter && (
                      <Box>
                        <LabelAndValue
                          label={t('currency_from')}
                          value={sfs.extra_attributes.default_currency}
                        />
                        <LabelAndValue
                          label={t('currency_to')}
                          value={sfs.extra_attributes.new_currency}
                        />
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes
                        .compare_price_at_restrict_conditions || []
                    ).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes
                        .compare_price_at_restrict_conditions || []
                    ).map((rc, index) => (
                      <Stack
                        key={index}
                        direction="row"
                        sx={{
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                        spacing={2}
                      >
                        <LabelAndValue
                          sx={{ marginTop: '0px' }}
                          label={t('column_name')}
                          value={rc.attribute}
                        />
                        <LabelAndValue label={t('value')} value={rc.value} />
                      </Stack>
                    ))}
                    {sfs.extra_attributes.force_override_compare_at_price && (
                      <OptionEnabled>
                        {t('label_force_override_compare_at_price')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                cost: (
                  <Box>
                    {(sfs.extra_attributes.cost_pricing_conditions || [])
                      .length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.cost_pricing_conditions || []).map(
                      (pc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={pc.condition}
                          />
                          <LabelAndValue
                            label={t('formula')}
                            value={pc.formula}
                          />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={
                          costRoundOptions.filter(
                            (a) =>
                              a.key ===
                                sfs.extra_attributes.price_round?.toString() ||
                              ''
                          )[0]?.value
                        }
                      />
                    )}
                    {sfs.extra_attributes.currency_converter && (
                      <Box>
                        <LabelAndValue
                          label={t('currency_from')}
                          value={sfs.extra_attributes.default_currency}
                        />
                        <LabelAndValue
                          label={t('currency_to')}
                          value={sfs.extra_attributes.new_currency}
                        />
                      </Box>
                    )}
                    {(sfs.extra_attributes.cost_restrict_conditions || [])
                      .length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.cost_restrict_conditions || []).map(
                      (rc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('column_name')}
                            value={rc.attribute}
                          />
                          <LabelAndValue label={t('value')} value={rc.value} />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                track_quantity: (
                  <Box>
                    {sfs.extra_attributes.eq_enable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_enable_track_qty_value')}
                        value={sfs.extra_attributes.eq_enable_track_qty_value}
                      />
                    )}
                    {sfs.extra_attributes.eq_disable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_disable_track_qty_value')}
                        value={sfs.extra_attributes.eq_disable_track_qty_value}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                metafields_global_title_tag: (
                  <Box>
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                  </Box>
                ),
                metafields_global_description_tag: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                  </Box>
                ),
                discount_amount: sfs.extra_attributes.discount_type && (
                  <OptionEnabled>{t('label_discount_type')}</OptionEnabled>
                ),
                visible: (
                  <Box>
                    {currentStore.provider === 'bigcommerce' && (
                      <LabelAndValue
                        value={
                          sfs.extra_attributes.static_flag
                            ? t('static')
                            : t('mappings')
                        }
                      />
                    )}
                    {sfs.extra_attributes.is_visible_value && (
                      <LabelAndValue
                        label={t('label_is_visible')}
                        value={sfs.extra_attributes.is_visible_value}
                      />
                    )}
                    {sfs.extra_attributes.is_not_visible_value && (
                      <LabelAndValue
                        label={t('label_is_not_visible')}
                        value={sfs.extra_attributes.is_not_visible_value}
                      />
                    )}
                  </Box>
                ),
                sale_price: (
                  <Box>
                    {feed.feedType === 'import' &&
                      sfs.extra_attributes.language_code && (
                        <LabelAndValue
                          label={t('label_language_code')}
                          value={sfs.extra_attributes.language_code}
                        />
                      )}
                    {(sfs.extra_attributes.pricing_conditions || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.pricing_conditions || []).map(
                      (pc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={pc.condition}
                          />
                          <LabelAndValue
                            label={t('formula')}
                            value={pc.formula}
                          />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={priceRound?.value}
                      />
                    )}
                    {sfs.extra_attributes.currency_converter && (
                      <Box>
                        <LabelAndValue
                          label={t('currency_from')}
                          value={sfs.extra_attributes.default_currency}
                        />
                        <LabelAndValue
                          label={t('currency_to')}
                          value={sfs.extra_attributes.new_currency}
                        />
                      </Box>
                    )}
                    {(sfs.extra_attributes.restrict_conditions || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.restrict_conditions || []).map(
                      (rc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('column_name')}
                            value={rc.attribute}
                          />
                          <LabelAndValue label={t('value')} value={rc.value} />
                        </Stack>
                      )
                    )}
                  </Box>
                ),
                stock_status: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.in_stock_flag && (
                      <LabelAndValue
                        label={t('label_instock_flag')}
                        value={sfs.extra_attributes.in_stock_flag}
                      />
                    )}
                    {sfs.extra_attributes.out_stock_flag && (
                      <LabelAndValue
                        label={t('label_outstock_flag')}
                        value={sfs.extra_attributes.out_stock_flag}
                      />
                    )}
                  </Box>
                ),
                width: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                categories: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {feed.feedType !== 'import' &&
                      sfs.extra_attributes.override_categories && (
                        <OptionEnabled>{t('label_categories')}</OptionEnabled>
                      )}
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                  </Box>
                ),
                height: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                length: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                retail_price: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {(sfs.extra_attributes.compare_at_pricing_conditions || [])
                      .length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes.compare_at_pricing_conditions || []
                    ).map((pc, index) => (
                      <Stack
                        key={index}
                        direction="row"
                        sx={{
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                        spacing={2}
                      >
                        <LabelAndValue
                          sx={{ marginTop: '0px' }}
                          label={t('condition')}
                          value={pc.condition}
                        />
                        <LabelAndValue
                          label={t('formula')}
                          value={pc.formula}
                        />
                      </Stack>
                    ))}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={
                          compareAtPriceRoundOptions.filter(
                            (a) =>
                              a.key ===
                                sfs.extra_attributes.price_round?.toString() ||
                              ''
                          )[0]?.value
                        }
                      />
                    )}
                    {sfs.extra_attributes.currency_converter && (
                      <Box>
                        <LabelAndValue
                          label={t('currency_from')}
                          value={sfs.extra_attributes.default_currency}
                        />
                        <LabelAndValue
                          label={t('currency_to')}
                          value={sfs.extra_attributes.new_currency}
                        />
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes
                        .compare_price_at_restrict_conditions || []
                    ).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes
                        .compare_price_at_restrict_conditions || []
                    ).map((rc, index) => (
                      <Stack
                        key={index}
                        direction="row"
                        sx={{
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                        spacing={2}
                      >
                        <LabelAndValue
                          sx={{ marginTop: '0px' }}
                          label={t('column_name')}
                          value={rc.attribute}
                        />
                        <LabelAndValue label={t('value')} value={rc.value} />
                      </Stack>
                    ))}
                    {sfs.extra_attributes.force_override_compare_at_price && (
                      <OptionEnabled>
                        {t('label_force_override_compare_at_price')}
                      </OptionEnabled>
                    )}
                  </Box>
                ),
                free_shipping: (
                  <Box>
                    {sfs.extra_attributes.with_free_shipping_value && (
                      <Box>
                        <LabelAndValue
                          label={t('label_with_free_shipping')}
                          value={sfs.extra_attributes.with_free_shipping_value}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.without_free_shipping_value && (
                      <LabelAndValue
                        label={t('label_without_free_shipping')}
                        value={sfs.extra_attributes.without_free_shipping_value}
                      />
                    )}
                  </Box>
                ),
                preorder_release_date: (
                  <Box>
                    {sfs.extra_attributes.date_format && (
                      <LabelAndValue
                        label={t('label_date_format')}
                        value={sfs.extra_attributes.date_format}
                      />
                    )}
                    {sfs.extra_attributes.custom_preorder_message && (
                      <LabelAndValue
                        label={t('label_custom_preorder_message')}
                        value={sfs.extra_attributes.custom_preorder_message}
                      />
                    )}
                    {sfs.extra_attributes.remove_preoder_on_date && (
                      <OptionEnabled>
                        {t('label_remove_preorder')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.set_date_if_zero_qty && (
                      <OptionEnabled>
                        {t('label_set_date_if_zero_qty')}
                      </OptionEnabled>
                    )}
                  </Box>
                ),
                purchase_availability: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.purchase_available_flag && (
                      <Box>
                        <LabelAndValue
                          label={t('label_purchase_available_flag')}
                          value={sfs.extra_attributes.purchase_available_flag}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.purchase_preorder_flag && (
                      <LabelAndValue
                        label={t('label_purchase_preorder_flag')}
                        value={sfs.extra_attributes.purchase_preorder_flag}
                      />
                    )}
                    {sfs.extra_attributes.purchase_disabled_flag && (
                      <LabelAndValue
                        label={t('label_purchase_disabled_flag')}
                        value={sfs.extra_attributes.purchase_disabled_flag}
                      />
                    )}
                  </Box>
                ),
                description_availability: (
                  <LabelAndValue
                    value={
                      sfs.extra_attributes.static_flag
                        ? t('static')
                        : t('mappings')
                    }
                  />
                ),
                quantity_quality_control: (
                  <Box>
                    {(sfs.extra_attributes.rules_json || []).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('label_rules_json')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.rules_json || []).map(
                      (rj, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={rj.operator}
                          />
                          <LabelAndValue label={t('key')} value={rj.key} />
                          <LabelAndValue label={t('value')} value={rj.value} />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.reset_init_quantity && (
                      <OptionEnabled>{t('quantity_set_to_zero')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.eq_disable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_disable_track_qty_value')}
                        value={sfs.extra_attributes.eq_disable_track_qty_value}
                      />
                    )}
                  </Box>
                ),
                quantity_safety_stock: (
                  <Box>
                    {(sfs.extra_attributes.rules_json || []).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('label_rules_json')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.rules_json || []).map(
                      (rj, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={rj.operator}
                          />
                          <LabelAndValue label={t('key')} value={rj.key} />
                          <LabelAndValue label={t('value')} value={rj.value} />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.reset_init_quantity && (
                      <OptionEnabled>{t('quantity_set_to_zero')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.eq_disable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_disable_track_qty_value')}
                        value={sfs.extra_attributes.eq_disable_track_qty_value}
                      />
                    )}
                  </Box>
                ),
                quantity_incoming: (
                  <Box>
                    {(sfs.extra_attributes.rules_json || []).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('label_rules_json')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.rules_json || []).map(
                      (rj, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={rj.operator}
                          />
                          <LabelAndValue label={t('key')} value={rj.key} />
                          <LabelAndValue label={t('value')} value={rj.value} />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.reset_init_quantity && (
                      <OptionEnabled>{t('quantity_set_to_zero')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.eq_disable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_disable_track_qty_value')}
                        value={sfs.extra_attributes.eq_disable_track_qty_value}
                      />
                    )}
                  </Box>
                ),
                quantity_damaged: (
                  <Box>
                    {(sfs.extra_attributes.rules_json || []).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('label_rules_json')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.rules_json || []).map(
                      (rj, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={rj.operator}
                          />
                          <LabelAndValue label={t('key')} value={rj.key} />
                          <LabelAndValue label={t('value')} value={rj.value} />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.reset_init_quantity && (
                      <OptionEnabled>{t('quantity_set_to_zero')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.eq_disable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_disable_track_qty_value')}
                        value={sfs.extra_attributes.eq_disable_track_qty_value}
                      />
                    )}
                  </Box>
                ),
                quantity_reserved: (
                  <Box>
                    {(sfs.extra_attributes.rules_json || []).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('label_rules_json')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.rules_json || []).map(
                      (rj, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={rj.operator}
                          />
                          <LabelAndValue label={t('key')} value={rj.key} />
                          <LabelAndValue label={t('value')} value={rj.value} />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.reset_init_quantity && (
                      <OptionEnabled>{t('quantity_set_to_zero')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                    {sfs.extra_attributes.eq_disable_track_qty_value && (
                      <LabelAndValue
                        label={t('label_disable_track_qty_value')}
                        value={sfs.extra_attributes.eq_disable_track_qty_value}
                      />
                    )}
                  </Box>
                ),
                compare_price_at_region: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {(sfs.extra_attributes.compare_at_pricing_conditions || [])
                      .length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes.compare_at_pricing_conditions || []
                    ).map((pc, index) => (
                      <Stack
                        key={index}
                        direction="row"
                        sx={{
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                        spacing={2}
                      >
                        <LabelAndValue
                          sx={{ marginTop: '0px' }}
                          label={t('condition')}
                          value={pc.condition}
                        />
                        <LabelAndValue
                          label={t('formula')}
                          value={pc.formula}
                        />
                      </Stack>
                    ))}
                    {(sfs.extra_attributes.additional_currencies || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('additional_currencies')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.additional_currencies || []).map(
                      (ac, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('currency')}
                            value={ac.currency}
                          />
                          <LabelAndValue
                            label={t('mapping_label')}
                            value={ac.mapping}
                          />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={
                          compareAtPriceRoundOptions.filter(
                            (a) =>
                              a.key ===
                                sfs.extra_attributes.price_round?.toString() ||
                              ''
                          )[0]?.value
                        }
                      />
                    )}
                    <LabelAndValue
                      label={t('currency_code')}
                      value={sfs.extra_attributes.default_currency}
                    />
                    {(
                      sfs.extra_attributes
                        .compare_price_at_restrict_conditions || []
                    ).length > 0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(
                      sfs.extra_attributes
                        .compare_price_at_restrict_conditions || []
                    ).map((rc, index) => (
                      <Stack
                        key={index}
                        direction="row"
                        sx={{
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                        spacing={2}
                      >
                        <LabelAndValue
                          sx={{ marginTop: '0px' }}
                          label={t('column_name')}
                          value={rc.attribute}
                        />
                        <LabelAndValue label={t('value')} value={rc.value} />
                      </Stack>
                    ))}
                    {sfs.extra_attributes.force_override_compare_at_price && (
                      <OptionEnabled>
                        {t('label_force_override_compare_at_price')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                price_region: (
                  <Box>
                    {feed.feedType === 'import' &&
                      sfs.extra_attributes.language_code && (
                        <LabelAndValue
                          label={t('label_language_code')}
                          value={sfs.extra_attributes.language_code}
                        />
                      )}
                    {(sfs.extra_attributes.pricing_conditions || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('pricing_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.pricing_conditions || []).map(
                      (pc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('condition')}
                            value={pc.condition}
                          />
                          <LabelAndValue
                            label={t('formula')}
                            value={pc.formula}
                          />
                        </Stack>
                      )
                    )}
                    {(sfs.extra_attributes.additional_currencies || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('additional_currencies')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.additional_currencies || []).map(
                      (ac, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('currency')}
                            value={ac.currency}
                          />
                          <LabelAndValue
                            label={t('mapping_label')}
                            value={ac.mapping}
                          />
                        </Stack>
                      )
                    )}
                    {sfs.extra_attributes.price_delimiter && (
                      <Box>
                        <LabelAndValue
                          label={t('delimiter')}
                          value={t(priceDelimiter?.value)}
                        />
                      </Box>
                    )}
                    {sfs.extra_attributes.price_round && (
                      <LabelAndValue
                        label={t('round')}
                        value={priceRound?.value}
                      />
                    )}
                    <LabelAndValue
                      label={t('currency_code')}
                      value={sfs.extra_attributes.default_currency}
                    />
                    {(sfs.extra_attributes.restrict_conditions || []).length >
                      0 && (
                      <Box sx={{ marginTop: '8px' }}>
                        <LabelTag>{t('restrict_conditions')}</LabelTag>
                      </Box>
                    )}
                    {(sfs.extra_attributes.restrict_conditions || []).map(
                      (rc, index) => (
                        <Stack
                          key={index}
                          direction="row"
                          sx={{
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                          spacing={2}
                        >
                          <LabelAndValue
                            sx={{ marginTop: '0px' }}
                            label={t('column_name')}
                            value={rc.attribute}
                          />
                          <LabelAndValue label={t('value')} value={rc.value} />
                        </Stack>
                      )
                    )}
                  </Box>
                ),
                short_description: (
                  <>
                    <LabelAndValue
                      label={t('label_case_convert')}
                      value={
                        sfs.extra_attributes.case_convert
                          ? caseConvert?.value
                          : 'No changes'
                      }
                    />
                    {sfs.extra_attributes.find && (
                      <LabelAndValue
                        label={t('find')}
                        value={sfs.extra_attributes.find}
                      />
                    )}
                    {sfs.extra_attributes.replace && (
                      <LabelAndValue
                        label={t('replace')}
                        value={sfs.extra_attributes.replace}
                      />
                    )}
                    <LabelAndValue
                      label={t('wrap_tag')}
                      value={wrapTag?.value}
                    />
                    {sfs.extra_attributes.labels && (
                      <LabelAndValue
                        label={t('label_body_labels')}
                        value={sfs.extra_attributes.labels}
                      />
                    )}
                    {sfs.extra_attributes.original_language && (
                      <LabelAndValue
                        label={t('translate_from')}
                        value={
                          languages.find(
                            (a) =>
                              a.key === sfs.extra_attributes.original_language
                          )?.value
                        }
                      />
                    )}
                    {sfs.extra_attributes.returned_language && (
                      <LabelAndValue
                        label={t('translate_to')}
                        value={
                          languages.find(
                            (a) =>
                              a.key === sfs.extra_attributes.returned_language
                          )?.value
                        }
                      />
                    )}
                    {feed.humanizeFeedType === 'update' &&
                      currentStore.provider === 'shopify' &&
                      sfs.extra_attributes.update_only_if_nil && (
                        <OptionEnabled>{t('update_only_if_nil')}</OptionEnabled>
                      )}
                    {sfs.extra_attributes.convert_line_break && (
                      <OptionEnabled>
                        {t('label_convert_line_break')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.force_override_description && (
                      <OptionEnabled>
                        {t('label_force_override_description')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_zero_blank_labels && (
                      <OptionEnabled>
                        {t('label_skip_zero_blank_labels')}
                      </OptionEnabled>
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </>
                ),
                condition: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.condition_is_new && (
                      <LabelAndValue
                        label={t('new')}
                        value={sfs.extra_attributes.condition_is_new}
                      />
                    )}
                    {sfs.extra_attributes.condition_is_used && (
                      <LabelAndValue
                        label={t('used')}
                        value={sfs.extra_attributes.condition_is_used}
                      />
                    )}
                    {sfs.extra_attributes.condition_is_refurbished && (
                      <LabelAndValue
                        label={t('refurbished')}
                        value={sfs.extra_attributes.condition_is_refurbished}
                      />
                    )}
                    {sfs.extra_attributes.skip_if_blank && (
                      <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                    )}
                  </Box>
                ),
                show_condition: (
                  <Box>
                    <LabelAndValue
                      value={
                        sfs.extra_attributes.static_flag
                          ? t('static')
                          : t('mappings')
                      }
                    />
                    {sfs.extra_attributes.enable_condition && (
                      <LabelAndValue
                        label={t('enabled_indicator')}
                        value={sfs.extra_attributes.enable_condition}
                      />
                    )}
                    {sfs.extra_attributes.disable_condition && (
                      <LabelAndValue
                        label={t('disabled_indicator')}
                        value={sfs.extra_attributes.disable_condition}
                      />
                    )}
                  </Box>
                ),
              }[sfs.field_name] ?? (
                <Box sx={{ marginTop: '8px' }}>{t('no_extra_settings')}</Box>
              )}
            </FieldAndColumnName>
          </React.Fragment>
        );
      })}

      {feed.productOptionsSyncField?.map((posf, index) => {
        const caseConvert = caseConvertOptions.filter(
          (a) => a.key === posf.extra_attributes.case_convert
        )[0];

        return (
          <React.Fragment key={index}>
            <Box sx={{ m: theme.spacing(2, 0) }} />
            <FieldAndColumnName
              open={openCollapse === posf.field_name || showAllDetail}
              onClick={() =>
                openCollapse !== posf.field_name
                  ? setOpenCollapse(posf.field_name)
                  : setOpenCollapse('')
              }
              fieldName={t(posf.field_name)}
              columnName={posf.field_mapping ?? ''}
            >
              {posf.field_name.includes('variant_group') && feed.importSort && (
                <OptionEnabled>{t('import_sort_label')}</OptionEnabled>
              )}
              {posf.field_name.includes('variant_group') &&
                posf.extra_attributes.existing_product_identifier !== '' && (
                  <>
                    <OptionEnabled>{t('merge_variants')}</OptionEnabled>
                    <Box sx={{ paddingLeft: '20px' }}>
                      <span>
                        {t(
                          posf.extra_attributes.existing_product_identifier ??
                            ''
                        )}
                      </span>
                    </Box>
                  </>
                )}
              {posf.field_name.includes('option') && (
                <Box>
                  <LabelAndValue
                    label={t(
                      posf.extra_attributes.dynamic_option_name
                        ? t('label_mapping')
                        : t('label_option_name')
                    )}
                    value={posf.extra_attributes[`${posf.field_name}_name`]}
                  />
                  <LabelAndValue
                    value={
                      posf.extra_attributes.dynamic_option_name
                        ? t('mappings')
                        : t('static')
                    }
                  />
                  <LabelAndValue
                    label={t('label_case_convert')}
                    value={
                      posf.extra_attributes.case_convert
                        ? caseConvert?.value
                        : 'No changes'
                    }
                  />
                  {posf.extra_attributes.find && (
                    <LabelAndValue
                      label={t('find')}
                      value={posf.extra_attributes.find}
                    />
                  )}
                  {posf.extra_attributes.replace && (
                    <LabelAndValue
                      label={t('replace')}
                      value={posf.extra_attributes.replace}
                    />
                  )}
                  {feed.humanizeFeedType === 'export' &&
                    posf.extra_attributes.auto_remove_default_title && (
                      <OptionEnabled>
                        {t('auto_remove_default_title')}
                      </OptionEnabled>
                    )}
                </Box>
              )}
            </FieldAndColumnName>
          </React.Fragment>
        );
      })}
      {feed.metafields?.map((metafield, index) => (
        <React.Fragment key={index}>
          <Box sx={{ m: theme.spacing(2, 0) }} />
          <FieldAndColumnName
            open={openCollapse === metafield.field_name || showAllDetail}
            onClick={() =>
              openCollapse !== metafield.field_name
                ? setOpenCollapse(metafield.field_name)
                : setOpenCollapse('')
            }
            fieldName={match([currentStore.provider])
              .with(['shopify'], () => (
                <span>
                  {t('metafield')} {metafield.field_name.substring(10)}
                </span>
              ))
              .with(['bigcommerce'], () => (
                <span>
                  {t('custom_field')} {metafield.field_name.substring(13)}
                </span>
              ))
              .with(['woocommerce'], () => (
                <span>
                  {t('metadata')} {metafield.field_name.substring(9)}
                </span>
              ))
              .otherwise(() => '')}
            columnName={metafield.field_mapping ?? ''}
          >
            {currentStore.provider === 'shopify' ? (
              <Box>
                <LabelAndValue
                  value={
                    metafield.extra_attributes.static_flag
                      ? t('static')
                      : t('mappings')
                  }
                />
                <LabelAndValue
                  label={t('metafield_namespace_and_key')}
                  value={`${
                    metafield.extra_attributes.metafield_namespace ?? ''
                  }.${metafield.extra_attributes.metafield_key}`}
                />
                <LabelAndValue
                  label={t('label_metafield_owner')}
                  value={t(metafield.extra_attributes.metafield_owner ?? '')}
                />
                <LabelAndValue
                  label={t('label_metafield_type')}
                  value={t(metafield.extra_attributes.metafield_type ?? '')}
                />
                {['date', 'date_time'].includes(
                  metafield.extra_attributes.metafield_type ?? ''
                ) &&
                  metafield.extra_attributes.metafield_date_format && (
                    <LabelAndValue
                      label={t('label_date_format')}
                      value={metafield.extra_attributes.metafield_date_format}
                    />
                  )}
                <LabelAndValue
                  label={t('label_blank_val')}
                  value={metafield.extra_attributes.blank_val}
                />
                {['weight'].includes(
                  metafield.extra_attributes.metafield_type ?? ''
                ) && (
                  <LabelAndValue
                    label={t('label_metafield_weight_unit')}
                    value={t(metafield.extra_attributes.metafield_weight_unit)}
                  />
                )}
                {['dimension', 'list.dimension'].includes(
                  metafield.extra_attributes.metafield_type ?? ''
                ) && (
                  <LabelAndValue
                    label={t('label_metafield_dimension_unit')}
                    value={t(
                      metafield.extra_attributes.metafield_dimension_unit
                    )}
                  />
                )}
                {['volume'].includes(
                  metafield.extra_attributes.metafield_type ?? ''
                ) && (
                  <LabelAndValue
                    label={t('label_metafield_volume_unit')}
                    value={t(metafield.extra_attributes.metafield_volume_unit)}
                  />
                )}
                {['boolean'].includes(
                  metafield.extra_attributes.metafield_type ?? ''
                ) && (
                  <>
                    {metafield.extra_attributes.is_true_indicator && (
                      <LabelAndValue
                        label={t('metafield_indicator')}
                        value={metafield.extra_attributes.is_true_indicator}
                      />
                    )}
                    {metafield.extra_attributes.is_false_indicator && (
                      <LabelAndValue
                        label={t('non_metafield_indicator')}
                        value={metafield.extra_attributes.is_false_indicator}
                      />
                    )}
                  </>
                )}
                {metafield.extra_attributes.skip_if_blank && (
                  <OptionEnabled>{t('skip_if_empty')}</OptionEnabled>
                )}
                {metafield.extra_attributes.remove_if_blank && (
                  <OptionEnabled>{t('remove_if_blank')}</OptionEnabled>
                )}
              </Box>
            ) : (
              <Box>
                {metafield.extra_attributes.custom_field_key && (
                  <LabelAndValue
                    label={t('label_custom_field_name')}
                    value={metafield.extra_attributes.custom_field_key}
                  />
                )}
                {metafield.extra_attributes.create_key_if_not_found && (
                  <OptionEnabled>
                    {t('label_create_custom_field_if_not_found')}
                  </OptionEnabled>
                )}
              </Box>
            )}
          </FieldAndColumnName>
        </React.Fragment>
      ))}
      {feed.woocommerceProductAttributes?.map((wpa, index) => (
        <React.Fragment key={index}>
          <Box sx={{ m: theme.spacing(2, 0) }} />
          <FieldAndColumnName
            open={openCollapse === wpa.field_name || showAllDetail}
            onClick={() =>
              openCollapse !== wpa.field_name
                ? setOpenCollapse(wpa.field_name)
                : setOpenCollapse('')
            }
            fieldName={
              <span>
                {t('product_attribute')} {wpa.field_name.substring(18)}
              </span>
            }
            columnName={wpa.field_mapping ?? ''}
          >
            {wpa.extra_attributes.custom_field_key && (
              <LabelAndValue
                label={t('label_custom_field_name')}
                value={wpa.extra_attributes.custom_field_key}
              />
            )}
            {wpa.extra_attributes.create_key_if_not_found && (
              <OptionEnabled>
                {t('label_create_custom_field_if_not_found')}
              </OptionEnabled>
            )}
          </FieldAndColumnName>
        </React.Fragment>
      ))}
    </Box>
  );
}

interface CollectionItem {
  id: string;
  title: string;
}

function useShopifyCollections(haveCollectionFilter) {
  const { currentStore } = useCurrentStore();
  const [{ data, fetching }] = useQuery({
    query: GetCollections,
    pause: currentStore.provider !== 'shopify' || !haveCollectionFilter,
  });
  return {
    fetching,
    collections: React.useMemo<CollectionItem[]>(
      () =>
        data?.getCollections.map((o) => ({
          id: String(o.id),
          title: o.title,
        })) ?? [],
      [data]
    ),
  };
}

function useWixCollections() {
  const { currentStore } = useCurrentStore();
  const [{ data, fetching }] = useQuery({
    query: GetCollectionsFromWix,
    pause: currentStore.provider !== 'wix',
  });
  return {
    fetching,
    collections: React.useMemo<CollectionItem[]>(
      () =>
        data?.getCollectionsFromWix.map((o) => ({
          id: String(o.key),
          title: o.value,
        })) ?? [],
      [data]
    ),
  };
}

function useWoocommerceCategories() {
  const { currentStore } = useCurrentStore();
  const [{ data, fetching }] = useQuery({
    query: GetWoocommerceCategories,
    pause: currentStore.provider !== 'woocommerce',
  });
  return {
    fetching,
    collections: React.useMemo<CollectionItem[]>(
      () =>
        data?.getWoocommerceCategories.map((o) => ({
          id: String(o.key),
          title: o.value,
        })) ?? [],
      [data]
    ),
  };
}

function useWoocommerceTags() {
  const { currentStore } = useCurrentStore();
  const [{ data, fetching }] = useQuery({
    query: GetWoocommerceTags,
    pause: currentStore.provider !== 'woocommerce',
  });
  return {
    fetching,
    collections: React.useMemo<CollectionItem[]>(
      () =>
        data?.getWoocommerceTags.map((o) => ({
          id: String(o.key),
          title: o.value,
        })) ?? [],
      [data]
    ),
  };
}

function useBigcommerceBrands() {
  const { currentStore } = useCurrentStore();
  const [{ data, fetching }] = useQuery({
    query: GetBigcommerceBrands,
    pause: currentStore.provider !== 'bigcommerce',
  });
  return {
    fetching,
    collections: React.useMemo<CollectionItem[]>(
      () =>
        data?.getBigcommerceBrands.map((o) => ({
          id: String(o.key),
          title: o.value,
        })) ?? [],
      [data]
    ),
  };
}

interface FilterSummaryProps {
  feedId: UserProfile['id'];
  showAllDetail: boolean;
}

function FilterSummary({ feedId, showAllDetail }: FilterSummaryProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const feedQuery = useSettingsFeed({ id: Number(feedId) });
  const feed = feedQuery.feed!;
  const [openCollapse, setOpenCollapse] = React.useState('');

  const storeFiltersFilterOutBooleanAsValue = React.useMemo(
    () =>
      feed.storeFilters.filter(
        (a) => typeof a.conditions[0].value !== 'boolean'
      ),
    [feed]
  );

  const haveCollectionFilter = storeFiltersFilterOutBooleanAsValue.find(
    (a) => a.key === 'collection'
  );

  const storeFiltersFindBooleanAsValue = React.useMemo(
    () => feed.storeFilters.filter((a) => a.conditions[0].value === true),
    [feed]
  );
  const shopify = useShopifyCollections(haveCollectionFilter);
  const wix = useWixCollections();
  const woocommerceCategories = useWoocommerceCategories();
  const woocommerceTags = useWoocommerceTags();
  const bigcommerce = useBigcommerceBrands();

  return (
    <Box>
      {feed.feedType !== 'import' && (
        <>
          <Typography variant="body1" sx={{ marginBottom: '8px' }}>
            {t('store_product_filter')}
          </Typography>
          {storeFiltersFilterOutBooleanAsValue?.length > 0
            ? storeFiltersFilterOutBooleanAsValue?.map((sf, index) => {
                const collectionsLoading = match({
                  provider: currentStore.provider,
                  fieldKey: sf.key,
                })
                  .returnType<boolean>()
                  .with({ provider: 'wix' }, () => wix.fetching)
                  .with({ provider: 'bigcommerce' }, () => bigcommerce.fetching)
                  .with(
                    { provider: 'woocommerce', fieldKey: 'tags' },
                    () => woocommerceTags.fetching
                  )
                  .with(
                    { provider: 'woocommerce' },
                    () => woocommerceCategories.fetching
                  )
                  .with({ provider: 'shopify' }, () => shopify.fetching)
                  .otherwise(() => false);

                const collections = match({
                  provider: currentStore.provider,
                  fieldKey: sf.key,
                })
                  .with({ provider: 'shopify' }, () => shopify.collections)
                  .with({ provider: 'wix' }, () => wix.collections)
                  .with(
                    { provider: 'woocommerce', fieldKey: 'tags' },
                    () => woocommerceTags.collections
                  )
                  .with(
                    { provider: 'woocommerce' },
                    () => woocommerceCategories.collections
                  )
                  .with(
                    { provider: 'bigcommerce' },
                    () => bigcommerce.collections
                  )
                  .otherwise(() => []);

                const findCollectionTitle = (searchKey) =>
                  collections.find((o) => o.id === searchKey)?.title ||
                  searchKey ||
                  '-';

                return (
                  <React.Fragment key={index}>
                    <Box sx={{ marginBottom: '16px' }}>
                      <FieldAndColumnName
                        open={openCollapse === sf.key || showAllDetail}
                        onClick={() =>
                          openCollapse !== sf.key
                            ? setOpenCollapse(sf.key)
                            : setOpenCollapse('')
                        }
                        fieldName={t(sf.key)}
                      >
                        {sf.conditions?.map((condition, index) => {
                          return (
                            <Box key={index}>
                              <LabelAndValue
                                label={
                                  sf.key === 'published'
                                    ? t('contains')
                                    : t(condition.operator)
                                }
                                value={match({
                                  fieldKey: sf.key,
                                  loading: collectionsLoading,
                                  conditionValue: t(String(condition.value)),
                                })
                                  .with({ loading: true }, () => '-')
                                  .otherwise(({ conditionValue, fieldKey }) =>
                                    match(fieldKey)
                                      .with(
                                        'brand',
                                        'category',
                                        'collection',
                                        'tags',
                                        () =>
                                          conditionValue
                                            .split(',')
                                            .map((id) =>
                                              findCollectionTitle(id)
                                            )
                                            .join(', ')
                                      )
                                      .otherwise(() =>
                                        findCollectionTitle(conditionValue)
                                      )
                                  )}
                              />
                            </Box>
                          );
                        })}
                        {feed.feedType !== 'import' &&
                          storeFiltersFilterOutBooleanAsValue?.length !==
                            index + 1 && (
                            <Box sx={{ m: theme.spacing(2, 0) }} />
                          )}
                      </FieldAndColumnName>
                    </Box>
                  </React.Fragment>
                );
              })
            : '-'}

          {storeFiltersFindBooleanAsValue?.length > 0 &&
            storeFiltersFindBooleanAsValue?.map((sf) => (
              <Box key={sf.key}>
                <OptionEnabled>{sf.key}</OptionEnabled>
              </Box>
            ))}
        </>
      )}
      {feed.feedFilters?.length > 0 && (
        <>
          <Box sx={{ m: theme.spacing(2, 0) }} />
          <Typography variant="body1" sx={{ marginBottom: '8px' }}>
            {t('incoming_feed_filter')}
          </Typography>
        </>
      )}
      {feed.feedFilters?.map((ff, index) => (
        <React.Fragment key={index}>
          <Box sx={{ m: theme.spacing(2, 0) }} />
          <FieldAndColumnName
            open={openCollapse === ff.key || showAllDetail}
            onClick={() =>
              openCollapse !== ff.key
                ? setOpenCollapse(ff.key)
                : setOpenCollapse('')
            }
            fieldName={t(ff.key)}
          >
            <LabelAndValue label={t(ff.op)} value={ff.value} />
          </FieldAndColumnName>
        </React.Fragment>
      ))}

      {feed.bypassBlankRow && (
        <OptionEnabled>{t('bypass_blank_row_label')}</OptionEnabled>
      )}
    </Box>
  );
}

interface AdvancedSettingSummaryProps {
  feedId: UserProfile['id'];
  showAllDetail: boolean;
}

function AdvancedSettingSummary({
  feedId,
  showAllDetail,
}: AdvancedSettingSummaryProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { currentStore } = useCurrentStore();
  const feedQuery = useSettingsFeed({ id: Number(feedId) });
  const feed = feedQuery.feed!;
  const [openCollapse, setOpenCollapse] = React.useState('');

  const isRemoveAdvanceSettingOptionEnabled =
    (['shopify', 'woocommerce'].includes(currentStore.provider) &&
      feed.deleteMode === 1) ||
    feed.partialMatch ||
    feed.updateDuplicateProductKey ||
    feed.removeProductWhenAllLocationsNil;
  const isImportAdvanceSettingOptionEnabled =
    feed.publishedApplyToAll || feed.importTags !== '';
  const isOutOfStockOptionEnabled =
    feed.hideUnmatchProducts || feed.autoResetQuantity;

  return (
    <Box>
      {{
        update: ['squarespace', 'quickbooks', 'square'].includes(
          currentStore.provider
        ) ? (
          <OptionEnabled>{t('auto_completed')}</OptionEnabled>
        ) : (
          <Box
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'initial',
            }}
          >
            <FieldAndColumnName
              open={openCollapse === 'auto_publish_products' || showAllDetail}
              onClick={() =>
                openCollapse !== 'auto_publish_products'
                  ? setOpenCollapse('auto_publish_products')
                  : setOpenCollapse('')
              }
              fieldName={t('auto_publish_products')}
            >
              <LabelAndValue
                label={t('contains')}
                value={
                  (feed.publishedApplyMatchingProducts &&
                    t('product_with_feed_file')) ||
                  (feed.publishedApplyToAll && t('all_product_in_store')) ||
                  t('do_not_apply')
                }
              />
            </FieldAndColumnName>
            <Box sx={{ m: theme.spacing(2, 0) }} />
            <FieldAndColumnName
              open={openCollapse === 'auto_archive_products' || showAllDetail}
              onClick={() =>
                openCollapse !== 'auto_archive_products'
                  ? setOpenCollapse('auto_archive_products')
                  : setOpenCollapse('')
              }
              fieldName={
                ['shopify'].includes(currentStore.provider)
                  ? t('hide_and_archive_product_online_store')
                  : t('hide_product_online_store')
              }
            >
              <LabelAndValue
                label={t('contains')}
                value={match({
                  deleteMode: feed.deleteMode,
                  unpublishedApplyToAll: feed.unpublishedApplyToAll,
                  unpublishedApplyMatchingProducts:
                    feed.unpublishedApplyMatchingProducts,
                })
                  .with({ deleteMode: 1, unpublishedApplyToAll: true }, () =>
                    t('make_product_archive')
                  )
                  .with({ deleteMode: null, unpublishedApplyToAll: true }, () =>
                    t('make_product_unpublish')
                  )
                  .with(
                    { deleteMode: 1, unpublishedApplyMatchingProducts: true },
                    () => t('make_product_archive_match')
                  )
                  .with(
                    {
                      deleteMode: null,
                      unpublishedApplyMatchingProducts: true,
                    },
                    () => t('make_product_unpublish_match')
                  )
                  .otherwise(() => t('do_not_apply'))}
              />
            </FieldAndColumnName>
            {isOutOfStockOptionEnabled && (
              <Box>
                <Box sx={{ m: theme.spacing(2, 0) }} />
                <Typography variant="body1" sx={{ marginBottom: '8px' }}>
                  {t('out_of_stock_handling')}
                </Typography>
                {feed.hideUnmatchProducts && (
                  <OptionEnabled>
                    {t('set_out_of_stock_to_zero_label')}
                  </OptionEnabled>
                )}
                {feed.autoResetQuantity && (
                  <OptionEnabled>
                    {t('set_out_of_stock_to_label')}
                  </OptionEnabled>
                )}
              </Box>
            )}
            {feed.updateDuplicateProductKey &&
              feed.humanizeFeedType === 'remove' && (
                <OptionEnabled>
                  {t('apply_same_product_id_for_archive')}
                </OptionEnabled>
              )}
            {feed.updateProductLevel && (
              <OptionEnabled>{t('update_on_product_level')}</OptionEnabled>
            )}
            {feed.bigcommerceRetainAvailability && (
              <OptionEnabled>
                {t('dont_update_based_on_inventory_level')}
              </OptionEnabled>
            )}
          </Box>
        ),

        import: ['ekm'].includes(currentStore.provider) ? (
          <OptionEnabled>{t('auto_completed')}</OptionEnabled>
        ) : isImportAdvanceSettingOptionEnabled ? (
          <Box
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'initial',
            }}
          >
            {feed.publishedApplyToAll && (
              <OptionEnabled>{t('import_auto_publish')}</OptionEnabled>
            )}
            {feed.importTags && (
              <Typography variant="body2" sx={{ marginTop: '8px' }}>
                {' '}
                {t('import_new_tags')}{' '}
                <Box component="span" sx={{ fontWeight: '600' }}>
                  {feed.importTags}
                </Box>
              </Typography>
            )}
          </Box>
        ) : (
          '-'
        ),
        export: <OptionEnabled>{t('auto_completed')}</OptionEnabled>,
        remove: isRemoveAdvanceSettingOptionEnabled ? (
          <Box
            sx={{
              wordBreak: 'break-word',
              whiteSpace: 'initial',
            }}
          >
            {['shopify', 'woocommerce'].includes(currentStore.provider) &&
              feed.deleteMode === 1 && (
                <OptionEnabled>
                  {currentStore.provider === 'shopify'
                    ? t('archive_products_feed_setting_summary')
                    : t('archive_products_wc')}
                </OptionEnabled>
              )}
            {feed.partialMatch && (
              <OptionEnabled>{t('partials_match')}</OptionEnabled>
            )}
            {feed.updateDuplicateProductKey &&
              feed.humanizeFeedType === 'remove' && (
                <OptionEnabled>
                  {t(
                    feed.deleteMode === 1
                      ? 'apply_same_product_id_for_archive'
                      : 'apply_same_product_id_for_remove'
                  )}
                </OptionEnabled>
              )}
            {feed.removeProductWhenAllLocationsNil && (
              <OptionEnabled>
                {t(
                  feed.deleteMode === 1
                    ? 'archive_product_when_all_locations_nil'
                    : 'remove_product_when_all_locations_nil'
                )}
              </OptionEnabled>
            )}
          </Box>
        ) : (
          t('auto_completed')
        ),
      }[feed.humanizeFeedType] ?? ''}
    </Box>
  );
}

interface FieldAndColumnNameProps {
  fieldName: React.ReactNode;
  columnName?: string;
  children: React.ReactNode;
  onClick: () => void;
  open: boolean;
}

function FieldAndColumnName({
  fieldName,
  columnName,
  children,
  onClick,
  open,
}: FieldAndColumnNameProps) {
  const theme = useTheme();
  return (
    <Card
      sx={{
        padding: '0px',
        border: '1px solid #e0e0e0',
        width: '100%',
        boxShadow: 0,
      }}
    >
      <CardHeader
        sx={{
          background: open ? '#6e6d7a0F' : 'initial',
          '&:hover': { background: '#f5f7f8' },
          '&:focus': { background: '#f5f7f8' },
          cursor: 'pointer',
          padding: '16px',
        }}
        title={
          <Box onClick={onClick}>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.grey[300],
                wordBreak: 'break-word',
                whiteSpace: 'initial',
              }}
            >
              {fieldName}
            </Typography>
            {columnName && (
              <Stack
                direction="row"
                spacing={2}
                sx={{ marginTop: '8px', color: theme.palette.grey[300] }}
              >
                <Icon type="default" icon={faArrowRight} fontSize={14} />
                <Typography
                  variant="body2"
                  sx={{
                    color: theme.palette.common.black,
                    wordBreak: 'break-word',
                    whiteSpace: 'initial',
                  }}
                >
                  {columnName}
                </Typography>
              </Stack>
            )}
          </Box>
        }
      />

      <CardContent
        sx={{
          padding: '0px',
          height: 'auto',
          '&:last-child': {
            paddingBottom: '0px',
            borderBottom: 'unset',
          },
        }}
      >
        <Collapse in={open}>
          <Box sx={{ padding: '16px', marginLeft: '16px' }}>{children}</Box>
        </Collapse>
      </CardContent>
    </Card>
  );
}

function OptionEnabled({ children }) {
  const theme = useTheme();
  return (
    <Stack
      direction="row"
      spacing={1}
      sx={{
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        marginTop: '8px',
      }}
    >
      <Icon
        type="default"
        icon={faCircleCheck}
        fontSize={14}
        style={{ marginTop: '2px' }}
      />
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.common.black,
          wordBreak: 'break-word',
          whiteSpace: 'initial',
        }}
      >
        {children}
      </Typography>
    </Stack>
  );
}

function LabelTag({ children }) {
  return (
    <Box
      sx={{
        background: '#f5f7f8',
        padding: '8px',
        borderRadius: '8px',
        fontSize: '14px',
        width: 'fit-content',
        marginBottom: '8px',
        whiteSpace: 'initial',
      }}
    >
      {children}
    </Box>
  );
}

interface LabelAndValueProps extends Pick<TypographyProps, 'sx'> {
  value: React.ReactNode;
  label?: React.ReactNode;
}

function LabelAndValue({ label, value, sx }: LabelAndValueProps) {
  const theme = useTheme();
  return (
    <Box
      sx={{
        marginTop: '8px',
        ...sx,
      }}
    >
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.grey[300],
          wordBreak: 'break-word',
          whiteSpace: 'initial',
        }}
      >
        {label}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.common.black,
          wordBreak: 'break-word',
          whiteSpace: 'initial',
        }}
      >
        {value}
      </Typography>
    </Box>
  );
}
