import {
  Collapse as Mui<PERSON>ollap<PERSON>,
  useTheme,
  type CollapseProps as MuiCollapseProps,
} from '@mui/material';

export type CollapseProps = Pick<MuiCollapseProps, 'in' | 'children'>;

export function Collapse(props: CollapseProps) {
  const theme = useTheme();
  return (
    <MuiCollapse
      in={props.in}
      timeout={theme.transitions.duration.shortest}
      unmountOnExit
      easing={theme.transitions.easing.easeInOut}
    >
      {props.children}
    </MuiCollapse>
  );
}
