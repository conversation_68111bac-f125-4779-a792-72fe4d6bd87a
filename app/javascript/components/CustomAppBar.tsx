import { faArrowLeft, faBars } from '@fortawesome/pro-light-svg-icons';
import {
  AppBar,
  Box,
  Stack,
  Typography,
  useTheme,
  type AppBarProps,
} from '@mui/material';
import * as React from 'react';

import { Button } from '@/components/Button';
import { Icon, type IconProps } from '@/components/Icon';
import { IconButton } from '@/components/IconButton';
import { Link } from '@/components/Link';
import { useIsEmbeddedApp } from '@/hooks/useIsEmbeddedApp';
import { useMatches } from '@/hooks/useMatches';
import { useNavbar } from '@/hooks/useNavbar';

interface CustomAppBarProps {
  title: React.ReactNode;
  redirectUrl?: string;
  btnGroup?: React.ReactNode;
  children?: React.ReactNode;
  iconProps?: IconProps;
  sx?: AppBarProps['sx'];
  description?: React.ReactNode;
}

export const CustomAppBar = React.forwardRef<HTMLDivElement, CustomAppBarProps>(
  (props, forwardedRef) => {
    const { title, redirectUrl, btnGroup, sx, children, description } = props;
    const theme = useTheme();
    const navbar = useNavbar();
    const { isEmbeddedApp } = useIsEmbeddedApp();
    const smAndDown = useMatches('sm');

    return (
      <AppBar
        ref={forwardedRef}
        position="sticky"
        sx={{
          boxShadow: 'none',
          backgroundColor: theme.palette.background.default,
          color: theme.palette.grey[300],
          borderRadius: 0,
          padding: '24px 0 10px',
          marginBottom: '32px',
          ...sx,
        }}
      >
        {/* Logo & Hamburger */}
        <Stack
          direction="row"
          sx={{
            justifyContent: 'space-between',
            display: { xs: 'flex', sm: 'none' },
          }}
        >
          {isEmbeddedApp ? (
            <></>
          ) : (
            <IconButton
              onClick={() => navbar.toggle()}
              sx={{ color: theme.palette.grey[300] }}
              id="on-sidenavbar"
            >
              <Icon type="default" icon={faBars} />
            </IconButton>
          )}
        </Stack>
        <Stack sx={{ paddingTop: { xs: 1, sm: 0 } }} />

        {/* Show title + other components on header*/}
        <Box
          sx={{
            width: '100%',
            maxWidth: '998px',
            marginLeft: 'auto',
            marginRight: 'auto',
          }}
        >
          <Stack direction="row" spacing={{ xs: 0, sm: 1 }}>
            {!navbar.open && (
              <Button
                variant="outlined"
                onClick={() => navbar.toggle()}
                id="off-sidenavbar"
                sx={{
                  display: { xs: 'none', sm: 'inherit' },
                  color: theme.palette.grey[300],
                  padding: '5px 6px',
                  border: 'none',
                  fontSize: '22px',
                }}
              >
                <Icon type="default" icon={faBars} />
              </Button>
            )}
            <Stack
              sx={{
                width: '100%',
                overflow: 'hidden',
                paddingLeft: { xs: '6px', sm: 0 },
              }}
              spacing={{ xs: '22px', sm: '18px' }}
            >
              <Stack
                direction={smAndDown ? 'column' : 'row'}
                spacing={2}
                sx={{
                  justifyContent: 'space-between',
                  alignItems: smAndDown ? 'unset' : 'center',
                  marginTop: btnGroup ? '2px' : '8px',
                }}
              >
                <Stack
                  direction="row"
                  spacing={1}
                  sx={{
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    height: '47px',
                  }}
                >
                  {redirectUrl && (
                    <Link
                      to={redirectUrl}
                      id="back-to-url"
                      sx={{
                        marginTop: '5px',
                        color: theme.palette.common.black,
                        '&:hover': {
                          color: theme.palette.common.black,
                        },
                      }}
                    >
                      <Icon type="default" icon={faArrowLeft} fontSize={18} />
                    </Link>
                  )}
                  <Box sx={{ display: 'inline' }}>
                    <Typography
                      variant="h4"
                      sx={{
                        color: theme.palette.common.black,
                        fontWeight: 600,
                      }}
                    >
                      {title}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: theme.palette.grey[300],
                        paddingBottom: '3px',
                      }}
                    >
                      {description}
                    </Typography>
                  </Box>
                </Stack>

                <div>{btnGroup}</div>
              </Stack>

              {children}
            </Stack>
          </Stack>
        </Box>
      </AppBar>
    );
  }
);
