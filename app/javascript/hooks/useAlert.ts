import { atom, useAtom } from 'jotai';
import * as React from 'react';

const defaultAtom = atom('');

export function useAlert() {
  const [message, setMessage] = useAtom(defaultAtom);

  React.useEffect(() => {
    if (!message) return;

    const interval = setTimeout(() => {
      setMessage('');
    }, 5000);
    return () => {
      clearInterval(interval);
    };
  }, [message, setMessage]);

  return { message, setMessage: (message: string) => setMessage(message) };
}
