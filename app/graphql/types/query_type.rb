module Types
  class QueryType < Types::BaseObject
    # ? users api methods
    field :current_store, StoreType, null: false
    def current_store
      store = context[:current_shop]
      raise GraphQL::ExecutionError.new("Unauthenticated") if store.blank?
      raise GraphQL::ExecutionError.new("store_plan_not_valid") if store.plan.blank?
      store
    end

    field :get_news_feed, NewsFeedType, null: false do
      argument :limit, Integer, required: false, default_value: 1
    end
    def get_news_feed(limit: nil)
      NewsFeed.where(published: true).order("created_at desc").limit(limit)
    end

    field :get_recommend_products_batch, GraphQL::Types::JSON, null: false,
      description: "Recommend products batch" do
        argument :terms, String, required: true, default_value: "[]"
        argument :service, String, required: false
        argument :options, Types::HashObjectType, default_value: {}
      end
    def get_recommend_products_batch(terms:, service:, options:)
      data = nil
      message = nil
      begin
        terms = JSON.parse(terms)
        unless terms.is_a?(Array)
          raise StandardError.new "Must pass terms as an array"
        end
        service = "RecommendProduct::#{service.present? ? service : "Alibaba"}".constantize
        data = service.send(:search_data_batch, terms) # ,params[:options])
        unless data.present?
          raise StandardError.new "Invalid cache data"
        end
      rescue => e
        message = e.message
      end
      if message.present?
        {error: {message: :invalid_operation, details: message}}
      else
        {data: {result: data.nil? ? {} : data, status: !data.nil?}}
      end
    end

    field :get_recommend_products, GraphQL::Types::JSON, null: false,
      description: "Recommend products" do
        argument :term, String, required: false
        argument :service, String, required: false
        argument :user_profile_id, String, required: false
      end
    def get_recommend_products(term: nil, service: nil, user_profile_id: nil)
      data = nil
      quantity = nil
      flag = false
      message = nil
      if user_profile_id.present? # && current_store.public_token == params[:shop]
        begin
          quantity = Rails.cache.read("#{user_profile_id}/low_stock_title")[:quantity]
        rescue
          quantity = nil
          message = "Invalid cache data"
        end
        begin
          term = Rails.cache.read("#{user_profile_id}/low_stock_title")[:title]
        rescue
          term = nil
          message = "Invalid cache data"
        end
        flag = true
      end
      if term.present?
        begin
          recommend_product_service = "RecommendProduct::#{service.present? ? service : "Alibaba"}".constantize
          data = recommend_product_service.send(:search_data, term) # ,params[:options])
          if flag && data.present?
            if quantity.present?
              data["targetItemStockLevel"] = quantity
            else
              raise StandardError.new "Invalid cache data"
            end
          end
        rescue => e
          message = e.message
        end
      end
      if message.present?
        {error: {message: :invalid_operation, details: message}}
      else
        {data: {result: data.nil? ? {} : data, status: !data.nil?}}
      end
    end

    field :get_single_product, GraphQL::Types::JSON, null: false,
      description: "Single  Shopify product" do
        argument :id, String, required: false
        argument :vid, String, required: false
      end
    def get_single_product(id: nil, vid: nil)
      if id.present? || vid.present?
        product = nil
        begin
          max = 0
          product = ShopifyAPI::LightGraphQL.get_product(current_store.shopify_domain, current_store.shopify_token, id.present? ? {id: id} : {variant_id: vid}).dig("data", "products", "nodes", 0)
          raise StandardError.new("Product empty") if product.blank?
          locations = ((ShopifyAPI::LightGraphQL.get_locations_valid(current_store.shopify_domain, current_store.shopify_token) || {}).dig("data", "locations", "nodes") || []).map { |o| [o["id"].split("/").last.to_i, o["name"]] }.to_h
          profiles = UserProfile.where(user_id: current_store.id, feed_type: "update").pluck("id, profile_name, low_stock_level").each do |row|
            if max < row.last
              max = row.last
            end
            row.pop
          end.to_h
        rescue => e
          return {error: {message: :invalid_operation, details: e.message}}
        end
        {data: {product: product, profiles: profiles, locations: locations, lowStockLevel: max}}
      else
        {error: {message: :invalid_api}}
      end
    end

    field :get_product_changes, GraphQL::Types::JSON, null: false,
      description: "Product changes" do
        argument :size, Integer, required: false
        argument :page, Integer, required: false
        argument :product_id, String, required: false
        argument :variant_id, String, required: false
        argument :sku, String, required: false
      end
    def get_product_changes(size: nil, page: nil, product_id: nil, variant_id: nil, sku: nil)
      if product_id.present? || variant_id.present? || sku.present?
        size = (size.present? && size > 0 && size <= 30) ? size.to_i : 30
        page = (page.present? && page > 1) ? (page.to_i - 1) * size : 0
        # variants = params[:variant_ids].present? ? params[:variant_ids] : nil
        variants = nil
        args = {user_id: current_store.id}

        if sku.present?
          args[:sku] = sku
        else
          args[:spid] = product_id if product_id.present?
          if variant_id.present?
            args[:svid] = variant_id
            variants = variant_id
          end
        end

        # args = {}

        # if product_id.present?
        #   args.merge!({spid: product_id, sku: nil})
        # elsif variant_id.present?
        #   args.merge!({svid: variant_id})
        #   variants = variant_id
        # end

        begin
          # profiles = params[:profiles].present? ? params[:profiles] : UserProfile.where(user_id: current_store.id, feed_type: 'update').pluck("json_build_object('id', id, 'profile_name', profile_name)")
          changes = ChangeLog.where(**args).limit(size).offset(page).order(created_at: :desc).pluck(Arel.sql("json_build_object('svid', svid, 'message', message, 'created_at', created_at, 'user_profile_id', user_profile_id)"))
          not_in_feeds = (variants.present? && changes.present?) ? NotInFeed.where(user_profile_id: changes.map { |json| json["user_profile_id"] }.uniq, shopify_variant_id: variants).pluck(Arel.sql("json_build_object('user_profile_id', user_profile_id, 'product_log_id', product_log_id, 'shopify_variant_id', shopify_variant_id, 'created_at', created_at, 'updated_at', updated_at)")) : []

          if not_in_feeds.present?
            temp = []
            not_in_feeds.each do |o|
              o["deleted_at"] = ProductLog.where(user_profile_id: o["user_profile_id"]).where("id > ?", o["product_log_id"]).limit(1).pluck("end_time").first

              o.except("user_profile_id", "shopify_variant_id", "product_log_id").map { |k, v|
                if v.present?
                  temp << {
                    "svid" => o["shopify_variant_id"].to_s,
                    "message" => {
                      "fields" => {
                        if k == "created_at"
                          "first_not_found"
                        else
                          (k == "updated_at") ? "last_not_found" : "restored"
                        end => {}
                      }
                    },
                    "created_at" => v,
                    "user_profile_id" => o["user_profile_id"]
                  }
                end
              }
            end
            temp.flatten!
            temp.compact!
            temp.uniq! { |a| a["created_at"] }
            changes += temp

            changes.sort_by! { |a| a["created_at"] }.reverse!
          end
          {data: changes}
        rescue => e
          {error: {message: :invalid_operation, details: e.message}}
        end
      else
        {error: {message: :invalid_api}}
      end
    end

    field :get_google_shopping_categories, GraphQL::Types::JSON, null: false,
      description: "Google shopping categories"
    def get_google_shopping_categories
      categories = GoogleShopping.get_categories
      if categories.present?
        {data: categories}
      else
        {error: {message: :failed_to_get_google_shopping_categories}}
      end
    end

    field :update_payment_info, GraphQL::Types::JSON, null: false,
      description: "Update Stripe payment info"
    def update_payment_info
      # ! Need to update back customer stripe id because some users customer stripe id become nil
      if current_store.stripe_subscription_id.present? && current_store.stripe_customer_id.nil?
        customer_id = Stripe::Subscription.retrieve({id: current_store.stripe_subscription_id})&.customer
        current_store.update(stripe_customer_id: customer_id) if customer_id.present?
      end
      billing_url = current_store.get_stripe_billing_portal_url
      if billing_url
        {data: billing_url}
      else
        {error: {message: :assistance_contact}}
      end
    end

    # ? vendors api method
    field :get_vendors, [GraphQL::Types::JSON], null: false
    def get_vendors
      Rails.cache.fetch("vendors_#{current_store.id}", expires_in: 24.hours) do
        ShopifyAPI::LightGraphQL.paginate(current_store.shopify_domain, current_store.shopify_token, ["productVendors"], :query_vendors)
      end
    end

    # ? collection api method
    field :get_collections, [GraphQL::Types::JSON], null: false
    def get_collections
      data = Rails.cache.fetch("collection_#{current_store.id}", expires_in: 24.hours) do
        data = ShopifyAPI::LightGraphQL.paginate(current_store.shopify_domain, current_store.shopify_token, ["collections"], :query_collections)
        data.each do |collection|
          collection[:id] = collection.delete("id").split("/").last
          collection[:title] = collection.delete("title")
        end
        data
      end
    end

    # ? metafield api
    field :get_metafield_definitions, GraphQL::Types::JSON, null: false,
      description: "Return metafield definitions based on owner type"
    def get_metafield_definitions
      return {} if current_store.provider != "shopify"
      Rails.cache.fetch("metafield_definitions_#{current_store.id}", expires_in: 30.minutes) do
        {
          "product" => ShopifyAPI::LightGraphQL.query_metafield_definitions(current_store.shopify_domain, current_store.shopify_token, owner_type: "PRODUCT").dig("data", "metafieldDefinitions", "nodes") || [],
          "variant" => ShopifyAPI::LightGraphQL.query_metafield_definitions(current_store.shopify_domain, current_store.shopify_token, owner_type: "PRODUCTVARIANT").dig("data", "metafieldDefinitions", "nodes") || []
        }
      end
    end

    field :get_matched_metafields, [GraphQL::Types::JSON], null: false do
      argument :public_token, String, required: false, description: "Shopify public token"
    end
    def get_matched_metafields(public_token: nil)
      return [] if current_store.provider != "shopify"
      target_store = User.find_by(public_token: public_token)

      current_store_metafields = current_store ? fetch_metafields(current_store) : []
      target_store_metafields = target_store ? fetch_metafields(target_store) : []

      # Find matched metafields
      current_store_metafields.select do |current_metafield|
        current_key = current_metafield["key"]
        target_store_metafields.any? { |target_metafield| target_metafield["key"] == current_key }
      end
    end

    # ? feed settings api
    field :get_all_feeds, [UserProfileType], null: false do
      argument :sort_by, String, required: false
      argument :sort_order, String, required: false
      argument :latest_product_log, String, required: false
    end
    def get_all_feeds(sort_by: nil, sort_order: nil, latest_product_log: nil)
      return GraphQL::ExecutionError.new("Unauthenticated") if current_store.blank?

      sorting = if sort_by.present? && sort_order.present?
        if sort_by != "id"
          "pinned desc, LOWER(#{sort_by}) #{sort_order}"
        else
          "pinned desc, #{sort_by} #{sort_order}"
        end
      else
        "pinned desc, created_at desc"
      end

      if is_impersonating_admin?
        current_store.user_profiles.where(fast_track: false).includes([:sync_fields])
          .includes([latest_product_log: :user_profile])
          .includes([template: :supplier])
          .with_deleted.order("deleted_at desc NULLS FIRST")
          .order(sorting)
      else
        current_store.user_profiles.includes([:sync_fields])
          .includes([latest_product_log: :user_profile])
          .includes([template: :supplier])
          .order(sorting)
      end
    end

    field :get_feed_by_id, UserProfileType, null: false,
      description: "Get Feed information" do
      argument :feed_id, Integer, required: true
    end
    def get_feed_by_id(feed_id:)
      current_store.user_profiles.with_deleted.find_by_id(feed_id)
    end

    field :first_snappy_feed, UserProfileType, null: false,
      description: "Get First Snappy Feed information"
    def first_snappy_feed
      current_store.user_profiles.where(fast_track: true).first
    end

    field :query_change_log, GraphQL::Types::JSON, null: false,
      description: "Query data from CSV (changelog)" do
      argument :feed_id, Integer, required: true
      argument :limit, Integer, required: false
    end

    def query_change_log(feed_id:, limit: nil)
      feed = current_store.user_profiles.find_by_id(feed_id)
      log = feed.latest_product_log if feed.present?
      file_path = log.s3_download_base(ProductUpdateStatus::FILE_UPDATED_INFO) if log.present?
      data = S3Select.paginate_from_logs(file_path, limit) if file_path.present?
      if file_path.present?
        total_products = Rails.cache.fetch("total_products_#{file_path}_#{feed_id}", expires_in: 1.hours) do
          S3Select.count_from_logs(file_path)
        end
      end
      {data: data, total_products: total_products}
    end

    field :query_by_keyword, GraphQL::Types::JSON, null: false,
      description: "Query data from CSV (changelog) with keyword" do
      argument :keyword, String, required: false
      argument :limit, Integer, required: false
      argument :feed_id, Integer, required: true
    end
    def query_by_keyword(feed_id:, keyword: nil, limit: nil)
      feed = current_store.user_profiles.find_by_id(feed_id)
      log = feed.latest_product_log if feed.present?
      file_path = log.s3_download_base(ProductUpdateStatus::FILE_UPDATED_INFO) if log.present?
      begin
        S3Select.search_from_logs(keyword, file_path, limit, "SKU,Title")
      rescue
        {}
      end
    end

    field :read_sample_data, [GraphQL::Types::JSON], null: false,
      description: "read sample data" do
      argument :feed_id, Integer, required: true
      argument :retries, Integer, required: false, default_value: 1
    end
    def read_sample_data(feed_id:, retries: nil)
      feed = current_store.user_profiles.find_by_id(feed_id)
      sample_data = feed.get_sample_data_from_cache if feed.present?
      sample_data ||= []
      if sample_data.blank? && retries <= 10 # try to query up to 10 times to prevent performance issue on UI
        feed_sample_job = ScanFeedSampleJob.new(feed_id, {file: false})
        Delayed::Job.where(queue: "feed_sample_jobs".freeze, user_profile_id: feed_id).delete_all
        Delayed::Job.enqueue(feed_sample_job, queue: "feed_sample_jobs".freeze, priority: 1)
      end

      if sample_data.present?
        keys = sample_data.first
        values = sample_data.last

        sample_data = keys.zip(values).map { |k, v|
          v = "-" if v.blank?
          {key: k["key"], value: v["key"]}
        }
      end

      sample_data
    end

    field :field_mapping_prediction, [GraphQL::Types::JSON], null: false,
      description: "field mapping prediction" do
      argument :feed_id, Integer, required: true
      argument :retries, Integer, required: false, default_value: 1
    end
    def field_mapping_prediction(feed_id:, retries: nil)
      feed = UserProfile.with_deleted.find_by_id(feed_id)
      template = FeedTemplate.find_by_id(feed&.template_id)
      supplier = Supplier.find_by_id(template&.supplier_id)

      return [] if feed.isXml? || (supplier && supplier.supplier_type == "supplier")
      mapping_prediction = RedisManager.get_field_mapping_prediction(feed_id) if feed.present?
      sample_data = feed.get_sample_data_from_cache if feed.present?
      mapping_prediction ||= []
      sample_data ||= []
      if mapping_prediction.blank? && sample_data.present? && retries <= 10
        predict_mapping = PredictMappingJob.new(feed_id, sample_data)
        job_count = Delayed::Job.where(queue: "ai_prediction_jobs", user_profile_id: feed_id).count
        if job_count == 0
          Delayed::Job.enqueue(predict_mapping, queue: "ai_prediction_jobs".freeze, signature: "predict_mapping_#{feed_id}", priority: 1)
        end
      end

      if mapping_prediction.present?
        begin
          result = JSON.parse(mapping_prediction)
          raise unless result["status"]
          mapping_prediction = result["remark"]
        rescue
          return []
        end
      end

      mapping_prediction
    end

    field :get_authorization_urls, GraphQL::Types::JSON, null: false,
      description: "get authorization urls" do
      argument :feed_id, Integer, required: true
    end
    def get_authorization_urls(feed_id:)
      find_feed(feed_id) do |feed|
        url_helpers = Rails.application.routes.url_helpers
        authorization_urls = {
          ebay: url_helpers.authorize_ebay_index_url(host: Settings.hostname, profile_id: feed.id),
          etsy: EtsyClient.create_authorization_url(feed.id),
          one_drive: OneDriveFileReader.callback_url(feed.id),
          quickbooks: url_helpers.authorize_quickbooks_url(host: Settings.hostname, profile_id: feed.id),
          vend: url_helpers.authorize_vend_index_url(host: Settings.hostname, profile_id: feed.id),
          aliexpress: url_helpers.authorize_aliexpress_index_url(host: Settings.hostname, profile_id: feed.id),
          xero: url_helpers.authorize_xero_index_url(host: Settings.hostname, profile_id: feed.id),
          box: url_helpers.authorize_box_index_url(host: Settings.hostname, profile_id: feed.id),
          google: url_helpers.authorize_google_index_url(host: Settings.hostname, profile_id: feed.id),
          woocommerce: url_helpers.authorize_woocommerce_index_url(host: Settings.hostname, profile_id: feed.id),
          zoho_inventory: url_helpers.authorize_zoho_index_url(host: Settings.hostname, profile_id: feed.id),
          bling: url_helpers.authorize_bling_index_url(host: Settings.hostname, profile_id: feed.id),
          ebay_v2: url_helpers.authorize_ebay_v2_index_url(host: Settings.hostname, profile_id: feed.id),
          airtable: url_helpers.authorize_airtable_index_url(host: Settings.hostname, profile_id: feed.id),
          onshopfront: url_helpers.authorize_onshopfront_index_url(host: Settings.hostname, profile_id: feed.id),
          tiktokshop: url_helpers.authorize_tiktokshop_index_url(host: Settings.hostname, profile_id: feed.id)
        }

        disconnect_urls = {
          quickbooks: url_helpers.disconnect_quickbooks_url(host: Settings.hostname, profile_id: feed.id),
          one_drive: url_helpers.disconnect_microsoft_index_url(host: Settings.hostname, profile_id: feed.id),
          box: url_helpers.disconnect_box_index_url(host: Settings.hostname, profile_id: feed.id),
          vend: url_helpers.disconnect_vend_index_url(host: Settings.hostname, profile_id: feed.id),
          aliexpress: url_helpers.disconnect_aliexpress_index_url(host: Settings.hostname, profile_id: feed.id),
          xero: url_helpers.disconnect_xero_index_url(host: Settings.hostname, profile_id: feed.id),
          ebay: url_helpers.disconnect_ebay_index_url(host: Settings.hostname, profile_id: feed.id),
          etsy: url_helpers.disconnect_etsy_index_url(host: Settings.hostname, profile_id: feed.id),
          google: url_helpers.disconnect_google_index_url(host: Settings.hostname, profile_id: feed.id),
          woocommerce: url_helpers.disconnect_woocommerce_index_url(host: Settings.hostname, profile_id: feed.id),
          zoho_inventory: url_helpers.disconnect_zoho_index_url(host: Settings.hostname, profile_id: feed.id),
          bling: url_helpers.disconnect_bling_index_url(host: Settings.hostname, profile_id: feed.id),
          ebay_v2: url_helpers.disconnect_ebay_v2_index_url(host: Settings.hostname, profile_id: feed.id),
          airtable: url_helpers.disconnect_airtable_index_url(host: Settings.hostname, profile_id: feed.id),
          onshopfront: url_helpers.disconnect_onshopfront_index_url(host: Settings.hostname, profile_id: feed.id),
          tiktokshop: url_helpers.disconnect_tiktokshop_index_url(host: Settings.hostname, profile_id: feed.id)
        }

        return {
          authorization_urls: authorization_urls,
          disconnect_urls: disconnect_urls
        }
      end
    end

    field :get_activity_logs, LogQueryResultType, null: false,
      description: "get activity logs" do
      argument :feed_id, Integer, required: true
      argument :page, Integer, required: true
      argument :status, Boolean, required: false
    end
    def get_activity_logs(feed_id:, status:, page:)
      has_more = true
      size = 10 # set pagination size on backend. Change here if needed
      feed = current_store.user_profiles.with_deleted.find(feed_id)

      # dont show latest log when feed is processing because that log will have status = false
      omit_latest_log = ["processing", "queuing"].include?(feed.status) && page == 1

      page = (page - 1) * 10
      # size = size < 5 || size > 30 ? 10 : size
      # page = page < 2 ? 0 : size*(page-1)
      # logs = feed.product_logs.order(created_at: :desc).limit(size).offset(page)
      logs = if status.to_s.present?
        feed.product_logs.where(status: status).order(created_at: :desc).limit(size * 2).offset(page)
        # feed.product_logs.includes([:user_profile]).where(status: status).order(created_at: :desc).limit(size * 2).offset(page)
      else
        feed.product_logs.order(created_at: :desc).limit(size).offset(page)
        # feed.product_logs.includes([:user_profile]).order(created_at: :desc).limit(size).offset(page)
      end
      has_more = false if logs.length < size

      logs = logs.to_a
      if omit_latest_log && logs.any?
        logs.shift if logs.first.end_time.nil?
      end

      {logs: logs, has_more: has_more}
    end

    field :validate_pricing_conditions, GraphQL::Types::JSON, null: false,
      description: "validate pricing conditions" do
      argument :feed_id, Integer, required: true
      argument :pricing_condition, GraphQL::Types::JSON, required: true
    end

    def validate_pricing_conditions(feed_id:, pricing_condition:)
      find_feed(feed_id) do |feed|
        pricing_condition.permit!
        pricing_condition_param = pricing_condition.to_h
        pricing_condition = pricing_condition_param.symbolize_keys
        condition = pricing_condition[:condition]
        formula = pricing_condition[:formula]
        pricing_condition = PricingCondition.new(condition, formula)

        if pricing_condition.valid?
          begin
            syntax = DentakuPricingTransformer.transform([pricing_condition_param])
            # populate dummy
            calculator = Dentaku::Calculator.new
            params = {price: 12, current_price: 12, price_f: 12, current_price_f: 12, cost: 12, compare_at_price: 12, compare_at_price_f: 12, shipping_cost: 12, taxable: 1, current_quantity: 12, vendor: "vendor", now: Time.now, today: Date.today}
            (1..120).collect { |a|
              params["col#{a}"] = "1"
              params["col#{a}_f"] = 1
              params["col#{a}_date"] = Time.now
            }
            calculator.add_function(:col, :numeric, ->(col_name) { 10.0 })
            calculator.add_function(:col_string, :string, ->(col_name) { "Test" })
            result = calculator.evaluate(syntax, params)
            if result.blank?
              return {success: false, error: :invalid_condition}
            else
              return {success: true}
            end
          rescue
            return {success: false, error: :invalid_condition}
          end
        else
          return {success: false, error: pricing_condition.errors.messages[:base].join(",")}
        end
      end
    end

    # ? filters api
    field :get_bigcommerce_brands, GraphQL::Types::JSON, null: false,
      description: "Get all bigcommerce brands"
    def get_bigcommerce_brands
      data = Rails.cache.fetch("brands_#{current_store.id}", expires_in: 2.hours) do
        brands = BigcommerceClient.new(current_store).get_all_brands
        data = brands.map { |brand| {key: brand["id"], value: brand["name"]} }
        data.sort! { |x, y| x[:value].upcase <=> y[:value].upcase }
      end
    end

    field :get_woocommerce_categories, GraphQL::Types::JSON, null: false,
      description: "Get all woocommerce categories"
    def get_woocommerce_categories
      data = Rails.cache.fetch("woocommerce_categories_#{current_store.id}", expires_in: 2.hours) do
        categories = WoocommerceClient.new(current_store).get_all_categories
        data = categories.map { |cat| {key: cat["id"], value: cat["name"]} }
        data.sort! { |x, y| x[:value].upcase <=> y[:value].upcase }
      end
    end

    field :get_woocommerce_tags, GraphQL::Types::JSON, null: false,
      description: "Get all woocommerce tags"
    def get_woocommerce_tags
      data = Rails.cache.fetch("woocommerce_tags_#{current_store.id}", expires_in: 2.hours) do
        tags = WoocommerceClient.new(current_store).get_all_tags
        data = tags.map { |cat| {key: cat["id"], value: cat["name"]} }
        data.sort! { |x, y| x[:value].upcase <=> y[:value].upcase }
      end
    end

    # ? get pricing plans api method
    field :get_pricing_plans, PricingPlansType, null: false
    def get_pricing_plans
      # TODO: cache this
      plans = Plan.non_customized.where(version: current_store.plan_version).published.order("price asc")
      {
        # generate mock id to conform to query selection spec
        id: [].to_s,
        packages: plans,
        add_on: Settings.import_packages.to_h.values.map(&:to_h),
        flexi: Settings.flexi_package.to_h
      }
    end

    # ? get supplier api method
    field :get_suppliers, [SupplierType], null: false do
      argument :feed_type, String, required: true
      argument :connection_level, String, required: false
    end
    def get_suppliers(feed_type:, connection_level: nil)
      io_mode = "in"
      provider = current_store.provider.downcase
      provider_support = {"on_#{provider}" => true}.to_json

      if feed_type === "export"
        io_mode = "out"
        feed_type = "update"
      end

      Rails.cache.fetch("#{provider}_#{feed_type}_#{io_mode}_#{connection_level}_suppliers_rails_8_0_0", expires_in: 1.week) do
        query = Supplier.publish_templates
          .joins(:feed_templates)
          .group("suppliers.id")
          .where("user_profiles.feed_type = ? and user_profiles.io_mode = ? and user_profiles.supported_providers @> ?", feed_type, io_mode, provider_support)
          .order("popular_count desc")
        connection_level.present? ? query.by_connection_type(connection_level).uniq { |s| s.name } : query
      end
    end

    # Admin-use only
    field :pre_load_store, String, null: false do
      argument :feed_id, Integer, required: true
    end
    def pre_load_store(feed_id:)
      profile = UserProfile.with_deleted.find_by_id(feed_id)
      user = profile.user
      client = user.active_session
      count = 0

      if user.provider == "shopify"
        count = ShopifyAPI::LightGraphQL.get_products_count(user.shopify_domain, user.shopify_token, query: ShopifyAPI::LightGraphQL.graphql_product_filters(profile).first).dig("data", "productsCount", "count")
        count = ">=10000" if count.to_i == 10_000
      else
        begin
          count = client.get_products_count
        rescue
          count = 0
        end
      end
      count.to_s
    end

    field :get_product_counts, GraphQL::Types::JSON, null: false do
      argument :feed_id, Integer, required: true
      argument :store_filters, GraphQL::Types::JSON, required: false
    end
    def get_product_counts(feed_id:, store_filters: nil)
      find_feed(feed_id) do |feed|
        user = feed.user

        if user.provider == "shopify"
          feed.store_filters = store_filters if store_filters.present?

          filtered_query = ShopifyAPI::LightGraphQL.graphql_product_filters(feed).first
          response = ShopifyAPI::LightGraphQL.query(
            {"query" => ShopifyAPI::LightGraphQL.clean_query(<<~GRAPHQL
              query {
                total: productsCount {
                  count
                  precision
                }
                filtered: productsCount#{filtered_query.present? ? "(query:\"#{filtered_query}\")" : ""} {
                  count
                  precision
                }
              }
            GRAPHQL
                                                            )},
            user.shopify_domain,
            user.shopify_token
          )

          total = response.dig("data", "total", "count")
          filtered = response.dig("data", "filtered", "count")
          precision = response.dig("data", "filtered", "precision")

          # Shopify's API has a maximum count limit of 10,000 if filtered
          {
            total: total.to_s,
            filtered: filtered.to_s,
            precision: precision&.downcase
          }
        else
          # For other platforms:
          # - Some platforms don't support filtered search
          # - Some platforms don't have product count API
          # - Return same count for both total and filtered to maintain consistent response
          begin
            client = user.active_session
            count = client.get_products_count.to_s
          rescue
            count = "0"
          end

          {total: count, filtered: count}
        end
      end
    end

    # ? Retrieve Wix collections
    field :get_collections_from_wix, [GraphQL::Types::JSON], null: false
    def get_collections_from_wix
      data = Rails.cache.fetch("collection_#{current_store.id}", expires_in: 24.hours) do
        collections = WixClient.new(current_store).get_collections.compact
        data = collections.map { |cat| {key: cat["id"], value: cat["name"]} } if collections.present?
        data.sort! { |x, y| x[:value].upcase <=> y[:value].upcase } if data.present?
      end
    end

    field :get_supplier_categories, [SupplierCategoryType], null: false
    def get_supplier_categories
      Settings.supplier_categories.map { |k, v| {code: k, name: v} }
    end

    field :get_countries, [CountryType], null: false
    def get_countries
      Settings.countries.map { |k, v| {code: k, name: v} }
    end

    field :get_supported_translations, [GraphQL::Types::JSON], null: false
    def get_supported_translations
      LanguageTranslator.list_supported_languages_v2
    end

    field :query_stores_by_public_token, [GraphQL::Types::JSON], null: false
    def query_stores_by_public_token
      public_token = current_store.public_token
      user_ids = UserProfile.where(source_url: public_token).pluck(:user_id).uniq
      data = User.where(id: user_ids).group_by(&:provider)
      data.map do |provider, users|
        shops = []
        users.each do |user|
          shops << user
        end
        {
          provider: provider,
          shops: shops
        }
      end
    end

    field :unresolved_conversations_count, Integer, null: false
    def unresolved_conversations_count
      Rails.cache.fetch("unresolved_conversations_count", expires_in: 10.minutes) do
        CrispClient.new.count_unresolved_conversations
      end
    end

    field :get_sales_channels, [GraphQL::Types::JSON], null: false,
      description: "List of Shopify sales channels for the current store"
    def get_sales_channels
      return [] unless current_store.provider == "shopify"

      Rails.cache.fetch("shopify:sales_channels:#{current_store.id}", expires_in: 30.minutes) do
        response = ShopifyAPI::LightGraphQL.get_publications(current_store.shopify_domain, current_store.shopify_token)
        response.dig("data", "publications", "edges")&.map do |edge|
          node = edge["node"]
          node["id"] = node["id"].split("/").last
          node
        end || []
      end
    end

    field :get_categories, [GraphQL::Types::JSON], null: false
    def get_categories
      Rails.cache.fetch("shopify_taxonomy_categories", expires_in: 1.month) do
        response = Faraday.get("https://raw.githubusercontent.com/Shopify/product-taxonomy/refs/heads/main/dist/en/taxonomy.json")
        return [] unless response.success?

        JSON.parse(response.body)["verticals"].flat_map { |vertical|
          vertical["categories"]
        }.map do |category|
          {
            id: category["id"].split("/").last,
            name: category["full_name"]
          }
        end
      end
    rescue Faraday::Error
      []
    end

    private

    def fetch_metafields(store)
      # ShopifyAPI::LightGraphQL.paginate(store.shopify_domain, store.shopify_token, ["metafieldDefinitions"], :query_metafield_definitions, **{})
      (ShopifyAPI::LightGraphQL.query_metafield_definitions(store.shopify_domain, store.shopify_token, owner_type: "PRODUCT").dig("data", "metafieldDefinitions", "nodes") || []) +
        (ShopifyAPI::LightGraphQL.query_metafield_definitions(store.shopify_domain, store.shopify_token, owner_type: "PRODUCTVARIANT").dig("data", "metafieldDefinitions", "nodes") || [])
    end
  end
end
