module Types
  class UserProfileType < BaseObject
    description "User Profile"

    field :id, ID, null: false
    field :fast_track_download_link, String, null: true
    field :profile_name, String, null: false
    field :feed_type, String, null: false
    field :source_type, String, null: false
    field :delete_mode, Integer, null: true
    field :partial_match, Boolean, null: false
    field :combined_source_type, String, null: true
    field :profile_status, String, null: false
    field :io_mode, String, null: false
    field :file_format, String, null: false
    field :job_type, String, null: false
    field :job_time, String, null: true
    field :job_interval, String, null: false
    field :use_time_range, Boolean, null: false
    field :hourly_start_time, String, null: false
    field :hourly_end_time, String, null: false
    field :formatted_job_type, String, null: true
    field :formatted_job_timezone, String, null: true
    field :frequency_time, String, null: true
    field :scheduler_enabled, Boolean, null: false
    field :status, String, null: false
    field :orig_status, String, null: false
    field :processing_no, Integer, null: true
    field :email, String, null: true
    field :checksum_check, Boolean, null: false
    field :export_email, String, null: true
    field :shopify_product_key, String, null: false
    field :product_identifier, String, null: true
    field :vendor_filter, String, null: true
    field :hide_unmatch_products, Boolean, null: false
    field :is_auto_generate_metafield_definitions, Boolean, null: false
    field :ignore_dont_track_inventory, Boolean, null: false
    field :ignore_zero_quantity, Boolean, null: false
    field :update_duplicate_product_key, Boolean, null: false
    field :published_apply_to_all, Boolean, null: false
    field :skip_import_with_zero_qty, Boolean, null: false
    field :variant_image_link, Boolean, null: false
    field :assign_variants_to_first_image, Boolean, null: false
    field :unpublished_apply_to_all, Boolean, null: false
    field :published_apply_matching_products, Boolean, null: false
    field :unpublished_apply_matching_products, Boolean, null: false
    field :shopify_track_inventory, Boolean, null: false
    field :location_id, String, null: true
    field :location_name, String, null: true
    field :auto_reset_quantity, Boolean, null: false
    field :auto_tagging, Boolean, null: false
    field :import_sort, Boolean, null: false
    field :activity_logs_url, String, null: true
    field :not_in_feed_url, String, null: true
    field :created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: true
    field :low_stock_level, Integer, null: false
    field :has_header, Boolean, null: false
    field :parent_node, String, null: true
    field :variant_node, String, null: true
    field :skip_total_rows, Integer, null: true
    field :auto_file_settings, Boolean, null: true
    field :col_sep, String, null: false
    field :sheet_name, String, null: false
    field :source_url, String, null: true
    field :source_file_file_name, String, null: true
    field :path_to_file, String, null: true
    field :namespace_identifier, String, null: true
    field :acc_name, String, null: true
    field :ssh_key, String, null: true
    field :product_key_separator, String, null: false
    field :source_auth, Boolean, null: true
    field :oauth_granted, Boolean, null: false
    field :oauth_granted_to, String, null: true
    field :ftp_rename, Integer, null: true
    field :custom_file_name, String, null: true
    field :fast_track, Boolean, null: false
    field :call_params, GraphQL::Types::JSON, null: true
    field :header_params, GraphQL::Types::JSON, null: true
    field :body_raw, String, null: true
    field :exclude_vendors, String, null: true
    field :include_skus, String, null: true
    field :exclude_skus, String, null: true
    field :shopify_inventory_management, String, null: true
    field :progress, String, null: true
    field :product_type_filter, String, null: true
    field :product_title_filter, String, null: true
    field :exclude_product_titles, String, null: true
    field :collection_filter_title, String, null: true
    field :collection_filter_id, String, null: true
    field :exclude_collections, String, null: true
    field :s3_secret_access_key, String, null: true
    field :s3_access_key_id, String, null: true
    field :s3_bucket_name, String, null: true
    field :custom_login_field, String, null: true
    field :custom_login_password_field, String, null: true
    field :clear_google_sheet, Boolean, null: false
    field :google_out_insert, Boolean, null: false
    field :pinned, Boolean, null: false
    field :http_method, String, null: false
    field :published_filter, String, null: false
    field :zero_qty, Boolean, null: true
    field :exclude_product_types, String, null: true
    field :exclude_tags, String, null: true
    field :include_tags, String, null: false
    field :force_override_compare_at_price, Boolean, null: false
    field :import_tags, String, null: false
    field :filter_params, String, null: true
    field :file_encoding, String, null: true
    field :has_filter, Boolean, null: true
    field :ftp_whitelist, Boolean, null: false
    field :google_shopping_category, String, null: true
    field :bigcommerce_retain_availability, Boolean, null: false
    field :woocommerce_api_version, String, null: false
    field :woocommerce_consumer_key, String, null: true
    field :woocommerce_consumer_secret, String, null: true
    field :detected_file_format, String, null: true
    field :auth_type, String, null: true
    field :remove_product_when_all_locations_nil, Boolean, null: false
    field :feed_file_location, String, null: true
    field :webhook_mode, Boolean, null: true
    field :bypass_blank_row, Boolean, null: false
    field :enabled, Boolean, null: true
    field :from_template, Boolean, null: true
    field :activity_logs, [ProductLogType], null: true,
      description: "activity logs field" do
      argument :size, Integer, required: false, default_value: 30
    end
    field :latest_activity_log, ProductLogType, null: true
    field :update_product_level, Boolean, null: false
    field :sync_field_settings, [GraphQL::Types::JSON], null: true
    field :prefix, String, null: true
    field :store_prefix, String, null: true
    field :postfix, String, null: true
    field :case_sensitive, Boolean, null: false
    field :api_sleep_sec_per_page, Integer, null: false
    field :include_bpn, String, null: true
    field :exclude_bpn, String, null: true
    field :store_filters, [GraphQL::Types::JSON], null: false
    field :feed_filters, [GraphQL::Types::JSON], null: false
    field :last_email_created_at, GraphQL::Types::ISO8601DateTime, null: true
    field :humanize_feed_type, String, null: false
    field :metafields, [GraphQL::Types::JSON], null: true
    field :metafields_count, Integer, null: true
    field :supplier, SupplierType, null: true
    field :remove_profile_id, Integer, null: true
    field :product_identifier_sync_field, GraphQL::Types::JSON, null: true
    field :product_options_sync_field, [GraphQL::Types::JSON], null: true
    field :is_deleted, Boolean, null: false
    field :received_email, String, null: true
    field :decrypted_password, String, null: true
    field :connection_settings, GraphQL::Types::JSON, null: false
    field :deleted_at, GraphQL::Types::ISO8601DateTime, null: true
    field :bulk_loading, Boolean, null: true
    field :public_file, Boolean, null: false
    field :preview, Boolean, null: true
    field :is_qty_rule_locked, Boolean, null: false
    field :is_querying_products, Boolean, null: false
    field :is_canceling, Boolean, null: false

    field :aliexpress_app_id, String, null: true
    field :aliexpress_app_secret, String, null: true
    field :aliexpress_access_token, String, null: true

    field :box_access_token, String, null: true
    field :box_refresh_token, String, null: true

    field :ebay_auth_token, String, null: true
    field :ebay_session_id, String, null: true

    field :etsy_token, String, null: true
    field :etsy_secret, String, null: true

    field :walmart_client_id, String, null: true
    field :walmart_client_secret, String, null: true
    field :walmart_access_token, String, null: true

    field :one_drive_token, String, null: true
    field :one_drive_refresh_token, String, null: true
    field :one_drive_share_url, String, null: true

    field :quickbooks_access_token, String, null: true
    field :quickbooks_refresh_token, String, null: true
    field :quickbooks_realm_id, String, null: true

    field :unleashed_api_id, String, null: true
    field :unleashed_api_key, String, null: true

    field :vend_access_token, String, null: true
    field :vend_refresh_token, String, null: true
    field :vend_domain_prefix, String, null: true

    field :xero_client_id, String, null: true
    field :xero_client_secret, String, null: true
    field :xero_access_token, String, null: true
    field :xero_refresh_token, String, null: true
    field :xero_tenant_id, String, null: true
    field :file_name, String, null: true
    field :auto_clear_not_in_feed_created, Boolean, null: false
    field :run_webhook_mode, Boolean, null: true
    field :woocommerce_product_attributes, [GraphQL::Types::JSON], null: true
    field :sales_channel_ids, [String], null: true
    field :extra_options, GraphQL::Types::JSON, null: false

    def is_deleted
      object.deleted_at.present?
    end

    def last_email_created_at
      last_email_date = (object.source_type == "email") ? object.email_logs.order("created_at desc").limit(1).pluck("created_at").first : nil
      last_email_date = ActiveSupport::TimeZone.new(object.timezone).parse(last_email_date.to_s) if last_email_date.present?
      last_email_date
    end

    def fast_track_download_link
      shop = object.user.shopify_domain
      token = object.user.public_token

      File.join(Settings.hostname, "home", "download_fast_track_products.csv?shop=#{shop}&token=#{token}")
    end

    def status
      object.status
    end

    def call_params
      YAML.safe_load(object.call_params&.gsub("=>", ":").to_s)
    end

    def header_params
      YAML.safe_load(object.header_params&.gsub("=>", ":").to_s)
    end

    def filter_params
      YAML.safe_load(object.filter_params&.gsub("=>", ":").to_s)
    end

    def activity_logs_url
      profile_id = object.id
      File.join(Settings.hostname, "user_profiles", "download_product_logs.csv?profile_id=#{profile_id}")
    end

    def not_in_feed_url
      profile_id = object.id
      File.join(Settings.hostname, "user_profiles", "download_not_in_feed.csv?profile_id=#{profile_id}")
    end

    def combined_source_type
      case object.source_type
      when "google_spreadsheet", "zoho_inventory", "email",
        "etsy", "ftp", "ebay",
        "google_shopping", "stileo", "ftp_houzz",
        "walmart", "wayfair", "amazon",
        "aliexpress", "walmart_api",
        "mysale_api", "sftp", "ebay_v2"
        [object.source_type, object.io_mode].join("_")
      else
        object.source_type
      end
    end

    def frequency_time
      if ["email", "email_link"].include?(object.source_type) && object.job_type == "on_email_received"
        :on_email_received
      elsif object.source_type == "uploaded_file"
        :on_file_uploaded
      elsif object.job_type == "periodically"
        :periodical_job
      elsif object.job_type == "minute_periodically"
        :minute_periodical_job
      elsif object.job_time.present? && ActiveSupport::TimeZone.new(object.timezone).parse(object.job_time)
        :job_type_at_timezone
      else
        :no_job_time_available
      end
    rescue ArgumentError
      :time_out_of_range
    end

    def formatted_job_timezone
      ActiveSupport::TimeZone.new(object.timezone.to_s).parse(object.job_time.to_s).strftime("%l:%M%p")
    rescue
      ""
    end

    def formatted_job_type
      Settings.job_type[object.job_type]
    end

    def has_filter
      object.has_filter? || object.filter_params != "{}" || object.store_filters_exist? || object.feed_filters != []
    end

    # Ensure store filters is up to date for all stores
    def store_filters
      current_store_filters = object.store_filters.dup
      store_filters = UserProfile.process_new_store_filters(object)
      if store_filters != current_store_filters
        object.update_column(:store_filters, store_filters)
      end
      store_filters
    end

    def feed_filters
      if object.feed_filters.blank? && object.filter_params != "{}" && object.filter_params.present?
        filters = UserProfile.process_new_feed_filters(object.filter_params)
        object.update_column(:feed_filters, filters)
      end
      object.feed_filters
    end

    def enabled
      object.user.enabled_profiles.present? ? object.user.enabled_profiles.include?(object.id) : false
    end

    def from_template
      object.template_id.present?
    end

    # Return 10 logs by default. The rest will be paginated on demand
    def activity_logs(size:)
      # dont show latest log when feed is processing because that log will have status = false
      omit_latest_log = object.status === "processing"
      logs = object.product_logs.order(created_at: :desc).limit(size).to_a
      logs.shift if omit_latest_log
      logs
    end

    def sync_fields
      object.sync_fields.sort
    end

    def sync_field_settings
      # ? list sync fields except for product_id
      @provider_sync_fields ||= "SyncFields::#{object.provider.titleize}".constantize.all_attributes
      sf = sync_fields.select { |item|
        extracted_field_name = item.field_name.sub(/_\d+\z(?!.*_\d+)/, "").to_sym
        @provider_sync_fields[extracted_field_name] && item.field_name != "product_id"
      }
      sf.each do |sync_field|
        if sync_field["extra_attributes"] && sync_field["extra_attributes"]["rules_json"]
          rules_json = sync_field["extra_attributes"]["rules_json"]
          if !rules_json.is_a?(Array)
            rules_json = rules_json.gsub('"=>"', '":"')
            if rules_json
              sync_field["extra_attributes"]["rules_json"] = RulesSeparator.perform(rules_json)
            end
          end
        end
        if (extra_attributes_default_value = SyncField.extra_attributes_with_defaut_values[sync_field["field_name"].to_sym])
          sync_field["extra_attributes"] = extra_attributes_default_value.stringify_keys.merge!(sync_field["extra_attributes"] || {})
        end
      end
    end

    def metafields
      if object.user.provider == "shopify"
        # ? there are some others metafields field name which is not like "metafield_1", "metafield_2", "metafield_3"
        sync_fields.select { |field| field.field_name =~ /metafield_\d+/i } if sync_fields.present?
      elsif object.user.provider == "bigcommerce"
        sync_fields.select { |field| field.field_name.start_with?("custom_field_") } if sync_fields.present?
      elsif object.user.provider == "woocommerce"
        sync_fields.select { |field| field.field_name.start_with?("metadata_") } if sync_fields.present?
      elsif object.user.provider == "wix"
        sync_fields.select { |field| field.field_name.start_with?("additional_info_section_") } if sync_fields.present?
      else
        []
      end
    end

    def woocommerce_product_attributes
      return [] unless object.user.provider == "woocommerce"
      sync_fields.select { |field| field.field_name.start_with?("product_attribute_") } if sync_fields.present?
    end

    def product_identifier_sync_field
      sf = sync_fields.select { |field| field.field_name == "product_id" } if sync_fields.present?
      product_identifier = sf.first if sf.present?
      # ? only push if source key is exist
      if product_identifier.present? && (object.source_key_1.present? || object.source_key_2.present? || object.source_key_3.present?)
        product_identifier.field_mapping = product_identifier.field_mapping << "++" << object.source_key_1 if object.source_key_1.present?
        product_identifier.field_mapping = product_identifier.field_mapping << "++" << object.source_key_2 if object.source_key_2.present?
        product_identifier.field_mapping = product_identifier.field_mapping << "++" << object.source_key_3 if object.source_key_3.present?
      end
      if product_identifier.nil?
        product_identifier = SyncField.new(user_profile_id: object.id, field_name: "product_id", field_mapping: "", extra_attributes: {})
      end
      product_identifier
    end

    def product_options_sync_field
      sync_fields.select { |field| field.field_name.match?(/^option\d+$/) || field.field_name == "variant_group" }
    end

    def shopify_product_key
      key = object.shopify_product_key

      if object.variant_key_1.present?
        key = key + "++" + object.variant_key_1
        if object.variant_key_2.present?
          key = key + "++" + object.variant_key_2
          if object.variant_key_3.present?
            key = key + "++" + object.variant_key_3
          end
        end
      end

      key
    end

    def latest_activity_log
      object.latest_product_log
    end

    def bulk_loading
      object.queuing? && (object.fargate_task_id =~ /^bulk::.*/).present?
    end

    def user
      object.user
    end

    def scheduler_enabled
      return false if user&.package&.downcase == "free"
      object.scheduler_enabled
    end

    def extra_options
      return {} if object.extra_options.nil?
      object.extra_options
    end

    def run_webhook_mode
      object.run_webhook_mode?
    end

    def is_qty_rule_locked
      object.extra_options&.dig("is_qty_rule_locked") || false
    end

    def is_querying_products
      object.status == "queuing" && object.fargate_task_id.to_s.start_with?("bulk::gid")
    end

    def is_canceling
      object.fargate_task_id.to_s.include?("CANCELING::")
    end
  end
end
