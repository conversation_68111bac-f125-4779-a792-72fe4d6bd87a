---
en:
  New: New
  Old: Old
  next: Next
  back: Back
  back_to_dashboard: Home
  done: Done
  "yes": "Yes"
  "no": "No"
  update: Update
  import: Add
  OK: OK
  OR: OR
  refreshed: refreshed
  Products: Products
  Preferences: Preferences
  how_to: How To
  demo: Demo
  step: Step
  notifications: Notification
  advance: Manage account
  billing: Billing
  more_apps: More apps
  more_info: More info
  FAQ: FAQ
  Visit FAQ page: Visit FAQ page
  Visit our guide page: Visit our guide page
  Guide: Guide
  Video: Video
  tutorial: Tutorial
  See our video tutorial: See our video tutorial
  Back to my Shopify store: Back to my %{provider} store
  Warning!: Warning!
  Updates: Updates
  Imports: Imports
  please_proceed: Please click 'Start Process' again to enforce update
  download_activity_logs: Download activity logs for the last 10 days
  download_not_in_feed: Download latest not in feed
  copy: Copy
  copy_to_clipboard: Copy to clipboard
  copied_to_clipboard: Copied to clipboard!
  left: left
  per_month: month
  reached_limit:
    Can't create new feeds, new feed limit reached. Modify or delete
    your existing feeds to continue. Upgrade to create more %{feed_type} feeds
  reached_limit_import:
    You have reached the feed limit. Modify or delete existing
    feeds to continue. To create more %{feed_type} products feed, please contact our
    support team %{supportEmail}
  upgrade your current plan here.: upgrade your current plan here.
  Collection: Collection
  required: Required
  Quantity: Quantity
  Barcode: Barcode
  Description: Description
  price: Price
  compare_price_at: Compare-at price
  cost: Cost
  body_html: Description
  select_store_field: Select store field
  enter_node_name: Enter node name
  enter_column_name_or_column_index: Enter column name or column index
  enter_column_range: Enter column range
  Group multiple variants as a single product:
    Group multiple variants as a single
    product
  "<0>pricing_conditions <2></2></0>":
    "<0>Pricing Conditions (Markdown / Markup)
    <2></2></0>"
  invalid_condition: Invalid condition
  Price formula can't be blank: Price formula can't be blank
  Price range can't be blank: Price range can't be blank
  formula_cant_be_blank: formula can't be blank
  URL prefix: URL prefix
  Add URL prefix(i.e. domain of the image) to every image links.:
    Add URL prefix(i.e.
    domain of the image) to every image links.
  "##.99": "##.99"
  "##.95": "##.95"
  "##.#99": "##.#99"
  "##9.00": "##9.00"
  "##5.00": "##5.00"
  "##9.99": "##9.99"
  "##,000.00": "##,000.00"
  "##,#00.00": "##,#00.00"
  "##,##0.00": "##,##0.00"
  "##,#99.00": "##,#99.00"
  "##9 (from #.#9 to ##9,000). eg. 129000, 12900, 1290, 129, 12.90, 1.29":
    "##9 (from
    #.#9 to ##9,000). eg. 129000, 12900, 1290, 129, 12.90, 1.29"
  add_rule: Add Rule
  with_formula: With formula
  empty: Empty
  match_any: Match Any
  rule_key: Rule Key
  rule_operator: Rule Operator
  rule_value: Rule Value
  less_than: "< Less than"
  greator_than: "> Greater than"
  equal: "= Equal"
  less_than_or_equal: "<= Less than or equal"
  greator_than_or_equal: ">= Greater than or equal"
  start_with: Start with
  end_with: End with
  range: Range
  e.g. Rule = HIGH -> 50, "HIGH" in feed quantity value will be converted to 50 as in store quantity.:
    e.g.
    Rule = HIGH -> 50, "HIGH" in feed quantity value will be converted to 50 as in
    store quantity.
  Select a field to uniquely identify store products:
    Select a field to uniquely identify
    store products
  click_for_more_settings: Click for more settings
  field_deprecated: Field deprecated
  Store Prefix: Store Prefix
  Optional store prefix: Optional store prefix
  Store Prefix matches products with a prefix:
    Store Prefix matches products with
    a prefix
  feed_file_is_empty:
    Feed not found. Check the URL or feed availability. For templates
    without URL, please contact support.
  same_checksum:
    No update. Feed value is same as previous run or previous value.
    Please check with supplier if new feed file is added.
  reach_shopify_limits:
    Only %{products_imported} added. Shopify daily limits reached.
    Please try after 24 hours.
  exceed_update_limit:
    The store have %{product_skus_total} variants which exceeded
    the limit of %{limit_skus}.
  exceed_import_limit: Low credit. Please buy more or enable only process 3 products
  exceed_import_limit_with_count:
    Insufficient credits, only %{products_imported}
    products added.
  exceed_import_with_count_and_remaining:
    "%{products_imported} products added successfully.
    Lack of credit to add remaining %{remaining} rows."
  exceed_remove_limit_with_count:
    Insufficient credits, only %{variants_deleted} variants
    removed. %{products_deleted} products being deleted
  import_failed_count:
    "%{number_product_updated} products added and %{fail_count}
    failed. Download log to check."
  please_check_file_name:
    Please check file name and permission. File not exist in
    directory.
  invalid_token: Invalid token. Please reauthorize or check secret key
  invalid_api: Invalid API credentials
  woocoommerce_authentication_error:
    Invalid API credentials. Please reauthorize or
    ensure keys have 'Read' permission
  woocommerce_rest_error:
    "API error: Current Woocommerce role may not have permission
    to view resources"
  source_host_500: Source server returns 500 Internal Server Error.
  xml_node_mapping: Invalid XML node mapping. Please check field mapping.
  authentication_failed: Authentication failed. Please check your login and password.
  dropbox_link_not_found: No file found. Please ensure the Dropbox URL is correct.
  file_not_found: File not found. Please check file name.
  connection_failed: Connection fail. Please check Source HOST.
  connection_timeout: Timeout while connecting to Source HOST.
  invalid_json: Invalid JSON. Please check settings or contact support if persists.
  file_invalid:
    Incorrect row separator. <NAME_EMAIL> for
    assistance.
  please_check_username_and_password: Please check username & password.
  invalid_file: Invalid file. Please contact support
  please_check_settings: Please check the settings or contact support for help
  snappy_plan: User in Free plan. Products are not synced.
  no_process_needed: No process needed.
  process_after_upload: Process automatically after upload
  existing_process_running: Existing process running. Goes to queue again.
  invalid_url: Invalid URL, feed file not found. Please check and try again.
  malformed_google_spreadsheet: Malformed Google spreadsheet.
  please_check_source_host: Please check your Source Host.
  no_such_file_or_directory: No such file or directory
  compress_file_corrupted: The file is corrupted. Please ensure the file is valid.
  connection_verified: Connection verified.
  please_retry: Please try again
  To get started, edit <1>src/App.js</1> and save to reload.:
    To get started, edit
    <1>src/App.js</1> and save to reload.
  out_of_import_credit:
    You are out of Import Credit. Please purchase Import Credits
    to enjoy this feature.
  snappy_plan_run_update_feed:
    You are on Free Plan. Please subscribe to at least
    Basic Plan to sync products from multiple sources.
  snappy_plan_run_import_feed:
    "You are on Free Plan. Please buy some syncX: Stock
    Sync Credit to import product."
  trial_expired: Your full access has ended. %{planLink} to a paid plan to continue.
  feed_not_found: Unable to find feed with id %{id}
  get_notified_via_email: Receive notification via email
  changes_log: Changes log
  download_low_stock: Download low stock
  download_product_status: Download product status
  no_change_row_tooltip:
    "syncX: Stock Sync did not detect any file change so no update
    performed. If you like to enforce update, please click the link to reset. Once
    done, the following process will update accordingly."
  feed_no_change:
    Feed data <1>no change</1> detected and no update performed. Click
    <3>Force Update</3>.
  delete_all_products_imported:
    Undo all added products from this activity and credits
    will be returned
  undo_import_confirm: Undo newly added products?
  loading: loading
  undo: Undo
  feed_failed_bulk:
    Feed can't be processed now due to another running feed. Please
    try again later
  number_product_updated:
    "Total updated divide by total variants. There are %{number_product_updated}
    updated. "
  download_not_found_in_feed:
    "Download list of %{productID} not found in the feed
    file "
  download_not_found_in_store:
    Download list of %{productID} not found in the store
    (no match from the feed file)
  not_in_store: Not in store
  not_in_feed: Not in feed
  product_status: Product status
  process_successful: Process successful
  process_fail: Process fail. Please refer to the remark
  process_skipped: Process is skipped. Source feed file is the same as previous
  please_upgrade_your_plan:
    Please upgrade your Plan accordingly before it affects
    the store inventory update
  over_limit: Over limit
  see_more_activity: more activity logs
  history: History
  close: Close
  email_settings: Email Settings
  invalid_email_address: Not valid email address
  notification_email: Notification email
  sync_success_or_failure: Sync Success or Failed
  sync_failure_only: Sync Failure Only
  all_except_email_confirmation: All except email confirmation (for Email import only)
  no_notification: No notification
  send_email_when: Send email when
  help_text_product_key_xml:
    'Select a node from the feed file as the product key
    for the Identifier on the left. The number of matches depends on this field. eg:
    "SKU", "SKU++Colour", "SKU++Colour++Size", "1++2++3".'
  help_text_product_key_edi:
    Column range, eg. 1-15 (1 is start position, 15 is the
    last position of the column)
  help_text_edi:
    Column range, eg. 1-15 (1 is start position, 15 is the last position
    of the column)
  help_text_quantity_xml:
    eg. <Qty> node mapping "Qty". <node code="ABC123"> attribute
    mapping is "@:code"
  help_text_quantity_ebay:
    "quantity_available : remaining quantity on eBay Or quantity
    : variant's quantity on eBay"
  help_text_quantity_quickbooks: The quantity value in QuickBooks is 'quantity_on_hand'.
  help_text_quantity_vend: The quantity value in Vend is 'inventory_level'.
  help_text_quantity_unleashed: The quantity value in Unleashed is 'AvailableQty'.
  help_text_quantity_default: The column name or column index.
  leave_this_field_blank: Leave this field blank to skip updating
  group_multiple_variants: Group multiple variants as a single product
  help_text_product_id: Select a field to uniquely identify store products
  placeholder_product_id: Select store product identifier
  placeholder_store_prefix: Optional store prefix/postfix
  label_store_prefix: Store prefix/postfix
  label_prefix: Prefix on Feed
  placeholder_prefix: PRE-
  help_text_store_prefix:
    "<0>eg. To match products with prefix <2>ABC123</2>, define
    as <5>ABC</5>.</0>"
  help_text_prefix_1:
    "<0>eg. <1>PRE-</1> will convert all Feed's SKU from <3>PRE-xxxx</3>
    to <6>xxxx</6>.</0>"
  feed_file: Feed File
  field_mappings: Field Mappings
  advanced: Advanced
  delete_feed: Delete Feed
  label_add_to_init_quantity: Add/Deduct to existing store's quantity.
  label_only_deduct_quantity: Only deduct from existing store’s quantity.
  help_text_only_deduct_quantity:
    "By default, syncX: Stock Sync will replace the
    existing quantity. Only enable this when feed's quantity are to be add/deduct
    to the existing store's quantity."
  label_rules_json: Quantity rules
  label_zero_qty: Set available quantity to non-negative (0 and positive) values.
  help_text_zero_qty:
    "By default, syncX: Stock Sync set any negative quantity to
    zero. Only disable this when you want to allow negative value in your inventory."
  help_text_rules_json_1:
    e.g. Rule = HIGH -> 50, "HIGH" in feed quantity value will
    be converted to 50 as in store quantity.
  help_text_rules_json_2:
    '"With formula" quantity rule, store quantity values can
    be set to 10, +10, -10'
  label_tags: Replace store product's tags
  help_text_tags:
    "When enabled, syncX: Stock Sync will remove ALL existing tags on
    store and only add new tags from source"
  help_text_variant_group:
    Leave empty if the product importing doesn't have variants.
    The column to group variants with the same name, e.g. variants in this group have
    "Var ID" as the value for this column. Handle, product’s title or any unique key
    can be used.
  label_weight: Weight unit
  placeholder_weight: select weight unit
  required_field: Required field
  help_text_barcode: e.g. UPC, ISBN
  help_text_metafield:
    When source value is blank, it will default to DASH "-". Default
    data type is "string"
  label_metafield_key: Metafield Name
  label_metafield_namespace: Metafield Namespace and Key
  label_metafield_owner: Metafield owner
  label_metafield_type: Metafield content type
  help_text_price_xml: The node name. eg. Price, Dollar, 7.
  help_text_price_ebay: current_price - selling price on eBay
  help_text_price_quickbooks: The price value in QuickBooks is 'unit_price'.
  help_text_price_default: The column name or column index. eg. Price, Dollar, 7.
  label_price_delimiter: Price delimiter
  label_price_round: Price round
  help_text_price_conditions_1: Valid price range keywords are "price" and "any"
  help_text_price_conditions_2:
    Accept column number with numerical value. eg.col1_f,
    col2_f
  help_text_price_conditions_3: "Note: Price conditions only apply to Selling price"
  help_text_compare_at_price_conditions:
    "Note: Price conditions only apply to Compare
    at price"
  help_text_cost_conditions: "Note: Price conditions only apply to Cost"
  label_images_force_override: Skip existing images
  help_text_images_force_override:
    "When unchecked, syncX: Stock Sync will only add
    images if the product has no image."
  label_images_url_prefix: URL prefix
  help_text_images_url_prefix:
    Add URL prefix(i.e. domain of the image) to every image
    links.
  label_option_name: Option name
  label_mapping: Field Name
  label_auto_mapping: Read option name from feed file by mapping column below
  label_policy_continue_value:
    Allow purchase when out of stock for value in feed
    is?
  label_policy_deny_value: Disallow puchase when out of stock for value in feed is?
  help_text_compare_price_at_xml: The node name. eg. Price, Dollar, 7.
  help_text_compare_price_at_default:
    The column name or index. eg. Price, Dollar,
    7.
  label_compare_price_at_formula: Compare at Price formula
  help_text_compare_price_at_formula_1:
    e.g. to add 30% to all updated variants, formula
    is * 1.30.
  help_text_compare_price_at_formula_2: "Note: only apply to Compare at Price"
  help_text_published_xml:
    The node name that indicates hidden products. eg. Price,
    Dollar, 7.
  help_text_published_default:
    The column name or column index that indicates hidden
    products. eg. Price, Dollar, 7.
  label_eq_hide_value: Unpublish value indicator
  help_text_eq_hide_value:
    "It will not unpublished if quantity still available unless
    force unpublished is enabled. Example: 0, hide, unpublish, out-of-stock"
  label_eq_show_value: Publish value indicator
  help_text_eq_show_value_1: "Example: show, publish, back-in-stock, 1"
  help_text_eq_show_value_2:
    The value 1 is inclusive of 1 and number greater than
    1.
  please_select_a_store_field: Please select a store field
  placeholder_price_range: Price range
  placeholder_price_formula: Formula, eg. + 2.5
  click_or_drag_sample_file: Click or drag sample file here
  click_for_guided_mapping: Need help? Click here for guided field mapping.
  assign_product_identifier: Assign product identifier for your new product
  matching_product_identifier:
    Matching product identifier between products on Feed
    file and Online store
  at_least_one_variant_option_required:
    At least 1 Variant Option required. Click
    "Add field" below
  store_field_selected: Store field selected. Please select another field
  not_sure_which_feed_to_create: Not sure which feed to create?
  create_new_import_feed: Create new import feed?
  create_new_update_feed: Create new update feed?
  click_here: Click Here
  to_unlock: to unlock
  for_new_products: For new products
  for_existing_products: For existing products
  run_now: Update now
  import_now: Import now
  feed_cannot_run_now:
    Feed can't start now because it's currently queuing or processing
    and it might take some time depending on the server status. Please be patient
    with us.
  are_you_sure: Aborting the feed will stop the current process.
  are_you_sure_uninstall:
    "This will uninstall syncX: Stock Sync from Woocommerce.
    Proceed?"
  cancel_process: Abort Process
  start_import_by_uploading_file_here: Start import by uploading file here
  start_update_by_uploading_file_here: Start update by uploading file here
  feed_settings: Edit settings
  loading_feed_settings: Loading feed settings...
  we_removed_the_refresh_button:
    We've removed the refresh button, feed now automatically
    refreshes latest changes!
  rename_feed: Rename feed
  make_a_copy: Make a copy
  import_feed_copy: Add Product
  update_feed_copy: Update Product
  setup_new_feed: Setup New Feed
  filters_desc: Filters provide better control and more effective updates
  filters: Filtering Shopify Products
  auto_publish_reset_qty: Auto publish ALL products in store with quantity > 0
  inventory_management: Inventory Management
  inventory_management_help:
    "Please select Shopify if syncX: Stock Sync is used to
    update the quantity."
  inventory_management_none: Don't track inventory
  shopify_tracks_inventory: Shopify tracks products' inventory
  caution: Caution
  import_check:
    Adding new product check for existing products. Won’t create duplicate
    if existing product identifier found.
  import_auto_publish:
    Auto publish all new added products. By default, new products
    are set to draft.
  import_new_tags: " All newly added products will tagged with"
  enter_feed_name_placeholder: Enter feed name, Eg. Source 1
  feed_renamed_to: Feed renamed to %{profile_name}
  ignore_track_variants: Ignore variants with "track inventory"
  ignore_zero_quantity: Ignore variants with zero quantity
  auto_hide_product: Auto hide ALL products in store with zero quantity
  auto_reset_shopify_tracking: Track inventory (Set up inventory tracking for products)
  low_stock_level: Low Stock Level
  apply_same_product_id: Allow update duplicate products with the same product identifier
  advance_option_header: Optional advanced settings for miscellaneous updates
  reset_all_quantity:
    "<0>Reset <1>ALL</1> variant quantity to 0 if <3>UNMATCHED</3>
    with SKU or Barcode in feed file (selected location)</0>"
  unmatched_all_variants: "<0>Set out of stock to <1>ZERO Quantity</1></0>"
  use_published_at: Only hide products from the online store
  unmatch_against_updated_variants:
    "<0>Set out of stock to <1>ZERO Quantity</1> that
    was updated by syncX: Stock Sync before</0>"
  note_filters:
    To include/exclude certain products with these vendors or product
    types, please provide a comma-separated name. For collection, you can choose from
    the dropdown. Does not support wildcard *.
  bigcommerce_note_filters:
    To include certain products by providing multiple brand
    ID, separated by comma.
  note_filters_else: Else please leave blank to update to all products.
  vendor_filter: Vendor Filter
  include: Include
  exclude: Exclude
  product_type_filter: Product Type Filter
  collection_filter: Collection Filter
  collection_not_found: Collection not found
  product_not_found: Product not found
  loading_collections: Loading collections...
  vendor_placeholder: Brand A,"Brand B, LLC",Brand C
  vendor_placeholder2: Apple,Orange,"WeBoost, LLC"
  type_placeholder: Type A,"Type B, LLC",Type C
  sku_placeholder: SKU0001,SKU0002,SKU0003
  snappy_inventory_update:
    Free Plan - Bulk update of Quantity, Price or Compare at
    Price
  quick_batch_update: Quick inventory batch update by SKU for
  download_csv: Download CSV
  snappy_step1: Download your Shopify products in CSV format.
  snappy_step2:
    Change the quantity, price and compare price of your products in the
    CSV file.
  snappy_step3:
    Please ensure the file is saved or exported as CSV file before upload
    the modified CSV file here.
  snappy_step4: Check the status of your update and activity logs here.
  feed_status: Feed Status
  activity_logs: Activity logs
  file_upload_success: Uploaded successfully
  file_upload_limit: "File upload limit is "
  file_upload_fail: file upload failed
  file_upload_invalid:
    "File Error: Please add a file extension to proceed - change
    the copy"
  file_upload_desc: Click or drop your feed file here
  file_upload_modal_title: Upload file for %{feedName}
  assistance_contact: For assistance, <NAME_EMAIL>
  column_separator: Column Separator
  first_row_is_header: First row is header
  parent_node: Parent Node
  select_xml_file: Type here or click below to select from XML file
  help_text_parent_node: What is a parent node?
  sample_csv_file: Download sample.csv
  sheet_name: Sheet name
  variant_node: Variant Node
  help_text_variant_node: Only needed for variants in nested node.
  xml_loaded: XML sample loaded. Click dropdown above to select parent node.
  upload_xml_here: Click or drag sample XML file here
  select_parent_node_here: Click here to select Parent Node from XML file.
  flip_switch_to_turn_it: Flip switch to turn it %{enabled}
  edit_schedule: Edit schedule
  every: Every
  hours: hours
  edit: Edit
  started: started
  paused: paused
  "off": "Off"
  "on": "On"
  schedule_updated: "%{profile_name} schedule updated"
  how_frequent_to_run: "%{feed_title} %{icon} How frequent should this feed run?"
  schedule_frequency: Schedule Frequency
  schedule_time: Schedule Time
  global_time_zone: Global Time Zone
  source_host: Source host
  source_url: Source url
  clear_google_sheet: Clear google sheet before write
  google_out_insert:
    Enabling this will create new product(s) line as well update
    existing
  key: Key
  wsdl_url: WSDL URL
  action_name: Action Name
  google_url_tooltips: https://docs.google.com/spreadsheets/d/example
  ftp_url_tooltips: FTP Host, eg. vendor.com:20, ftp.vendor.com, ***********
  sftp_url_tooltips: FTP Host, eg. vendor.com:22, ftp.vendor.com, ***********
  dropbox_url_tooltips: Full URL. eg. https://www.dropbox.com/s/iwu/abc.csv?dl=0
  microsoft_sharepoint_url_tooltips:
    "sample URL: https://<tenant>.sharepoint.com/...
    The shared link needs to be publicly downloadable URL"
  s3_url_tooltips: eg. file_sample/inventory.csv
  soap_url_tooltips: Full URL. eg. http://exmple.com/services/get_inventory.asmx?wsdl
  soap_path_tooltips: Eg. inventory_status
  rest_api_url_tooltips: eg. https://api.exmple.com/inventory/all
  search_for_node: Search for node
  select_node: Please select node
  daily: Daily
  minutes: Minutes
  hourly: Hourly
  weekdays_only: Weekdays
  weekends_only: Weekends
  mon_to_sat_only: Mon - Saturday
  sundaythursday: Sun - Thursday
  tuesdaysaturday: Tues - Saturday
  wednesdaysaturday: Wed - Saturday
  wednesdaysunday: Wed - Sunday
  sundayfriday: Sun - Friday
  on_email_received: On Email Received
  on_file_uploaded: Manual upload
  authorize: Authorize
  authorized: Authorized
  redirect_to_authorization_page:
    " settings updated. Redirecting to authorization
    page..."
  ebay_authorize_tooltips:
    You might need to authorize again if failed to get authorize
    from Ebay
  etsy_authorize_tooltips:
    You might need to authorize again if failed to get authorize
    from Etsy
  one_drive_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from One Drive
  quickbooks_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from QuickBooks
  zoho_inventory_authorize_tooltips:
    You might need to authorize again if failed to
    get authorize from Zoho Inventory
  login_field_name: Login Field Name
  password_field_name: Password Field Name
  destination_email_helptext:
    The destination email which exported file will be send
    to
  destination_email: Destination Email
  email: Receive Email
  email_helptext:
    Send email through To, CC or BCC to this given address with feed
    file as attachment.
  etsy_term: The term 'Etsy' is a trademark of Etsy, Inc.
  etsy_certified:
    This application uses the Etsy API but is not endorsed or certified
    by Etsy, Inc.
  file_rename: After Process
  rename_the_file_after_process: Rename the file after process
  login: Login
  namespace_identifier: Namespace Identifier
  namespace_identifier_tooltips: Eg. thz, name, item
  password: Password
  password_placeholder: "(use saved password)"
  path_and_file_name: Path and File name
  pathfilename_tooltips: eg. Path and file name. eg. /www/project/data/inventory.csv
  please_change_google_permission:
    " Please change Google permission to %{anyone}
    for it to work"
  anyone_with_the_link: Anyone with the link
  to_request_a_new_channel:
    To request a new channel, please fill up the Google form
    we've opened in a new tab for you.
  request_new_channel_thankyou:
    Thank you for your time. %{dashboard} or select existing
    feed type to continue updating.
  return_to_dashboard: Return to dashboard
  access_key_id: Access Key Id
  bucket_name: Bucket Name
  secret_access_key: Secret Access Key
  source_authentication: Source Authentication
  please_select_a_source_type: "Please select a source type below to begin:"
  direction: Direction
  feed_type: Feed Type
  request_a_new_channel: Request a new channel
  source_url_can_obtain: Source url can obtain by sharing document in One Drive
  ssh_key: SSH Key
  ssh_key_tooltips: Public or private ssh key in
  pem_format: PEM format
  connection_successful: Connection Successful
  please_complete_all_fields: Please complete all fields required to test connection
  test_connection: Test connection
  whitelist_helptext: " Whitelist the IP address %{ip1} in your server. %{link}"
  ftp_tutorial: Watch FTP tutorial video
  value: Value
  total_imported: Products added
  Search_feed_name...: Search feed name...
  time_out_of_range: Time out of range. Please update your schedule.
  periodical_job: Every %{job_interval} hours
  minute_periodical_job: Every %{job_interval} minutes
  job_type_at_timezone: "%{translated_job_type} %{formatted_job_timezone}"
  search_feed_name...: Search feed name...
  fliter: Fliter
  all_feeds: All Feeds
  queuing_feeds: Queueing Feeds
  processing_feeds: Processing Feeds
  import_feeds: Add Product
  update_feeds: Update Product
  remove_feeds: Remove Product
  feed_name_a_to_z: "Feed Name: A-Z"
  feed_name_z_to_a: "Feed Name: Z-A"
  latest: Latest
  oldest: Oldest
  "server_status:": "Server status:"
  two_hours_delay: "> 2 hours delay"
  one_hour_delay: "~ 1 hour delay"
  ten_to_twenty_min_delay: 10 - 20 min delay
  five_min_delay: "< 5 min delay"
  refresh_feeds: Refresh feeds
  new_feed_created: New feed created. Loading feed settings...
  feeds_refreshed: Feeds refreshed
  dont_show_again: Don't show again
  email_settings_updated: Email settings updated.
  pin_feeds_exceeds_limit: You have exceeded pin limit
  error_while_updating_feed: Error while updating feed
  invalid_operation: Invalid operation
  click_to_unpin: Click to unpin
  pin_feed: Pin feed
  uploaded_file: Upload file
  online_file: Download URL (link)
  shopify_store: "Store to Store (requires installation of syncX: Stock Sync)"
  ftp: FTP
  sftp_in: SFTP
  stileo: Stileo
  walmart: Walmart
  wayfair: Wayfair
  amazon: Amazon
  xero: Xero
  xero_authorize_tooltips:
    You might need to authorize again if failed to get authorize
    from Xero
  aliexpress_in: AliExpress
  aliexpress_out: Export to AliExpress (beta)
  aliexpress_scraper: AliExpress Scraper
  aliexpress_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from AliExpress
  aliexpress_app_key: App Key
  aliexpress_app_secret: App Secret
  ftp_houzz: Houzz - SFTP
  walmart_api: Walmart API
  walmart_client_id: Client ID
  walmart_client_secret: Client Secret
  read_from_google_spreadsheet: Google Sheets
  write_to_google_spreadsheet: Write to Google Sheets
  google_drive: Google Drive
  dropbox: Dropbox
  dropbox_with_password: Dropbox With Password
  receive_email: Receive Email
  send_out_email: Send Out Email
  ebay_in: Read from eBay (Fixed price items only)
  ebay_out: Write to eBay
  etsy_in: Read from Etsy
  etsy_out: Write to Etsy
  ftps: FTP (TLS Enabled)
  one_drive_file: One Drive
  s3: S3 Read File
  custom_login: 3rd Party Download with Login
  soap: Soap (beta)
  kevro: Kevro
  rest_api: Rest API
  quickbooks: Intuit QuickBooks (Beta)
  timezone: Timezone
  public_token: Public token
  help_text_public_token:
    Use this public token to share your inventory with your
    buyers
  use_highest_quantity_value:
    Use highest quantity value when same SKU detected from
    multiple feeds
  add_on_import: Add Ons for IMPORT only
  available_basic_plan: credits remain after app is reinstalled
  strictly_no_refunds: Credit refund policy
  buy_now: Buy Credits - $%{flexi_limit}
  notification_settings_updated: "<0>Notification</0> settings updated."
  not_valid_email_address: Not Valid Email Address
  advance_settings_updated: Advance settings updated.
  save: Save
  google_spreadsheet: Google Spreadsheet
  ebay: eBay
  etsy: Etsy
  sku: SKU (Variant's SKU)
  barcode: Barcode
  id: ID
  variants_title: Variant's title
  products_title: Product's title
  handle: URL / Handle
  products: products
  for: for
  buy_import_credits: Buy Import Credits
  to_suspend_for_later_use: to suspend for later use
  current_plan: Current plan
  pay_for_a_block_of_credits_you_need: "Pay for a block of credits you need "
  import_only: Import only
  monthly_subscription_badge: Update Product
  view_plan: View plan
  import_new_product: Lets you import new products directly into your Shopify store
  update_only: Update only
  your_plan: Current Plan
  upgrade_now: Upgrade now
  downgrade: Downgrade
  http_method: HTTP Method
  header_params: Header Params
  body_raw: Body Raw
  feed_being_processed: Feed is already being processed.
  slide_plan: Slide left or right to choose the right plan
  skus: SKUs
  starter_pack_come_with: "<0>{starter_pack}</0>"
  snappy_plan_description: Batch inventory update by matching SKU for only $%{snappyPlanPrice}/month
  feed_started: "<0>Feed <1><0>%{profile_name}</0></1> started</0>"
  feed_cancelled: "<0>Feed <1><0>%{profile_name}</0></1> cancelled</0>"
  sku_limit: limit <1><0>%{limit}</0></1> SKUs
  feed_limit: up to <1><0>%{source_limit}</0> update</1> feeds
  schedule_frequency_min: Schedule frequency <1><0>%{schedule}</0></1>
  free_credit: Free credits
  vend: Vend
  vend_authorize_tooltips:
    You might need to authorize again if failed to get authorize
    from Vend
  unleashed: Unleashed
  api_id: API ID
  api_key: API Key
  api_log: API Log
  published_filter: Published Filter
  any: Any
  published: Published
  json_data_key: Enter JSON data key here. eg. 'data'
  json_data_key_label: JSON Data Key
  from_ftp: FTP - Download
  to_ftp: Upload to FTP (CSV only)
  help_text_description: Support multiple columns, separated by comma. eg "description1,description2".
  sku++option1: SKU + Option 1
  sku++option2: SKU + Option 2
  sku++option1++option2: SKU + Option 1 + Option 2
  handle++option1++option2: Handle + Option 1 + Option 2
  handle++option1: Handle + Option 1
  handle++option2: Handle + Option 2
  barcode++option1: Barcode + Option 1
  sku++barcode: SKU + Barcode
  sku++barcode++option1: SKU + Barcode + Option 1
  not_enough_column: Wrong column format
  email_subscriptions: Email Subscriptions
  sync_success: Sync success
  sync_failure: Sync failure
  newsletter: New features and special offers
  incoming_email_confirmation: Incoming email confirmation
  low_stock_alert: Low stock alert
  low_stock_alert_helper_text:
    Low stock level that has been set to each update product
    feed
  low_credit_alert: Low credit alert (less than 10)
  zero_qty_update_alert_helper_text:
    When update stops reaching the threshold selected
    in <2>Advanced</2>
  milestone: Milestone
  transactions: Transactions
  label_restrict_conditions: Don't update %{fieldName} when...
  vendor: Vendor
  product_type: Product type
  placeholder_restrict_conditions: Value A, Value B, C....
  help_text_restrict_conditions:
    The value will not be applied if conditions above
    are matched.
  add_condition: Add condition
  tags_filter: Tags Filter
  help_text_email_notification:
    Support multiple emails, separated by comma. eg <EMAIL>,
    <EMAIL>
  tags: Tags
  filter: Filter
  title: Product's title
  collection: Collection
  skip_import_with_zero_qty: Skip add product with zero quantity.
  clf: CLF Distribution
  clf_parent_node: Parent Node
  clf_parent_node_tooltips: Please enter 'Product' as default if you are not sure.
  clf_source_url_tooltips:
    Please enter 'http://services.clfdistribution.com:8080/CLFWebOrdering_Test/WebOrdering.asmx?WSDL'
    as default url if you are not sure.
  time_saved: Time Saved
  news_feed: News Feed
  disconnect: Disconnect
  confirm_disconnect: Confirm disconnect?
  get_this_calculation: Find out how we get this calculation.
  time_you_save: "Amount of time you saved using syncX: Stock Sync"
  xml_link: Click for XML guide to set up
  label_force_override_compare_at_price:
    Set store value to blank when feed value
    is blank (value zero is not blank)
  read_compressed_file: If file is compressed (.zip or .gz), file format must be specified
  contact_supplier: Contact Supplier/Dropshipper
  etsy_store_name_helper: https://www.etsy.com/shop/<store_name>
  supplier_email: Supplier/Dropshipper Email
  cc: CC
  message: Message
  preview_email: Preview Email
  send_email: Send Email
  email_send_successfully: Email send successfully
  supplier: Contact supplier
  filename_with_timestamp:
    "<0>For filename with timestamp, please click <2>here</2>
    for more info</0>"
  label_static_value: Static Value
  flexi_description:
    "<0>You have <1><0>%{import_limit_skus}</0></1> credit(s) left.
    Every new product successfully added or removed will cost one credit (only $0.01
    or less). You may UNDO added products and used credits will be returned back to
    you. However, removed products can't be UNDONE. Is pay as you need, you do NOT
    have to subscribe to the Monthly Subscription. </0>"
  your_update_plan: "You are currently subscribing "
  monthly_subscription_description:
    "You are currently subscribing <2><0>%{full_plan_name}</0></2>
    plan. These monthly subscription plans are for updating existing products only.
    Please select the plan based on your total variant in store. No Import credit
    will be given for these plans. All plans include <4>UNLIMITED MANUAL UPLOADS</4>
    but scheduling frequency is based on the selected monthly subscription. "
  learn_more: Learn more
  help_text_sheet_name: "Support multiple sheets separated by comma. eg: Sheet1,Sheet2"
  label_fallback_to_price_if_less_than_price:
    Apply same value as selling price when
    Compare at Price is less than variant's selling price
  variant_image_link: Link image to variant when the image url is same row as variant
  assign_variants_to_first_image: Always assign first product images to all variants
  label_images_alt_mapping: Image ALT column mapping
  label_video_links_alt_mapping: Video Links Alt Column Mapping
  help_text_images_alt_mapping: Column mapping to get image ALT values
  help_text_video_links_alt_mapping: Column mapping to get video links ALT values
  file_format: File format
  disabled: Disabled
  join_supplier_network: Join Supplier Network - LAST CALL!!
  supplier_network_description:
    Want to expand your market? Make your Shopify store
    as a supplier to reach more customers.
  pre_signup_now:
    Pre sign-up now and enjoy an extended Full Access period when it
    launches.
  sunday_only: Sunday
  saturday_only: Saturday
  friday_only: Friday
  thursday_only: Thursday
  wednesday_only: Wednesday
  tuesday_only: Tuesday
  monday_only: Monday
  plan_promote: "<0>Unlimited</0> manual uploads"
  add_filters: Add Filters
  filter_params_info:
    "To include or exclude certain products or both, please provide
    column name/number as the key and enter the value you wish to include or exclude
    or both from feed data. "
  box: Box
  box_authorize_tooltips:
    You might need to authorize again if failed to get authorize
    from Box
  enjoy_our_app: "Do you enjoy using syncX: Stock Sync?"
  label_override_publish: Force unpublished when quantity still available
  box_label: Folder and File Name
  box_tooltips: eg. Folder and file name. eg. "data/inventory.csv", "datafeed.csv"
  box_extra_info: The file must exist in the authorized box user account.
  error_status: "<0>Server error, try to<1>refresh</1>your page</0>"
  supplier_feed_created: New supplier feeds created.
  review: "Leave a ⭐⭐⭐⭐⭐ REVIEW to make syncX: Stock Sync better"
  feed_filters: Filtering Feed Data
  filter_col: Column Name/Index
  ftpsimplicit: Implicit FTP (TLS Enabled)
  label_existing_product_identifier: Add to an existing product that matching this
  new_feed: Setup New Feed
  setup_a_new_feed: Setup a New Feed
  spend_several_mins_setting_up:
    Save hours of tedious work with only 10 minutes of
    setup!
  create: Create
  website: Website
  create_and_setup: Proceed to Setup
  give_feed_name: Name your new feed
  express_setup: Choose from template - NEW
  no_template_selection:
    No selection for now. We are working hard to add more suppliers
    in days to come, stay tuned!
  choose_existing_supplier:
    'Choose an existing supplier template by clicking <2>"Create"</2>
    to start generating feeds onto your dashboard. We have prepared these templates
    to help you get started quickly:'
  setup_configure:
    "Let syncX: Stock Sync knows how to read feed file or export Shopify
    data."
  find_supplier: Can't find your <1></1> ?
  read_timeout: Connection timeout while reading
  no_field_mapping: Please configure field mapping
  how_many_imports: "I like to buy "
  label_postfix: Postfix
  placeholder_postfix: "-END"
  configure_field_mapping_before_proceed:
    Please map required fields at Step 2 Field
    Mappings
  help_text_postfix_1:
    "<0>eg. <1>-END</1> will convert all Feed's SKU from <3>xxxx-END</3>
    to <6>xxxx</6>.</0>"
  label_weight_formula: Weight converter
  choose_this_supplier: Confirm choosing this supplier?
  quick_search: Search for supplier or template name...
  template_search_placeholder:
    Search connection methods. Eg, FTP, Email, Upload file
    and more
  ftp_multiple: FTP (multiple files)
  ftps_multiple: FTPS (multiple files)
  configure_filters: Apply Filters
  configure_filters_for: Apply Filters for %{feedName}
  save_and_close: Save and Close
  cancel: Cancel
  no_filter: No Filter
  saved_successfully: Saved Successfully
  filters_moved: The filters configuration is now available on the dashboard.
  please_proceed_email: Please send email with attachment again to enforce update
  too_many_request: Host returns 'Too Many Requests' error
  customer_setup:
    Build a custom feed profile if you have need to connect to supplier
    or warehouse feed data via manual file upload, FTP, Email, Dropbox, Google Sheet
    and many more. It supports CSV, XLS, XLSX, XML and JSON format.
  manual_setup: Create your own
  update_plan_description:
    These monthly subscription plans are for updating existing
    products or inventories. Please select the plan based on your total variant in
    store. No Import credit will be given for these plans.
  label_override_to_deny: Disable "Continue selling when out of stock"
  label_base_on_quantity:
    Enable "Continue selling when out of stock" when quantity
    MORE THAN ZERO and vice versa
  update_customer_setup:
    Create your own feed profile from scratch. Should you have
    any questions, please refer to the %{helpDoc} as a guide. You can also watch a
    %{tutorialVideo} before you begin.
  api_error: Something went wrong, please try again.
  need_help: No, I Need Help
  help_form_label: Click here for support
  ask_us: Ask us
  help_form_tooltip: We'll respond to %{user_email} as soon as possible.
  message_cant_be_blank: Message can't be blank
  ticket_created_successfully:
    Ticket created successfully. We'll get in touch with
    you soon.
  custom_file_name: Custom File Name
  custom_file_name_help: Rename the file to this given file name
  process_now: Start process
  total_number_of_variants: Total variants
  total_monthly_credits: Monthly Credits
  need_fast_schedule_time:
    Need faster schedule time? %{contactSupport} and we will
    evaluate
  contact_us: Contact us
  cant_find_the_right_plan:
    Can't find the right plan for you? Let us help you figure
    it out
  custom_plan: Custom plan
  connection_method: Connection Method
  inventory_location: Inventory location
  label_case_sensitive: Case sensitive
  label_ignore_words: Ignore words
  label_auto_ignore_option_words: Auto ignore words from variant option values
  label_title_separator: Multiple title separator
  label_deduct_quantity_from_column: Deduct quantity from column within feed file
  help_text_title_separator: Define column separator for multiple title fields
  help_text_deduct_quantity_from_column:
    Define column index (e.g. col3) if targeted
    column is the third column. The column mapped to quantity field will deduct the
    value in column above
  tooltips_case_sensitive:
    Enabling this option will differentiate upper capital and
    lower capital identifiers. For example, Abc123, abc123 and ABC123 are all considered
    as different products.
  discontinued: Discontinued
  auto_publish_product: Auto publish products within feed file with quantity > 0
  auto_unpublish_product: Auto-hide products within feed file with zero quantity
  label_static_flag: Read value from field instead of mapping (static value).
  banggood: Banggood
  app_id: App ID
  app_secret: App Secret
  app_secret_placeholder: "(use saved app secret)"
  category_id: Category ID
  help_text_quantity_banggood: The quantity value in Banggood is 'quantity'.
  manage_billing: Manage Billings
  google_shopping_authorize_tooltips:
    You might need to authorize again if failed
    to get authorize from Google
  google_shopping_out: Write to Google Shopping
  google_shopping: Google Shopping
  merchant_id: Merchant ID
  google_shopping_disclaimer:
    Before authorizing the connection, please follow this
    guide to make sure you’re ready.
  view_more: View More
  file_encoding: File Encoding
  label_field_prefix: Prefix
  help_text_field_prefix: Add prefix (i.e. PRE-, ABC-) to field.
  ignore_tags: Ignore tags
  help_text_ignore_tags: List of tags (separated by comma) to be ignored from importing
  only_tags: Include tags
  help_text_only_tags: List of tags (separated by comma) to be imported only
  label_add_tags: Append new tags to existing tags
  help_text_add_tags: "NOTE: Disable this will REMOVE from existing tags"
  having_problem_setup: Having problem setting up?
  rename_my_file_to: Rename/Move My File To
  ftp_whitelist: IP Whitelisting
  watch_tutorial: Watch Tutorial
  woocommerce: WooCommerce
  woocommerce_authorize_tooltips:
    Click Authorize button if role is %{role_one} or
    %{role_two} to automatically get the API from WooCommerce. Otherwise, please request
    API consumer key and secret from store admin and manually enter below.
  woocommerce_store_url_tooltips: eg. https://yourstorename.com
  store_url: Store URL
  help_center: Help Center
  Tutorial: Tutorial
  demo_store: Demo Store
  support: Support
  help_text_images_force_override_warning_1:
    When enabled, it will check existing
    image file names before importing new images
  help_text_images_force_override_warning_2:
    Slow process, don't run update frequently.
    It will keep replacing the image.
  help_text_images_force_override_warning_3: Not good practise for SEO
  google_shopping_category: Google Product Category
  sku++option3: SKU + Option 3
  sku++option1++option3: SKU + Option 1 + Option 3
  sku++option2++option3: SKU + Option 2 + Option 3
  auto_select_location: Auto Select Location
  auto_select_location_tooltips:
    Auto-select the first inventory location found if
    the selected inventory location is not available.
  video: Video
  sku++option1++option2++option3: SKU + Option 1 + Option 2 + Option 3
  label_sum_quantities: Sum all quantity column mapped.
  wholesalesportwear: Wholesale Sportwear UK
  wholesalesportwear_url_tooltips: eg. https://dropship.wholesalesportswear.co.uk/api/all-stock
  stileo_out: Stileo.it - Poland Fashion Marketplace
  wayfair_out: Wayfair Inventory
  amazon_out: Amazon Warehouse
  ftp_houzz_out: Houzz - FTP Inventory
  walmart_api_out: Walmart API
  turn14: Turn14 Distribution
  giga_cloud: GigaCloud B2B
  spm_network: SPM Network
  tpl_central: 3PL Central
  bsale_chile: Bsale Chile
  boards_and_more: Boards & More
  ecomdash: Ecomdash
  ecomdash_key: ecd-subscription-key
  hlc: HLC Bike
  brands_distribution: Brands Distribution
  kotryna: Kotryna Group
  store_is_empty: Store is empty
  api_version: Api Version
  api_consumer_key: Api Consumer Key
  api_consumer_secret: Api Consumer Secret
  api_user: API User
  api_token: API Token
  secret_key: Secret Key
  username: Username
  label_images_clear_current_images:
    Warning! This will clear all matching product's
    images before importing as new.
  help_text_images_clear_current_images:
    "NOTE: This option will be disabled after
    the process finish. This is to avoid keep deleting and importing images."
  ensure_file_format: Ensure the file format is %{file_format}.
  credit_badge: Add/Remove Product
  automate_this_feed: Automate this feed
  low_stock: Low stock
  error_list: Error list
  total_removed: Variants removed
  total_archive: Variants archived
  total_updated: Variants updated
  total_exported: Variants exported
  product_removed: products removed
  label_case_convert: Case convert
  append_name_for_file:
    Use [current_file] to represent file name. eg. "[current_file]_updated"
    will renamed to "feed190327_updated.csv"
  email_run_on_certain_hour: Run every %{minHour} hours
  email_with_next_run_time: Run every %{minHour} hours - Next process is at %{nextRunTime}
  no_job_time_available: "%{translated_job_type}"
  trendcollection: Trends Collection
  mma_silver_data_feed: MMA Silver - All Active Items
  mma_silver_retail_xml: MMA Silver - Stock & Cost XML
  fail_to_detect_file_format:
    Fail to Auto detect file format. Please select the file
    format in settings page.
  fail_to_detect:
    Fail to Auto detect file format or your file format is not supported.
    Please select the file format.
  label_default_image_url: Default image URL
  help_text_default_image_url:
    Use this image URL if the product does not have any
    image
  help_text_image_cdn:
    Use to deliver image if image is not retrievable via link (setup
    needed)
  placeholder_default_image_url: https://test.com/products/no_image.jpg
  no_result_found: No result found...
  label_weight_delimiter: Weight delimiter
  coasteramer: Coaster Co. of America
  novaengel: Novaengel
  malabs: Malabs
  malabs_placeholder: eg. mfrno=BX80684I78700
  url_params: URL params
  malabs_tooltips: e.g. limit=200&search=Seagate
  metafield_error: Metafield key required and must be 3 - 30 characters
  metafield_namespace_error: Metafield namespace and key required
  metafield_not_found: Metafield definition not found, please add definition in store
  metafield_namespace_text_count_over_limit: Metafield namespace must be 2 - 20 characters
  metafield_key_text_count_over_limit: Metafield key required and must be 3 - 30 characters
  naturaldispensary: Natural Dispensary
  airtable: Airtable
  africa_altitude: Altitude (Africa)
  intcomex: Intcomex
  xtrader: XTrader UK
  beautyfort: Beautyfort EU
  erply: Erply
  stuller: Stuller
  africa_amrod: Amrod (Africa)
  pleaser: Pleaser
  feed: Auto Clear Discountinued
  update_feed_label: Update Feed
  select_feed_help_text:
    Choose an update feed to remove the discontinued products
    found in the Not in Feed log from your store
  url_tooltips: www.example.com/inventory.csv
  shopify_store_tooltips: "Request for supplier’s public token under Account > Advance. "
  label_images_col_sep: Image column separator
  label_video_links_col_sep: Video links column separator
  label_tags_col_sep: Tags column separator
  help_text_images_col_sep: Support multiple column separator for image field
  help_text_video_links_col_sep:
    Support multiple column separator for video link
    field
  help_text_tags_col_sep: Support multiple column separator for tags field
  total_published: Products published
  total_hidden: Products hidden
  label_body_labels: Labels for additional info
  placeholder_body_labels: Material,Height
  help_text_body_labels:
    "Add labels to 2nd field mapping onwards. eg. Material: 'description2',
    Height: 'description3'"
  wrap_tag: Wrap each column mapped in paragraph(s) with ...
  file_encoding_issue: File encoding issue. Learn more at https://support.stock-sync.com/support/solutions/articles/***********-file-encoding-issue-in-stock-sync
  cant_upload_mutation:
    Multiple feed are running. Please wait a few minutes for it
    to restart
  app_key: App Key
  table_name: Table Name
  host: Host
  user: User
  directory_and_file_name: Directory and filename
  directory_file_name_tooltips: eg. Directory and filename. eg. /public_html/project/data/inventory.csv
  filter_filter_feed_data: Don’t want to upload all products? Apply filter here…
  filter_filter_feed_data_by_remove:
    Don’t want to remove all products? Apply filter
    here…
  filter_filter_shopify_data: Don’t want to update all products? Apply filter here…
  filter_filter_shopify_data_by_remove:
    Don’t want to remove all products? Apply filter
    here…
  add_product_to_existing_products:
    Adding products to existing products will require
    extra credits.
  current_time_zone: Current Time Zone
  no_products_to_remove: No products to remove
  static_value: Static value
  static_flag: Static flag
  labels: Labels
  force_override: Force replace
  url_prefix: URL prefix
  alt_mapping: Alt mapping
  clear_current_images: Clear current images
  ignore_clear_images_reset: Clear images option persists
  col_sep: Col sep
  default_image_url: Default image URL
  image_cdn: Modify Image URL
  case_convert: Case convert
  ignore_words: Ignore words
  auto_ignore_option_words: Auto ignore option values
  add_to_init_quantity: Add to init quantity
  only_deduct_quantity: Only deduct quantity
  rules_json: Rules json
  zero_qty: Zero quantity
  sum_quantities: Sum quantities
  quantity_delimiter: Quantity delimiter
  price_delimiter: Price delimiter
  price_round: Price round
  restrict_conditions: Restrict conditions
  prefix: Prefix
  option1_name: Option1 name
  existing_product_identifier: Existing product identifier
  fallback_to_price_if_less_than_price: Fallback to price if less than price
  force_override_compare_at_price: Force replace compare at price
  compare_at_pricing_conditions: Compare at pricing conditions
  compare_price_at_restrict_conditions: Compare price at restrict conditions
  eq_hide_value: Eq hide value
  eq_show_value: Eq show value
  override_publish: Replace publish
  add_tags: Add tags
  override_tags: Replace tags
  field_prefix: Field prefix
  weight_unit: Weight unit
  weight_formula: Weight formula
  weight_delimiter: Weight delimiter
  metafield_key: Metafield key
  metafield_namespace: Metafield namespace
  metafield_owner: Metafield owner
  metafield_type: Metafield Content Type
  base_on_quantity: Base on quantity
  override_to_deny: Replace to deny
  policy_deny_value: Policy deny value
  policy_continue_value: Policy continue value
  option2_name: Option2 name
  option3_name: Option3 name
  cost_pricing_conditions: Cost pricing conditions
  cost_restrict_conditions: Cost restrict conditions
  update_only_when_zero_qty:
    Ignore variants with available quantity (greater than
    zero)
  use_pk_to_match: Use pk to match
  label_product_key_separator: Product key separator
  notice: Notice
  help_text_quantity_add_product:
    "<0>All products added will have quantity set to
    the <1>default location</1>.</0>"
  choose: Customize
  endpoint: Endpoint (URL)
  shared_link: Shared link
  client_code: Customer code
  customer_code: Customer ID
  facility_code: Facility ID
  erply_tooltips: Your ERPLY account code(for example, 2881)
  compare_price_warning:
    Shopify not longer allows saving when Compare at Price less
    than or equal to Price. Please ensure Compare at Price is higher than Price, else
    it will auto set to blank
  google_drive_url_tooltips:
    eg. https://docs.google.com/… and https://drive.google.com/....
    are accepted
  price_click_for_more_settings: Add Markup (Price Conditions and more setting)
  product_id_click_for_more_settings:
    Add prefix/postfix or key separator to match
    products
  quantity_click_for_more_settings: Add quantity and other settings
  compare_price_at_click_for_more_settings: Add markup or set to blank and other settings
  cost_click_for_more_settings: Add markup to cost and other settings
  product_title_click_for_more_settings: Add case convert or ignore words
  body_html_click_for_more_settings: Add labels or choose to wrap in tag
  tags_click_for_more_settings: Choose to append/replace tags and other settings
  weight_click_for_more_settings: " Choose to edit the unit or convert weight"
  policy_click_for_more_settings: Manage out of stock product handling
  published_click_for_more_settings: Add value indicator to publish/unpublish
  option_click_for_more_settings: Add variant option name
  images_click_for_more_settings: Skip product with image and other settings
  metafield_click_for_more_settings: Define the metafield name and namespace
  replace_all: Replace all
  create_new_line: Create new line
  help_form_placeholder:
    Please provide a few SKU that is having the problem if have
    any.
  setting_updated: "%{profile_name}  settings updated."
  complete_fields: There are required fields, please complete fields in red
  troubleshoot_tooltip:
    Why? Require this credential for secure connection. This credential
    is encrypted when stored. Our support team will only access this for troubleshooting
    purpose.
  file_upload_can_done_later:
    " * File can also uploaded in dashboard. If file is
    compressed (.zip or .gz), file format must be specified below."
  email_tooltip: Why? We'll send you the exported inventory feed to this email
  auto_publish_products: Auto publish products
  auto_hide_products: Hide products from the online store
  auto_hide_and_archive_products: "<0>Hide products from <1>all sales channels</1></0>"
  archive_product: Archive products
  archive_products_wc: Hide products
  partial_match: Partial match
  query_params: Query Params
  add_param: Add Param
  confirm_delete: Confirm delete?
  remove_this_field: Remove this field
  how_to_retrieve_feed_file_from_supplier:
    Select how to retrieve the inventory feed
    file from your supplier/drop shipper.
  connection: Connection
  feed_info: Feed Info
  fill_in_info_can_read_inventory_feed_file:
    "Fill in respective feed info so syncX:
    Stock Sync can better read your inventory feed file and perform sync."
  step_1: Step 1/3
  step_2: Step 2/3
  step_3: Step 3/3
  how_to_setup_feed: How to setup feed?
  2min_tutorial: "2mins: Tutorial"
  how_product_updated_in_store:
    Manage how products are updated in the store. This
    will apply to all products in the feed
  how_product_updated_in_storeV2:
    "<0>Manage how products are updated in the store.
    Apply to all products in the feed. Will be ignored if <1>Track Inventory field
    mapped.</1></0>"
  how_product_updated_in_storeV2_WC:
    "<0>Manage how products are updated in the store.
    Apply to all products in the feed. Will be ignored if <1>Stock Management field
    mapped.</1></0>"
  choose_certain_product_to_add_filter:
    You can choose to include/exclude certain
    products from being added with filters.
  location: Location
  select_location_to_update_inventory: Select the location to update your inventory
  choose_certain_product_to_update_with_filter:
    You can choose to include/exclude
    certain products from being updated with filters.
  out_of_stock_handling: Out of stock strategy
  delete_description: Removes products permanently from your store
  archive_limitation:
    "<0>When enabled, only one variant of any product is archived.
    The rest are deleted.<1/>Note: Shopify Plus stores are currently not supported</0>"
  archive_description:
    "Manage to delete the products or archive the products in store.
    syncX: Stock Sync does not archive specific variants in a multi variant product."
  archive_or_delete_products: Archive or delete products
  warning: Warning
  how_to_setup_advanced: How to setup advanced?
  1min_tutorial: "1mins: Tutorial"
  advanced_settings: Advanced settings
  apply_filter: Apply filter
  remove_feed_caution_1:
    "<0>You have created a feed to <1>remove products</1> from
    the store. Each product removed will cost <5>$0.01 or less</5>. There is no UNDO
    process.</0>"
  remove_feed_caution_2:
    "<0>Please make sure to filter your feed data and Shopify
    data in the <1>Filters</1> of the created feed to remove the right products.</0>"
  bigcommerce_notice_1:
    "<0>You have created a feed to <2>update products</2> in your
    store. The products updated will be based on the information on the feed file
    provided. There is no UNDO process.</0>"
  bigcommerce_notice_2:
    "<0>Please make sure to filter feed data to update the right
    products.</0>"
  how_to_map_fields: How to map fields?
  auto_reset_quantity_warning_1:
    "<0><1>ALL unmatched</1> variants’ quantity to zero
    (out of stock) that was updated by syncX: Stock Sync before</0>"
  hide_unmatch_product_warning_1:
    "<0><0>ALL unmatched</0> variants’ quantity to zero
    (out of stock)</0>"
  under_vendors: " <0>under vendors <1><0>%{vendor_filter}</0></1></0>"
  do_not_apply: No action
  all_product_in_store:
    Make product visible for all filtered product in store (Step
    3)
  product_with_feed_file: Make product visible for store product match the feed data
  quick_guide: Quick guide
  feed_settings_modal_desc:
    Choose how you receive the inventory file from your supplier
    based on the connection methods available. You can specify the inventory file
    details according to the connection method.
  field_mapping: Field Mapping
  field_mapping_modal_desc:
    Add product identifier based on your inventory file and
    choose to update specific fields of your products
  advanced_settings_modal_desc:
    Select specific location of product to update and
    also manage how the products are updated in detail.
  add_field: Add field(s)
  field_required: Required field
  only_number_allowed: Only numbers allowed
  provide_valid_url: Please provide a valid url
  product_title: Product's title
  weight: Weight
  policy: Inventory policy (continue selling)
  fulfillment_service: Fulfillment Service
  quantity: Available quantity
  metafield: Custom data (metafield)
  option1: Variant option 1
  option2: Variant option 2
  option3: Variant option 3
  metafields_global_title_tag: SEO page title
  metafields_global_description_tag: SEO meta description
  country_code_of_origin: Country code of origin
  requires_shipping: Shipping (physical)
  harmonized_system_code: Harmonized system code
  images: Product images
  video_links: Product video links
  taxable: Charge tax
  variant_group: Variant group (optional)
  help_text_product_key_default:
    "Select a column from the feed file as the identifier
    on the left. The number of matches depends on this field. "
  help_text_images_default: "Publicly downloadable images only. "
  help_text_country_code_default:
    Only valid country code will save successfully.
    e.g. CN, US, UK, AU.
  help_text_harmonized_system_code_default:
    "Only valid HS code will save successfully.
    Without dot e.g. 640299. "
  help_text_deprecated:
    "This field is no longer supported by syncX: Stock Sync and
    does not perform any update. Please remove the field and proceed."
  pricing_conditions: "%{fieldName}  conditions (markdown/markup)"
  auto_tag_description:
    "Enabling syncX: Stock Sync auto tag will make it easier to
    view newly added products, improve the Undo and able to trace the newly added
    products better."
  auto_tag_label: Auto tagging
  auto_tag_help_text: When enabled, will add the auto tag and can't add own tags
  access_token: Access Token
  feed_is_not_working: The Feed is Not Working
  product_update_wrong: Products are Updated Wrongly
  feed_setup_incorrect: Feed Setup Incorrectly
  plan_and_billing_inquiry: Plan and Billing Inquiry
  general_inquiry: General Inquiry
  faster_schedule: Faster Schedule
  why_feed_not_working: Why is my feed not working?
  why_product_update_wrong: Why my products are updated wrongly?
  how_to_setup_correct: How to setup feed correctly?
  what_do_i_need_to_pay: What do I need to pay?
  faq: Frequently Asked Questions
  here_to_help:
    We’re here to help and answer any questions you might have. We look
    forward to hearing from you.
  store_name: Store name
  reason: Reason
  select_an_issue: Select an issue
  take_a_look_article_below:
    Please take a look at the article below for solution
    before sending the message.
  submit: Submit
  guidelines: "Guidelines:"
  column_only_define_once: Column name/index can only be defined once.
  include_and_exclude_multiple_keywords:
    Include/exclude multiple keywords with comma
    as separator.
  include_and_exclude_support_wildcard:
    Include/exclude keywords with wildcard support.
    eg, PRE*, *DO, *MIDDLE*
  note_for_filter:
    "Note: please keep single row for the same column name to avoid
    inconsistent result."
  column_name_or_index: Column Name / Index
  include_or: Include or
  remove_filter: Remove filter?
  show_more_list: Click here to show more list
  misc: Misc
  give_review: Your opinion matters to us!
  great: Great!
  not_good: Not good
  feed_refreshed: Feed refreshed
  credit_remaining: credits remaining
  here: here
  feed_process_started: Feed process started
  feed_process_cancelled: Feed process cancelled
  change_quantity_with_rules: Choose to change the quantity with the quantity rules
  pricing_condition_guide:
    "<0>Choose to <1>markup</1> the selling price with the
    price condition<3>here </3></0>"
  apply_filter_guide:
    "<0><0>Apply filters</0> to include/exclude certain collections,
    vendors, and much more<2>here </2></0>"
  dont_miss_a_thing: Don't miss a thing!
  last_updated: Last Updated
  last_added: Last Added
  last_removed: Last Removed
  total_variants: total variants
  products_before_added: Total variants (exclude last added)
  total_variants_scanned: Total Variants Scanned
  last_processed: Last processed
  no_feed_activity: No feed activity at the moment
  learn_hidden_features: "Learn syncX: Stock Sync's hidden features"
  tell_me_more: Tell me more
  schedule_is: Schedule is
  turn_on_here: Turn on here
  current_file: "Current file:"
  keep_window_open_until_complete: Keep this window open until upload is complete
  purchase_credit: "<0>Purchase <1>credits</1> <3></3></0>"
  feed_details: Feed details
  ok: Ok
  quick_guided: Quick Guide
  batch_inventory_update: Batch Inventory Update
  fixed_columns: "(Fixed columns)"
  clone_feed: "<0><0><0>%{profile_name}</0></0> created</0>"
  need_customise:
    "<0>Need customization? <2>Upgrade your plan</2> to unleash the
    full feature of syncX: Stock Sync.</0>"
  affiliate_description:
    This app is free to test while the store is under <2>development</2>.
    Once the store switches over to a paid Shopify plan you will be <5>downgraded
    to free plan</5> and need to subscribe to the plan to continue using this app.
  loading_page: Loading...
  sort_by: Sort by
  upload_here: Upload File Here
  feed_is_created: Feed is being created
  feed_process_take_long:
    Feed processing is taking longer than expected. Please click
    below and provide your email to receive a notification when the process is complete.
  exceeded_feed_limit_of_plan:
    You have exceeded the feed limit of your current plan.
    Enable feed(s) below
  upgrade_here: Upgrade Here
  choose_an_action: Choose an action on how to import products from supplier
  select_update: "<0><0>Update</0> existing products in store</0>"
  select_import: "<0><0>Add</0> new products to store</0>"
  select_remove: "<0><0>Remove</0> discontinued products from store</0>"
  selected_import_desc_1: "<0>Import <1>NEW</1> products from any formatted files</0>"
  selected_import_desc_2: Variant grouping is configurable
  selected_import_desc_3: Import product images that are publically hosted
  selected_import_desc_4: Credits will be returned when the process undo
  how_much_cost: How much does it cost me?
  how_much_cost_import_1: "Free 10 syncX: Stock Sync credits for first time users"
  how_much_cost_import_2: "Purchase syncX: Stock Sync credits to remove variants"
  how_much_cost_import_3: Each variant cost 1 credit which is equal to $0.01
  how_much_cost_import_example:
    "Example: To remove 500 variants from your store,
    would require 500 syncX: Stock Sync credit. You will need to purchase for $5."
  continue: Continue
  purchase_credits: "<0>Purchase <1>syncX: Stock Sync Credits</1> <3></3></0>"
  remove_feed_copy: Remove Product
  selected_remove_desc_1: Remove discontinued products
  selected_remove_desc_2: Remove process can't be reverted
  stock_sync_credit_remaining: "syncX: Stock Sync Credits remaining"
  monthly_credit_remaining: Monthly Credits remaining
  feeds_used: feeds used
  free: Free
  full_access_ended: Full Access ended
  how_much_cost_update_example:
    "Example: There are 15,000 variants in your store
    and have feeds from 4 suppliers, then subscribe to ProX plan ($15 monthly). All
    feed can be scheduled to run every 6 hours."
  how_much_cost_update_1: 14-days full access to all features available
  how_much_cost_update_2: Monthly subscription required
  how_much_cost_update_3:
    The subscription is limited by the variant(s), the number
    of update feed and schedule frequency
  selected_update_desc_1:
    "<0>Works right away by <2>matching SKU, Barcode or others</2>
    (no import needed)</0>"
  selected_update_desc_2: Keep available quantity level same as supplier or warehouse
  selected_update_desc_3: Adjust product pricing with flexible price condition
  selected_update_desc_4:
    "<0><0>60+ connection types</0> and <3>8 file formats</3>
    supported</0>"
  full_access: Full Access
  subscription: Subscription
  stock_sync_credit: "syncX: Stock Sync credit"
  update_with_subscription_plan: Update Products with Subscription Plan
  monthly: Monthly
  annually: Annually
  full_access_subscribing_ended:
    "<0>The <1>Full Access</1> plan that you are subscribing
    has <3>ended</3></0>"
  plan: plan
  current_subscribed: Currently subscribed to
  current_subscribed_free_plan: "<0>Currently subscribed to <1>Free</1> plan </0>"
  annual: Annual
  the: The
  plan_subscribing_expired: "<0>plan that you are subscribing has <1>Expired</1></0>"
  number_of_update_feed_used: Update feeds
  price_review_1:
    "<0>... the app definitely delivers what it promises at a <2>VERY
    Reasonable Price</2> ...</0>"
  price_review_2:
    "<0> This app makes my business life <3>10000x's easier!</3> ...
    </0>"
  more_reviews: more reviews
  year: year
  month: month
  update_product_feeds: update product feeds
  update_product_feed: update product feed
  upgrade: Upgrade
  variants: variants
  subscribe_free: Subscribe Free
  free_plan_confirmation:
    "<0><0>By subscribing to the free plan, you will not be
    able</0><1>to create any update feed. Are you sure?</1></0>"
  everything: Everything
  plus: plus
  monthly_credits: Monthly credits
  daily_schedules: daily schedules
  hr_min: hr min
  how_to_know_which_monthly_subscription:
    How do I know which monthly subscription
    is suitable for my store?
  select_plan_based_on_variant_in_store:
    Please select the plan based on your total
    variant in store. Each monthly plans also has different feed limit and schedule
    frequency. No Import credit will be given for these plans.
  frequently_asked_questions: Frequently Asked Questions
  how_to_pause_for_later_use:
    How do I pause / suspend / terminate / cancel for later
    usage?
  can_downgrade_to_free_plan:
    You can choose to downgrade to free plan to suspend
    for later use. All settings will remain the same when reinstall the app.
  cant_find_right_plan: Can't find the right plan?
  let_us_figure_out:
    Let us help you figure it out. Or we will offer a custom plan
    for your store need
  add_remove_with_credit: "Add/Remove Products with syncX: Stock Sync Credit"
  credit_how_to_cost:
    Every new product successfully added or removed will cost one
    credit (only $0.01 or less)
  would_like_to_buy: I would like to buy
  other_value: Other value
  stock_sync_credits: Credit(s)
  stock_sync_monthly_credits: Monthly credit(s)
  when_credits_reloaded: When will the credits be reloaded every month?
  credits_reloaded:
    The monthly credit will be reloaded to you on the 30th billing
    day from the day of your subscription
  credit_pushed_forward: Can the monthly credit be pushed forward?
  unused_credit:
    Yes, the unused monthly credit will be pushed forward and will not
    expire.
  which_plan_subscribe_for_credit:
    I wish to add new products to my store. Which plan
    do I subscribe?
  buy_credit_for_import_and_remove:
    "You don't have to subscribe to any monthly plan
    to add new products. You need to purchase syncX: Stock Sync credit for adding
    or removing products. You can add or remove 1 product per credit."
  subscribe_monthly_plan_cannot_do_import:
    I have subscribed to the monthly plan but
    I still cannot import any products.
  buy_credit_to_proceed: "You don't have to subscribe to any monthly plan to add
    new products. You need to purchase syncX: Stock Sync credit for adding products."
  get_back_credit_if_undo: Do I get back my credit if I undo the Add Products?
  can_get_back_credit:
    Yes, the credits you used on the latest Add Products process
    will be returned to you. Please note, you cannot UNDO removed products.
  buy_credit: Buy Credit
  check_added_products: Check added products
  undo_on: Undo on
  connect_product_with_app:
    "Connect your product inventory or feed data from your
    suppliers, warehouses or others with syncX: Stock Sync"
  keep_over_number_of_inventory:
    "<0>Keep over <1>32 millions</1> inventory levels
    accurately everyday</0>"
  setup_feed_never_been_easier: Setting up feed has never been easier
  different_type_of_connection_method: Different type of connection methods
  be_quantity_rules_expert: Be a Quantity Rules expert
  markup_price_with_price_condition: Markup Price with Price Conditions
  schedule_relax_and_enjoy: Schedule, Sit Back and Enjoy
  choose_type_of_feed:
    "Choose the type of feed: Add/remove products or update existing
    products in store. Connect to supplier/drop shipper using templates provided."
  multiple_connection_method_available:
    80+ connection methods are available to retrieve
    inventory files. All file format is supported, including compressed file.
  use_quantity_rule_reduce_overselling:
    Use the Quantity Rules to reduce overselling.
    You can also add/deduct product instead of overriding it. Also, read quantity
    in words.
  add_formula_to_price_condition:
    Markup your price with the Price Conditions. Add
    formulas to pricing and allows multiple Price Condition so you can customize up
    to specific products.
  automated_sync_by_scheduling:
    Automate sync by scheduling. Choose to run it hourly,
    daily or on weekends only and much more.
  variants_less_than_quantity: variants has less than quantity
  out_of_stock_variants: Low stock
  partial_complete: Some product(s) were not saved, see above
  partial_fail: Sync process was unsuccessful, see above
  having_trouble_setting_up: Are you sure to delete feed?
  let_us_know_we_ready_to_help: Please let us know, the support team is ready to help.
  import_remove_feed_tips:
    Deleting the feed does not revert the changes made. Please
    make sure to UNDO, if want to revert before delete feed.
  delete_anyway: Yes, delete
  previous: Previous
  start_new: Start New
  start_quickly_with_list_of_supplier:
    Get started quickly with Global Craft, Roma
    Costume, MMA Silver and more.
  prepared_template_to_start_quickly:
    We have prepared these templates to help you
    get started quickly.
  remove_discontinued_sold_out_quickly:
    Remove discontinued, back ordered, sold out
    products quickly.
  template_provided: from your supplier with templates provided
  choose_to: Choose to
  add_product: add product
  update_product: update product
  remove_product: remove product
  disable_scheduler: Disable scheduler?
  disable_scheduler_for:
    Disabling the scheduler will pause any auto update activity
    for
  contact_support_to_enable_option: "<0><0>Contact support</0> to enable this option</0>"
  uptracker_description:
    Automatically fulfill orders, update tracking info and PayPal
    tracking info. Free first 3000 orders
  exportible_description:
    Export customized order reports to vendors, suppliers, logistics
    partners in real-time automatically
  quick_scan_description:
    Simple way to add and deduct inventory quantity in store.
    Transfer stock between location.
  neon_description:
    Amplifies customer confidence by displaying product availability
    and sold count
  free_install: Free to install
  free_14_days_trial: 14 days free trial
  view_more_news_feed: view more
  announcement: Announcement
  list_of_country_codes: List of country codes
  template_provided_for: Template provided for
  visit_website: Visit website
  special_offer: Special Offer
  free_access_to_all_features: Free Access to all features
  with_full_access_to_all_features: with full access to all features
  your_annual_plan_expired: Your Annual plan has expired
  last_upgraded_at: Last upgraded at
  kindly: Kindly
  to_continue: to continue
  only_run_from: Only run from
  to: to
  the_current_plan: The current plan
  is_min: is min
  upgrade_plan_for_faster_schedule: Please upgrade plan for faster schedule
  contact_support_to_request_faster_schedule:
    Please contact support to request for
    a faster schedule than 24 hours
  formula_must_operand_followed_by_number:
    formula must be an operand followed by
    number
  and: and
  condition_cant_be_blank: condition can't be blank
  condition_must_consists_price_range: condition must consists of a price range
  variant_sku: "(Variant SKU)"
  collection_field_map_to_tag_field:
    "DEPRECATED: Please map to Tags field and use
    Automate Collection to group products into the repective collections"
  find: Find
  help_find_words:
    "<0>Find multiple match any words or phrases, separated by a comma.
    eg <2>word1,word2</2> To find any part of the word, include *. Also supports regular
    expressions.</0>"
  help_replace_words:
    "<0>Replace multiple words or phrases, separated by comma. eg
    <1>word1,word2</1>. (Auto matched with Find)</0>"
  replace: Replace with
  support_multiple_ignore_words_help_text:
    Support multiple ignored words, separated
    by a comma. Also support regular expressions
  update_from_shopify: Update from Shopify
  no_file_uploaded: No file uploaded
  quantity_become_zero_in_import:
    "<0>All products added will have <2>quantity 0</2>.
    Create update product feed to update the right quantity of the products added!</0>"
  qbp: Quality Bicycle Products
  qbp_key: QBP API Key
  auto_publish_or_hide: Product visibility
  track_quantity_need_to_be_enabled: "<0>Track inventory needs to be <2>enabled</2></0>"
  auto_hide_product_if_variant_is_zero:
    "<0>Auto hide products when all variant is
    0 and continue selling when out of stock is <1>disabled</1>.</0>"
  auto_publish_product_if_quantity_more_than_zero:
    Auto publish products if any one
    of variant quantity more than zero.
  product_change_history: Product's Change History
  product_info: Product Info
  variant_info: Variant Info
  product_variant_info: Product Variant Info
  availability: Availability
  latest_fourteen_days_only: Display changes from the last 14 days.
  timestamp: Timestamp / Feed Name
  updated: Updated
  beta: Beta
  very_slow_update: Very slow update
  ready: Ready
  queuing: Queuing
  processing: Processing
  time_taken_to_process: Time taken to process
  download: Download
  schedule: Schedule
  either_include_or_exclude_is_required: Either include or exclude is required
  column_name_is_required: Column Name is required
  enabled: enabled
  change_current_time_zone: To change the current time zone
  administrator: Administrator
  shop_manager: Shop manager
  access: Access
  do_nothing_to_my_file: Do nothing to my file
  append_date_to_my_file: Append date to my file (<date_time>_file_name)
  rename_my_file: Rename/Move my file
  delete_my_file: Delete my file
  choose_method_to_import: Choose from over 20+ methods to import
  choose_method_to_update: Choose from over 20+ methods to update
  choose_method_to_remove: Choose a method to remove
  import_to: Import to
  update_to: Update to
  remove_from: Remove from
  hide_unmatch_product_after_process_warning_1:
    "<0><0><0>%{remainingVariant}</0></0>
    variants’ quantity will be reset as Zero (out of stock)</0>"
  primary_location: Primary Location
  Auto: Auto
  HTML Table: HTML table
  decimal_point: Decimal point . (e.g. US, Australia, Hong Kong)
  auto_currency_delim: Auto
  comma: Comma , (e.g. Germany, France)
  Use the price round on Price field as default:
    Use the price round on Price field
    as default
  no change: no change
  kg: kg
  g: g
  lb: lb
  oz: oz
  g to lb: g to lb
  g to kg: g to kg
  kg to lb: kg to lb
  kg to g: kg to g
  lb to g: lb to g
  lb to kg: lb to kg
  No change: No change
  All lower case, e.g. vintage t-shirt: All lower case, e.g. vintage t-shirt
  All upper case, e.g. VINTAGE T-SHIRT: All upper case, e.g. VINTAGE T-SHIRT
  Titleize case, e.g. Vintage T Shirt: Titleize case, e.g. Vintage T Shirt
  Product: Product
  Variant: Variant
  String: String
  Integer: Integer
  product_tags: Product tags
  None: None
  "<p> paragraph": "<p> paragraph"
  "<li> list item": "<ul> unordered list"
  "</br> line break": "</br> line break"
  availability_description: Availability Description
  maximum_buy_credit: Maximum can purchase 10,000 at a time
  save_cost: Save
  total_variants_have: variants
  batch_inventory_update_from_plan: Batch Inventory Update
  affiliate_new_description:
    "<0>This app is free to test while the store is under
    <2>development</2><3></3></0>"
  affiliate_remaining_description:
    Once the store switches over to a paid Shopify
    plan you will be downgraded to free plan and need to subscribe to the plan to
    continue using this app.
  file_invalid_support_link: Invalid file. Please contact %{link}
  exceed_api_limit: API limit exceeded
  message_placeholder: SKU 12345 not updated in Feed 5
  upload_file: Upload file
  disable_feed: Feed is disabled
  mysale_api_out: MySale API
  bearer_token: Bearer Token
  bearer_token_body_url: Bearer Token Source URL
  bearer_token_body_params: Body Parameters
  cj: CJ Dropshipping
  cj_key: CJ API Key
  cj_category_id: CJ Category ID
  archive: Archive
  archive_help_text:
    "When enabled, single variant product is archived. Multi variant
    product will be ignored. Note: Shopify Plus stores are not supported"
  partial_match_help_text:
    When enabled, multi variant products are archived even
    if only one variant is matched to the product
  how_product_removed_in_store:
    Manage how products are removed in the store. This
    will apply to all products in the feed
  choose_certain_product_to_add_filter_for_avoid_remove:
    You can choose to include/exclude
    certain products from being removed with filters.
  filter_note_for_remove:
    "NOTE: There is no UNDO process. Please make sure to add
    filter by feed data and/or filter by Shopify products to avoid removing active
    products"
  dont_update_based_on_inventory_level:
    Don’t update availability according to inventory
    level
  bigcommerce_retain_availability_help_text:
    "By default, syncX: Stock Sync will update
    the availability according to the inventory level. Enabling this option will not
    update the availability and remain unchanged."
  brightpoint_soap: Brightpoint (SOAP)
  brightpoint_customer_no: CustomerNo
  brightpoint_instance: Instance
  brightpoint_site: Site
  label_hs_code_suffix: HS code prefix/postfix
  help_text_hs_code_suffix:
    Shopify only accepts 6 to 13-digits Harmonized System
    Code. To match codes with prefix "000", define as "000". For codes with postfix
    "0100", define as "0100$"
  note_archive_description:
    "NOTE: Archiving products does not require syncX: Stock
    Sync credit"
  captivity: Captivity
  view_product_added: View added products
  view_product_with_tag_added: View product(s) with tag added in Shopify admin
  bigcommerce_filters: Filtering Bigcommerce Products
  brand_id: Brand ID
  ss_active_wear: SS Active Wear
  find_placeholder: "*man will find woman, roman, gentleman"
  hlc_country: HLC Country
  width: Width
  height: Height
  depth: Depth
  sale_price: Sale Price
  shipping_cost: Fixed Cost Shipping Price
  retail_price: Retail Price (MSRP)
  free_shipping: Free Shipping
  label_with_free_shipping: With free shipping value
  label_without_free_shipping: Without free shipping value
  help_text_with_free_shipping: "It will enable 'Free Shipping' depending on the
    value of the source feed file. Example: 1, true, TRUE"
  help_text_without_free_shipping: "It will disable 'Free Shipping' depending on
    the value of the source feed file. Example: 0, false, FALSE"
  inventory_level: Inventory level
  sysco: Sysco
  microsoft_sharepoint: Microsoft SharePoint
  recommend_after_process:
    Recommend using Append Date or Rename File for better debugging
    process if any error occurs to retrieve the file.
  ensure_use_column_index:
    For exporting, please ensure all fields mapped by column
    index (e.g. 1, 4, 5) instead of column name
  update_from_store: Update from %{provider}
  add_field_for_import: Add field (e.g. Description, Image)
  add_field_for_update: Add field (e.g. Available Quantity, Price)
  source_store_uninstalled:
    "Shopify store unaccessible. Please ensure syncX: Stock
    Sync remain installed on source store."
  finish: Finish
  back_to_home_changes_not_saved: Back to home. Changes wont be saved.
  load_more: Load More
  change_logs: Change logs
  change_history: Change history
  view_more_products: View more products
  products_lowest_price_start_at: products lowest price starting at
  image: Image
  title_and_sku: Title / SKU
  remark: Remark
  recommendation: Recommendation
  subscription_key: Subscription Key
  location_inactive_or_deleted:
    The selected inventory location is inactive or missing.
    Please reselect an active location in the Available Quantity Setting.
  store_lang: Store Language
  woocommerce_filters: Filtering Woocommerce Products
  woocommerce_note_filters:
    To include certain products by providing multiple category
    name, separated by comma.
  category_placeholder: "10,23,24"
  preorder_message: Preorder Message
  please_ensure_tls_enabled:
    Go to Feed Settings > change connection method to FTP
    (TLS enabled) and try again.
  ital_trading: Ital Trading
  ital_trading_id: Ital Trading ID
  ital_trading_key: Ital Trading Key
  select_your_location: Select a new location as previous location is inactive
  axiz: Axiz
  axiz_client_id: Axiz Client ID
  axiz_client_secret: Axiz Client Secret
  start_now: Start now
  preorder_release_date: Pre-Order Release Date
  label_date_format: Feed Date Format
  western_power_sports: Western Power Sports
  apply_filters_desc:
    Apply filters to include/exclude certain products from the sync.
    Filter by SKU, collection, vendors and more.
  logsta: Logsta
  corrupted_xlsx: Corrupted Xlsx file. Please resave and try again.
  blank_xlsx: The Sheet has no data. Please ensure the Sheet name is correct.
  unknown_soap_operation:
    Ensure that the SOAP operation is separated by an underscore.
    E.g get_all_items
  review_description:
    We work super hard to serve you better and would like to know
    how would you rate our app?
  dismiss: Dismiss
  help_text_tags_support_multiple: Support multiple tag fields, separated by a comma.
  woocommerce_mismatched_protocol:
    Please contact support to change the store protocol
    and try again.
  queuing_reason:
    Feed is queuing and it might take some time depending on the server
    status.
  enable_delete_mode:
    "<0>Enabling this option will only hide products from online
    store instead of hide products from <1>all sales channels (archive)</1></0>"
  wix_limited_source_error:
    "<0>14-days free trial has ended. Please upgrade to create
    feeds and run the processes.<3></3></0>"
  help_text_date_format:
    "<0>Insert a date format which fits the date value in the
    feed. E.g. %d %B %Y<1><0></0> Learn More</1></0>"
  chattanooga_shooting: Chatanooga Shooting
  remove_preoder_on_date: Automatically remove preoder status on specified date
  label_remove_preorder: Auto remove preorder status on specified date
  help_text_remove_preorder:
    When enabled, the preorder status will be automatically
    removed on the preorder release date
  mpn: Manufacturer Part Number
  help_text_add_to_init_quantity:
    Enabling this option will add/deduct quantity instead
    of overwriting the quantity from the feed.
  help_text_label_zero_qty:
    Enabling this option set the available quantity to non-negative
    instead of having negative quantity.
  shopify_api_key: Shopify API Key
  shopify_api_secret: Shopify API Password
  shopify_feed: Shopify Feed via Private App
  learn_more_from: Learn more about %{field}
  product_id: Product identifier
  learn_more_about_metafield: Learn more about Metafield
  shopify_domain: Shopify Domain
  free_15_days_trial: 15-day free trial
  notification_description: Free App - Get Premium Popup and Notifications
  connection_method_how_it_work: How does %{field} works
  brand_name: Brand name
  import_sort_label: Auto sort
  help_text_import_sort: Sort the variant group before importing
  received_invalid_attachment: Email received but attachment is invalid or missing
  metadata_1: Metadata 1
  metadata_2: Metadata 2
  metadata_3: Metadata 3
  metadata_key: Custom Field key
  label_metadata_key: Custom Field key
  help_text_metadata_key: Key used to identify which product metadata value to update
  label_create_key_if_not_found: Auto create a new metadata if not found
  help_text_create_key_if_not_found:
    Enabling this would create a new metadata record
    based on the metadata key if no such key is found
  generate_woocommerce_keys_title:
    "Your current API does not have the necessary permission
    for syncX: Stock Sync to work."
  generate_woocommerce_keys_step_one: Login to WooCommerce store admin
  generate_woocommerce_keys_step_two: Navigate to Settings > Advanced > Rest API
  generate_woocommerce_keys_step_three:
    Create a new key (Ensure permissions are set
    to Read/Write)
  platform_500_error:
    The ecommerce platform having error, please contact our support
    team.
  shopify_feed_location:
    Provide a valid location ID in the store, e.g. 4211111111.
    Specifying the location, will only be able to update the product quantity. The
    mapping are, SKU = inventory_item_id and quantity = inventory_quantity.
  get_ready_to_select_feed_type:
    Get ready for a faster and much easier experience
    managing inventory
  label_custom_field_name: Custom field name
  custom_field: Custom Field
  woocommerce_internal_server_error:
    "<0>There has been a critical error on this store.
    Learn more about troubleshooting WordPress <1>here </1></0>"
  tme: TME Electronic
  bigcommerce_redirect_title: "Optimize syncX: Stock Sync"
  bigcommerce_redirect_tooltip: Use and bookmark app.stock-sync.com for optimal experience
  product_url: Product URL
  import_auto_set_to_visible:
    Auto set newly added products to be visible. By default,
    new products are not visible in storefront.
  store_products_missing_sku:
    "All store products do not have SKU. Please ensure that
    the SKU is present for syncX: Stock Sync to function optimally"
  set_date_if_zero_qty:
    Only products with zero quantity will be set to "Preorder"
    status
  label_set_date_if_zero_qty: Set date when quantity is 0
  help_text_set_date_if_zero_qty:
    Only set "Preorder Date" if quantity in feed file
    is zero
  label_custom_preorder_message: Custom pre-order message
  help_text_custom_preorder_message:
    Supports %%DATE%% which will be substituted for
    the release date. E.g. "Preorder date - %%DATE%%"
  dropbox_api: Dropbox (with password)
  dropbox_access_denied:
    Dropbox access denied. Please ensure shared link and password
    is correct
  dropbox_api_url_tooltips: eg. https://www.dropbox.com/sh/...
  link_password: Link password
  fragrancenet: Fragrance Net
  fullfashion: Full Fashion
  zoho_sheet: Zoho Sheet
  zoho_public_sheet: Public Zoho Sheet URL
  zoho_url_tooltips: eg. https://docs.zohopublic.com/sheet/open/...
  zoho_unauthorized: Please ensure your Zoho Sheet is a public file
  partner: Partner
  tada_description: Increase your Shopify Subscribers and Sales with Shopify Pop-ups
  free_7_days_trial: 7 days free trial
  free_30_days_trial: 30 days free trial
  kaching_description:
    Create volume discount bundles on your product page to increase
    average order value and sell more
  planet_description:
    Boost your sales with unlimited Bundles, Quantity breaks, BOGO,
    Free Gifts, Bulk discounts and more!
  more_app_by_stock_sync: "More apps by syncX: Stock Sync"
  currency_converter: Currency converter
  default_currency: Currency in Store
  new_currency: New Currency
  support_wildcard_date: Support dynamic dates. For example, %Y_%m_%d
  one_on_one_wholesale: 1on1 Wholesale
  one_style: 1Style
  alterego_csv: Alterego (CSV)
  alterego_xml: Alterego (XML)
  azure_green: AzureGreen
  big_toys: Big Toys USA
  clf_distribution: CLF Distribution
  daniel_smart_manufacturing: Daniel Smart Manufacturing
  daisy_corset: Daisy Corset
  honeys_place: Honey's Place
  kerusso: Kerusso
  kleerance: Kleerance
  matterhorn: Matterhorn
  maropost: Maropost (Neto)
  mclabels: McLabels
  wine_logistix: Wine Logistix
  diamond_sky: Diamond Sky
  feed4akid: 4akid
  feed4apet: 4apet
  fast_furnishings: Fast Furnishings
  fan_mats: Fan Mats
  elegant_moments: Elegant Moments Lingerie
  ingram_content_ftp: Ingram Content Ftp
  puckator_dropship: Puckator Dropship
  petdropshipper: Pet Dropshipper
  userid: User ID
  uk_shopping_mall: UK Shopping Mall
  bts_wholesaler: BTS Wholesaler
  happy_nest: Happy Nest
  nae_vegan: Nae Vegan
  skybound: SkyBound USA
  talksky: Talksky
  triple_xcessories: Triple Xcessories
  new_temptations: New Temptations (In Stock)
  new_temptations_clearance: New Temptations (Clearance)
  windsor: Windsor
  twhouse: TWHouse
  transamerican: TransAmerican AutoParts
  prv: PRV FTP
  eldorado: Eldorado FTP
  roma_costume: Roma Costume
  smiffys: Smiffy's Trade
  smiffys_api: Smiffy's Trade (API)
  sunkys: Sunkys
  copper_bay_digital: Copper Bay Digital
  intcomex_api_key: API Key
  intcomex_user_access_key: User's Access Key
  fulfillrite: Fulfillrite
  fragrance_x: FragranceX
  allure_lingerie: Allure Lingerie
  green_dropship: GreenDropShip
  dropship_clothes: Dropship Clothes
  dropshipping_b2b: Dropshipping B2B
  strawberry_net: Strawberry Net
  email_run_on_min_hour: Every %{minHour} hours
  file_upload: File Upload
  completed: Completed
  load_feed_and_products: Load feed & products
  scan_for_matching: Scan for matching
  performing_update: Performing update
  dropshipzone: Dropshipzone
  failed: Failed
  errbit: Please check the settings or contact support
  product_code: Product Code
  treasure_house: Treasure House Co.
  retail_edge: RetailEdge
  maxevan: Maxevan
  horizon_hobby: Horizon Hobby
  horizon_hobby_max_pages: Page Limit
  tsigaridas_books: TsigaridasBooks
  tsigaridasbooks_interval_hours: Interval Hours
  encompass: Encompass
  customer_number: Customer Number
  customer_password: Customer Password
  access_code: Access Code
  access_password: Access Password
  clerk_id: Clerk ID
  clerk_password: Clerk Password
  location_id: Location ID
  fn_name: Function Name
  shopify_api_connection_failed:
    Shopify API temporarily having issue. Please try
    again.
  brand: Brand
  reset_field_mapping: Reset field mapping
  reset_field_mapping_confirmation:
    Reset field mapping and all extra settings of
    fields to the original template.
  pid: Portal ID (PID)
  lid: Language ID (LID)
  site_id: Site ID
  lang_id: Language ID
  currency_code: Currency Code
  proceed: Proceed
  are_you_sure_reset_mapping: Are you sure?
  connection_method_cant_be_changed:
    Connection method can't be changed as it is provided
    by template
  western_fashion: Western Fashion
  football_souvenir: Football Souvenir
  tarsus_distribution: Tarsus Distribution
  update_existing_products: Update existing products
  add_or_remove_products: Add/remove products
  billing_cycle: "Billing cycle:"
  bewicked: Bewicked
  amrod: Amrod
  asi: ASI
  supplier_ids: Supplier IDs
  convert_line_break: Convert newline to line break
  label_convert_line_break: Convert newline('\n') to line break (<br />)
  wont_double_charged: All plans are prorated and you won't be double charged
  reduce_number_variant: "<0>Reduce number of variant count update using <1>filters</1>.</0>"
  google_drive_v3: Google Drive V3
  template_suffix: Theme Template
  undetermined: Undetermined
  category: Category
  combo: Combo (Update + Credit)
  total_variant_used: "%{currentVariant} of %{totalVariant}"
  update_feed_used: "%{currentUpdateFeedUsed} of %{totalUpdateFeed} used"
  monthly_credit_used: "%{currentMonthlyCreditUsed} of %{totalMonthlyCredit} left"
  auto_mapping_help_text:
    Example, enter Option Name in Field mapping below will auto
    assign Value accordingly.
  category_ids: Category IDs
  sex_toy_distributing: Sex Toy Distributing
  apply_same_product_id_for_remove:
    Allow remove of duplicate products / variants
    with the same product identifier
  feed_name: Feed name
  message_with_provide_sample: Message (provide a few SKU/Barcode if have any)
  update_mapped_field:
    "syncX: Stock Sync only update mapped fields. It do not make
    any changes to unmapped fields."
  configure_filters_snappy:
    Apply filters to only include/exclude certain products
    to be shared with buyers
  wix_store: "Wix Store (required installation of syncX: Stock Sync)"
  generate_woocommerce_keys_step_four:
    Ensure Permalinks is NOT set to 'Plain' (Settings
    > Permalinks)
  ribbon: Ribbon
  discount_amount: Discount
  discount_type: Discount Type
  label_discount_type: Set discount type as percentage.
  help_text_discount_type_percentage:
    Enabling this option will set the discount type
    as percentage instead of amount.
  help_text_for_cost_and_quantity:
    The column name or column index. Sum multiple %{mapping}
    field with comma separated
  select_category: Please select a category
  wix_filters: Filtering Wix Products
  wix_note_filters:
    To include certain products by providing multiple brand ID, separated
    by comma.
  dealer_code: Dealer Code
  parts_unlimited: Parts Unlimited
  auth_type: Authorization Type
  sanmar: SanMar FTP
  ingram_content: Ingram Content
  query: Query
  email_with_schedule_and_next_run_time: Run %{schedule}
  undo_process_limit_days: Undo process is limited to 14 days
  monthly_plan_required: Monthly plan required
  credit_required: Credit required
  write_to_zoho_inventory: Write to Zoho Inventory
  backblaze_b2: Backblaze S3
  help_text_auto_reset_quantity:
    Recommended to use filter so DOES NOT affect other
    unrelated products
  help_text_hide_unmatch_products:
    "Only affect products that have been updated using
    syncX: Stock Sync before"
  accept: Accept
  attention: Attention!
  process_not_reversible:
    "<0>Completed process is <1>NOT REVERSIBLE</1>. You are
    advised to download export product as backup prior to a running process.</0>"
  help_text_ignore_option_words: eg. Branded Shoe B̶l̶a̶c̶k̶
  recommend_product_filter:
    "<0>Recommend to apply <2>Shopify Product Filter</2> to
    avoid reset quantity to zero for all unrelated products</0>"
  find_regular_ex_error:
    Find words fail regular expression check. Please contact
    our support team.
  key_not_found: Key not found. Please check key name.
  api_read_inventory_needed:
    Shopify API access requires merchant approval for read_inventory
    scope
  xml_memory_fail: XML memory fail (most likely XML too huge)
  show_as_tag: Show as tag(s)
  uppromote_description:
    Complete, easy referral & affiliate program app, trusted
    by top brands & experts for a revenue boost
  free_plan_available: Free plan available
  billiet: Billiet
  change_formula_help_text: E.g. Change *1 to *1.2 to markup all product by 20%
  growave_description:
    "Growave replaces several apps: loyalty, referrals, rewards,
    gift cards, VIP tiers, reviews, wishlist"
  warning_zero_over_10:
    "Warning - update halt: detected over 10% inventory set to
    zero."
  warning_zero_over_20:
    "Warning - update halt: detected over 20% inventory set to
    zero."
  warning_zero_over_30:
    "Warning - update halt: detected over 30% inventory set to
    zero."
  warning_zero_over_40:
    "Warning - update halt: detected over 40% inventory set to
    zero."
  warning_zero_over_50:
    "Warning - update halt: detected over 50% inventory set to
    zero."
  warning_zero_over_60:
    "Warning - update halt: detected over 60% inventory set to
    zero."
  warning_zero_over_70:
    "Warning - update halt: detected over 70% inventory set to
    zero."
  warning_zero_over_80:
    "Warning - update halt: detected over 80% inventory set to
    zero."
  warning_zero_over_90:
    "Warning - update halt: detected over 90% inventory set to
    zero."
  warning_zero_over_100:
    "Warning - update halt: detected over 100% inventory set
    to zero."
  warning_quantity_update:
    Halt process when %{quantity_percent}% of products' quantity
    set to zero. Once the threshold selected above is reached, the update stops and
    a warning message is displayed.
  zero_qty_update_alert: Quantity zero alert
  cannot_remove_feed_while_processing:
    Feed can't be deleted while processing. Please
    stop the process to proceed.
  remove_product_when_all_locations_nil:
    Remove product / variants only if the quantity
    is zero in all locations
  daily_summary: Daily summary email
  dicker_data: Dicker Data
  subscription_type: Subscription Type
  country_api: Country API
  customer_token: Customer Token
  mustek: Mustek
  sex_toy_distributing_images: Sex Toy Distributing (Images)
  delay: Delay
  label_force_override_description:
    Set to blank when value in feed is blank (value
    zero is not blank)
  update_only_if_nil: Only update if current description is empty
  label_skip_zero_blank_labels: Hide label when value is 0
  how_to_get_api_token: How to get API token
  morris_costumes: Morris Costumes
  stricker_europe: Stricker Europe
  hill_interiors: Hill Interiors Feed
  need_separator_for_namespace_and_key:
    Need to use one period (.) to separate the
    namespace and key
  only_a_separator_for_namespace_and_key:
    Allow one period (.) to separate the namespace
    and key
  label_create_custom_field_if_not_found: Auto create a new custom field if not found
  help_text_create_custom_field_if_not_found:
    Enabling this would create a new custom
    field based on the custom field key if no such key is found
  status: Status
  pagefly_description:
    Craft stunning web pages without code. More advanced customization
    to convert your visitors.
  makeanoffer_description:
    Easily add make an offer and pay what you want buttons
    to products. Get Offers. Sell More Products.
  rivyo_description:
    Collect and showcase product reviews & star ratings. Add testimonials
    to build trust & social proof.
  zotabox_description:
    Grow Brand with Easy Promotions and Lead Captures. Roll Out
    your Onsite Conversion Script in Minutes
  bigcommerce_feed: BigCommerce Store
  bigcommerce_store_hash: BigCommerce Store Hash
  bigcommerce_access_token: BigCommerce Access Token
  sftp_out: Upload to SFTP (CSV)
  lights_america: Lights America
  learn_more_category_id: Learn more about Category ID
  sheet_name_or_sheet_index: Sheet name
  help_text_sheet_name_or_sheet_index:
    Enter name of the sheet containing feed data,
    find it at the bottom left corner of Google Sheet. Support multiple sheet separate
    by comma.(e.g., Sheet1, Sheet2).
  feed_auto_run_update:
    This feed is triggered by not in feed. It will automatically
    run after the update feed that is connected has finish processing.
  metafield_sample_value:
    field.toy (field is namespace and toy is key separated by
    a period(.))
  category_name: Category Name
  nod: Network One Distribution (NOD)
  use_filter: Apply filters
  internet_bikes: Internet Bikes
  label_url_unescape: Unescape URL
  help_text_url_unescape: Disable this option to use raw URL value
  number_allowed:
    Export feature only allows column index mapping and does not have
    guided field mapping.
  enter_column_index: Enter column index
  help_text_index_for_cost_and_quantity:
    The column index. Sum multiple %{mapping}
    field with comma separated
  help_text_gsheet_out: The column index. eg. 1, 2, 3.
  bigcommerce_store: "BigCommerce Store (required installation of syncX: Stock Sync)"
  exported_at: Exported at
  categories: Product Categories
  label_categories: Replace store product's categories
  help_text_categories:
    "When is unchecked, syncX: Stock Sync will not remove any
    existing categories on store and only add new categories from source"
  orso: Orso
  warning_remove_product_label: Remove Product
  warning_remove_product:
    Halt process when %{percent}% of products being removed.
    Once the threshold selected above is reached, removing products will stop and
    warning message is displayed.
  warning_partial:
    "Warning - remove halt: detected over %{warning_percentage}% products
    being removed, only %{products_deleted} products / %{variants_deleted} variants
    removed."
  label_blank_val: Default value if cell is blank
  help_text_blank_val:
    "<0>Set default value for blank cells. By default, blank metafield
    cell will be set as <1>-</1>.</0>"
  agis: Agis
  bigcommerce_store_suspended:
    BigCommerce store suspended. Please contact BigCommerce
    support for more information.
  invalid_store:
    "syncX: Stock Sync could not access store API. Please check your
    store settings."
  label_append_prefix: Append Prefix on Feed
  help_text_append_prefix:
    "<0>eg. <1>PRE-</1> will convert value from <6>xxxx</6>
    to <3>PRE-xxxx</3>.</0>"
  help_text_append_postfix:
    "<0>eg. <1>-END</1> will convert value from <6>xxxx</6>
    to <3>xxxx-END</3>.</0>"
  shopify_public: Shopify Public
  apply_same_product_id_for_archive:
    Allow archive of duplicate products with the
    same product identifier
  archive_product_when_all_locations_nil:
    Archive products only if the quantity is
    zero in all locations
  filter_note_for_archive:
    "NOTE: There is no UNDO process. Please make sure to add
    filter by feed data and/or filter by Shopify products to avoid archiving active
    products"
  choose_certain_product_to_add_filter_for_avoid_archive:
    You can choose to include/exclude
    certain products from being archived with filters.
  standardized_product_type: Product Category
  label_language_code: Language
  language_code_help_text:
    Set standard product type for the matching products based
    on the selected language
  bypass_blank_row_label: Include empty row.
  heo: Heo
  label_not_taxable_flag: Not charge tax indicator
  help_text_not_taxable_flag:
    "It will not charge tax when this value is present in
    the feed. Example: 0, hide, not-taxable"
  label_taxable_flag: Charge tax indicator
  help_text_taxable_flag: "Example: show, charge tax, 1"
  matas: Matas
  bin_picking_number: Bin Picking Number
  google_shopping_in: Google Shopping
  bts_account_id: BTS Account ID
  update_on_product_level: Include updating attributes on product level
  visible: Visibility
  label_is_visible: Visible indicator
  label_is_not_visible: Non visible indicator
  help_text_is_visible:
    It will ENABLE the product visibility in store when this value
    is present in the feed. For example, 1, true, TRUE
  help_text_is_not_visible:
    It will DISABLE the product visibility in store when this
    value is present in the feed. For example, 0, false, FALSE
  access_token_url: Access Token URL
  scope: Scope
  grant_type: Grant Type
  dropshipping_b2b_v2: Dropshipping B2B V2
  feed_4akid_shopify_store: Feed 4akid Shopify Store
  clickhere2shop: ClickHere2Shop
  global_craft: Global Craft
  inque_style: inQue.Style
  rct_data_feed: Rectron Data Feed
  vidaxl: VidaXL
  syscom_rest_api: Syscom REST API
  swift_stock_flat: Swift Stock Flat
  swift_stock_nested: Swift Stock Nested
  swift_stock_removal: Swift Stock Removal
  include_header: Include header row
  include_header_info: The header will only be added if the first row is empty
  label_status_active_flag: Active indicator
  label_status_draft_flag: Draft indicator
  label_status_archived_flag: Archived indicator
  help_text_status_active_flag:
    "It will become active when this value is present
    in the feed. Example: 0, true, TRUE, or any value."
  help_text_status_draft_flag:
    "It will become a draft when this value is present
    in the feed. Example: 0, false, FALSE, or any value."
  help_text_status_archived_flag:
    "It will become archived when this value is present
    in the feed. Example: 0, false, FALSE, or any value."
  update_feed_limit_title: Feed Limit Exceeded
  update_feed_limit_body:
    "<0>Feed limit exceeded, please <1>upgrade</1> to increase
    the feed limit.</0>"
  import_feed_limit_body: Click on request for extra add feed. (You will be notified).
  remove_feed_limit_body: Click on request for extra remove feed. (You will be notified).
  request: Request
  search: Search
  export: Export
  remove: Remove
  discard: Discard
  reset: Reset
  unpin_feed: Unpin feed
  ftp_out: Upload to FTP (CSV only)
  google_spreadsheet_out: Write to Google Sheets
  email_out: Send Out Email
  dashboard: Dashboard
  create_feed: Create Feed
  select_connection: Select connection
  feed_manager: Feed Manager
  matching_column: Matching column
  apply_optional_filter: Filter (Optional)
  change_connection: Change connection
  connect_product_feed: Create new feed
  continue_update: Product/inventory sync
  one_time_usage: Pay as you go
  monthly_subscription: Monthly plan-continuous sync
  select_connection_placeholder: Search for more connections / suppliers
  most_popular: Most popular
  no_connections_found: Sorry, no results were found.
  new_search: Try a new search or use our suggestion below.
  contact_supplier_helptext:
    "Are you new to syncX: Stock Sync or having problem communicating
    with your drop shipper or supplier regarding inventory feed? Please fill in the
    form below so we can help you connect with your supplier easier."
  cc_email: <EMAIL>
  send: Send
  disable_schedule: Disable Schedule
  disable_schedule_helptext:
    Disabling the scheduler will PAUSE any automated activity
    for this feed
  disabled_feed_title: Feed is Disable
  disabled_feed_body:
    "<0>You have exceeded the feed limit of your current plan. Enable
    feed by <1>upgrading your plan</1></0>"
  applied: Applied
  buy_here: Buy here
  pause: Ready
  start: Ready
  nrdata: NR Data
  square_store: "Square Store (required installation of syncX: Stock Sync)"
  google_spreadsheet_in: Google Sheets
  ftp_in: FTP - Download
  email_in: Email with Attachment
  snappy_feed_details_title: FREE Bulk Update of Quantity , Price or Compare at Price
  snappy_step_one: Download your Shopify products in CSV format
  snappy_step_two:
    Change the quantity, price and compare price of your products in
    the CSV file.
  select_connection_text: "...and 200 more connections / suppliers"
  periodic_helptext: Run interval hours
  nonperiodic_helptext: Run once per day
  new_setup: New Setup
  lang: EN
  switch_version: Classic Mode
  no_connections_found_helptext:
    No messages matched your search. Please try again
    with another keyword.
  en: English
  ja: Japanese
  zh_cn: Chinese (China)
  mapping: "Step 2: Matching Column"
  apply_filter_label: Apply filters certain products to be shared with buyers
  stock_sync_partners: "syncX: Stock Sync partners"
  transactions_helper_text:
    Events such as subscriptions, uninstalls, important updates
    etc
  milestone_helper_text: Special reminders such as first time usage and account inactivity
  sync_failure_helper_text:
    If process failed 2 to 3 times continuously, it won't
    notify unless failure less than 2
  feed_manager_title: "Step 1: Feed manager"
  feed_mapping_title: "Step 2: Matching column"
  feed_filter_title: "Step 3: Filter (optional)"
  feed_advanced_settings_title: "Step 4: Advanced settings"
  empty_dashboard_title: Start building your dashboard
  empty_dashboard_helptext:
    "Connect your product inventory or feed data from your
    suppliers, warehouses or others with syncX: Stock Sync"
  add_products: Add products
  import_three_products: Only process 3 products (no credits required)
  view_more_activity_log: More >
  billing_subscriptions_title: Update / Export (Subscription)
  billing_credits_title: Add / Remove (Credits)
  feed_limit_helptext: Feed Limit
  feed_limit_current_plan: Feed limit
  each_month: "/month"
  each_year: "/year"
  credit: Credit
  buy_credits: Buy Credits
  confirm_to_proceed: Confirm to proceed?
  quick_update: Quick Update
  upgrade_plan_here: Upgrade your plan here
  basic_auth: Basic Auth
  oauth_2: OAuth2
  oauth_2_with_password: OAuth2 with Password
  oauth_2_for_odoo: OAuth2 for Odoo
  none: No Auth
  zoho_inventory_out: Write to Zoho Inventory
  zoho_inventory: Write to Zoho Inventory
  consumer_key: Consumer Key
  consumer_secret: Consumer Secret
  token_secret: Token Secret
  realm: Realm
  restlet: Restlet
  only_continue_or_deny_allowed: Only "continue" or "deny" allowed
  lonbrook: Lonbrook
  killerdeal: Killer Deal
  label_cost_round: Cost round
  label_cost_delimiter: Cost delimiter
  label_compare_price_at_round: Compare-at Price round
  label_compare_price_at_delimiter: Compare-at Price delimiter
  switch_to_new_version: "Try new syncX: Stock Sync"
  directory_file_name_tooltip: eg. /public_html/project/data/inventory.csv
  please_fill_in: Please fill in
  custom_file_name_tooltip:
    Use [current_file] to represent file name. eg. '[current_file]_updated'
    will renamed to 'feed190327_updated.csv'
  receive_email_connection: Receive Email with Attachment Connection Method
  receive_url_email_connection: Receive Email with URL Connection Method
  how_gmail_forwarding:
    "How to get Gmail forwarding confirmation code on syncX: Stock
    Sync?"
  email_process_run_certain_time: Why email process only runs at a certain time period?
  click_or_drop_upload: Click/drop your feed file for manual upload
  file_setting: File setting
  no_file_setting_required: No file setting required
  field_name: Field Name
  no_extra_settings: No extra settings
  product_options: Product options (variations)
  store_product_field: " Store product field(s)"
  setting: Smart setting
  mappings: Mapping
  static: Fixed value
  add_more_field: Add more field
  add_metafield: Add metafield
  reset_mapping: Reset Mapping
  reset_settings_to_original:
    " This will reset all extra settings fields to the original
    template."
  delimiter: delimiter
  round: round
  currency_from: Currency from
  currency_to: Currency to
  condition_markdown_or_markup: conditions (markdown/markup)
  condition_if: Condition (if)
  price_then: Price (then)
  dont_update: Don't update
  when: when…
  column_name: Column name
  find_help_text:
    Find multiple match any words or phrases, separated by a comma.
    To find any part of the words, supports wildcard (*) and regular expressions.
  replace_help_text:
    Replace multiple words or phrases, separated by comma. eg word1,word2.
    (auto matched with find)
  blank_value_help_text:
    Set default value for blank cells. By default, blank metafield
    cell will be set as -
  no_image_append_image: Only add images if product has no image
  no_video_link_append_video_link: Only add video links if product has no video link
  skip_image_import_new_image: Skip existing images, import new images
  skip_video_link_import_new_video_link:
    Skip existing video links, import new video
    links
  clear_image_before_import: Clear matching product's images before importing as new
  url_option: URL option
  raw_url: Raw URL
  modify_url: CDN/Proxy URL
  variant_image_option: Variant image option
  assign_first_image_to_all_variant: Assign first product images to all variants
  use_image_url_if_no_image:
    Use this image URL if the product does not have any image,
    support multiple URL separated by comma
  auto_assign_value:
    Example, enter Option Name in Field Mapping below will auto assign
    value accordingly
  option_value: Option Value
  enable_continue_selling:
    "<0><0>Enable</0> continue selling when out of stock feed
    value is</0>"
  disable_continue_selling:
    "<0><0>Disable</0> continue selling when out of stock
    feed value is</0>"
  optional_store_prefix_or_postfix: Optional Store prefix/postfix
  feed_prefix: Feed prefix
  feed_postfix: Feed postfix
  case_sensitive: Match case
  case_sensitive_help_text:
    Enabling to differentiate upper capital and lower capital
    identifiers. For example, Abc123 and ABC123 considered different products
  update_with_same_product_identifier:
    Update duplicate products with same product
    identifier
  track_quantity: Track inventory
  setup_inventory_tracking: "(Set up inventory tracking for products)"
  quantity_option: Quantity option
  overwrite_quantity: Overwrite existing quantity
  add_or_deduct_quantity: Add/Deduct with existing quantity (+)
  deduct_quantity: Only deduct from existing quantity (-)
  quantity_set_to_zero: Set existing quantity in store to 0 before updating
  skip_product_zero_quantity: Skip product(s) with zero quantity
  quantity_then: Quantity (then)
  add_rules: Add rules
  help_text_update_feed:
    Choose the update feed that you would like to archive/remove
    products that are no longer in the feed file
  trigger_by_not_in_feed: Auto clear not in feed
  process_skip_existing_products_in_store:
    This process skip existing products in
    store that has matching product identifier. If no new products are added, tag
    will ignored.
  manage_to_delete_or_archive_products:
    "Manage to delete or archive products in store.
    syncX: Stock Sync does not archive specific variants in a multi variant product."
  manage_to_delete_or_archive_products_wc:
    "Manage to delete or hide products in store.
    syncX: Stock Sync does not hide specific variants in a multi variant product."
  enable_or_archive_single_variant_product:
    Enable to archive single variant product(s).
    Multi variant product will be ignored
  enable_or_archive_single_variant_product_wc:
    Enable to hide single variant product(s).
    Multi variant product will be ignored
  archive_descriptions: "Archiving products does not require syncX: Stock Sync credit"
  partials_match: Partial match
  enable_to_archive_multi_variant:
    Enable to archive multi variant products even if
    only one variant matching product
  enable_to_archive_multi_variant_wc:
    Enable to hide multi variant products even if
    only one variant matching product
  no_undo_process:
    There is no undo process. Please make sure to add <1>Filters (Step
    3)</1> to avoid removing active products
  no_undo_process_for_archive:
    There is no undo process. Please make sure to add <1>Filters
    (Step 3)</1> to avoid archiving active products
  no_undo_process_for_archive_wc:
    There is no undo process. Please make sure to add
    <1>Filters (Step 3)</1> to avoid hide active products
  manage_product_visibility: Automate based on inventory level
  total_inventory_on_all_location:
    Based on total inventory on all locations. (Shopify
    only)
  auto_publish: Auto publish
  any_variant_quantity_more_than_zero: If any variant has quantity greater than zero
  condition: Condition
  hide_and_archive_product_online_store: Auto unpublish / archive
  hide_product_online_store: Auto unpublish
  hide_product_sales_channel: Auto unpublish product
  all_variant_is_zero: If all variants is zero quantity in all locations and
  continue_selling_out_of_stock: continue selling when out of stock
  is_disabled: is disabled
  hide_product_from_online_store: Archive products
  hide_from_online_store: Disable to unpublish products only for online store
  track_quantity_and_low_stock_option:
    Track inventory & low stock option moved to
    <2>Column Matching > Quantity field setting</2>
  duplicate_product_option:
    Duplicate product option moved to <2>Column Matching >
    SKU field setting</2>
  reset_quantity_on_selected_location:
    Reset quantity on <1>selected location</1>
    to 0 for <4>unmatched products</4> in feed
  completed_process_is_not_reversible:
    Completed process is <1>not reversible</1>.
    You are advised to download export product as backup prior to running a process.
  set_out_of_stock_to_zero: "<0>Set out of stock to <1>zero quantity</1></0>"
  recommend_to_apply_filter:
    Recommend to apply Product Filter to avoid reset quantity
    to zero for all unrelated products
  set_out_of_stock_to:
    "<0>Set out of stock to <2>zero quantity that was updated by
    syncX: Stock Sync before</2></0>"
  all_unmatched_variants:
    "All unmatched variants’ quantity to zero (out of stock)
    that was updated by syncX: Stock Sync before."
  match_with_store_category: Match with store
  help_find_category_words:
    "<0>Find multiple words or phrases separated by a comma.
    eg <2>word1,word2</2></0>"
  find_in_feed: Find in feed
  help_map_category_words:
    "<0> Match with multiple categories in store separated
    by a comma. eg <2>word1,word2</2></0>"
  valid_email_address: Please enter a valid email address
  collection_name: Collection Name
  length: Length
  one_variant_option_required:
    At least 1 Variant Option required. Click "Add Option"
    below
  bearer_token_body: Bearer Token Body
  woocommerce_rest_cannot_view:
    The API key & secret provided is incorrect. Please
    check and try again.
  equals: Is in
  add_filter: Add filter
  ignore_variant_track_quantity: 'Ignore variants with "Track inventory". '
  contains: Equal to
  not_contains: Not equal to
  not_contain: Not contain
  decide_product_processed:
    "<0>Match <1>all</1> filters: Products to be processed
    from incoming feed and store products.</0>"
  store_product: Store product
  incoming_feed: Incoming feed
  preview_feed: Preview Feed
  greater_than: Greater than
  lower_than: Less than
  contain: Contain
  store_field_name: Store field name
  product_id_and_product_field: Product Identifier / Store Product Field(s)
  create_remove_feed_title: unmatched variants
  create_remove_feed_subtitle: Setup here
  view_remove_feed: View auto clear feed
  source_site_overload: Store server overload. Please retry or upgrade web hosting.
  sftp_wildcard_not_supported:
    SFTP server doesn't support wildcard. Please contact
    SFTP server IT team.
  add_option: Add option
  quick_link: Quick Link
  crawler: Crawler
  system: System
  not_equals: Is not in
  quantity_not_mapped_message:
    "To enable out of stock strategy, please map <2>quantity</2>
    field on <4>step 2: matching column</4>"
  no_matches: No matches found.
  why_failed: Why Failed?
  file: File
  to_store: to store
  warning_zero_over_percentage:
    "Warning - update halt: detected over %{percentage}%
    inventory set to zero."
  rows_remaining_count: Rows remaining
  label_false_values: Non physical product indicator
  help_text_false_values:
    "Accepts multiple value separated with comma. Example: 0,
    false, FALSE, or any value."
  import_fail_halfway:
    "%{products_imported} imported but fails halfway. Please contact
    support team"
  products_is_deleted: Products removed
  create_remove_feed_popover_message:
    Do you want to archive variants not in feed
    from current feed?
  import_trial_run:
    "%{products_imported} products added successfully. Continue process
    to add remaining %{remaining} rows."
  costway: Costway
  possible_solution: Possible Solution
  view_warning: View Warning
  change_logs_helptext: View variant(s) changes in the current process
  not_in_store_helptext: View variant(s) that was found in feed file but not in store
  product_status_helptext:
    View product(s) that was published,unpublished or changed
    status
  low_stock_helptext: View variant(s) that are low in stock
  not_in_feed_helptext: View variant(s) that are not found in feed
  single_line_text_field: Single line text
  list.single_line_text_field: Single line text (Accept list of values)
  multi_line_text_field: Multi-line text
  number_integer: Number - Integer
  number_decimal: Number - Decimal
  json: JSON string
  boolean: Boolean
  date_time: Date and time
  url: URL
  color: Color
  volume: Volume
  dimension: Dimension
  rating: Rating
  json_string: JSON string
  integer: Number - Integer
  string: Single line text
  PRODUCT: Product
  PRODUCTVARIANT: Variant
  content_type: Content Type
  owner: Owner
  create_location_label: Auto add stock to <2>location<1></1></2> if not there
  need_select_one: Field must be selected
  store_field: Search store field
  mapping_field: Required feed column name or index
  discord: Discord
  ficeda: Ficeda
  product: Product
  variant: Variant
  mapping_field_index_only: Export feed only allows column index matching
  next_billing_date: Renew on
  store_size: Total Variants
  active: Active
  daily_schedule: Daily Schedules per Feed
  daily_schedule_current_plan: Daily schedules for each feed
  total_number_variant_scanned: "(Total variants scanned, filter reduce count)"
  total_number_variant_scanned_current_plan:
    "(total variants scanned, filter reduce
    count)"
  store_size_current_plan: Total variants
  total_number_update_feed: "(Total number of update / export feeds)"
  total_number_update_feed_current_plan: "(total number of update / export feeds)"
  total_number_schedule_frequency: "(Total schedule frequency in a day)"
  manage_plan: Manage plan
  slider: Manage your usage limit by dragging the slider
  feature_list: Show all feature
  show_less_feature: Show less
  credit_can_be_purchased: Credits can be purchased any time
  credit_remain_after_reinstalled: Credits remain after app is reinstalled
  credit_with_combo_plan: Credits comes together with the combo plan
  credit_will_added_ever_month_with_subscribed_plan:
    Credits auto added every month
    according to the subscribed <2>Combo</2> plan
  buy_stock_sync_credit: Buy credit(s)
  platform: Platform
  label_url_unescape_with_sample: Unescape URL <2>https%3A// to https://</2>
  learn_how_to_make_custom_text: Learn how to make custom text
  how_to_switch_to_different_plan: How do I switch to a different plan?
  how_to_switch_to_different_plan_answer:
    You can switch to a new plan at any time
    by going to the Billing page in your account. Select the plan you'd like to switch
    to and immediately start using new limits
  what_happens_if_hit_feed_limit_or_store_size:
    What happens if I hit my feed limit
    or store size?
  what_happens_if_hit_feed_limit_or_store_size_answer:
    You are not able to update
    the products that has exceeded the store sizes, it is advisable to filter products
    to keep the size smaller. Once you have reached the feed limit, you are not able
    to create extra unless you upgrade your plan
  have_subscribed_monthly_plan_but_cant_import_product:
    I have subscribed to the monthly
    plan but I still cannot import any products.
  no_need_subscribed_monthly_plan:
    "You don't have to subscribe to any monthly plan
    to add new products. You need to purchase syncX: Stock Sync credit for adding
    products."
  small_volume: Small Volume
  large_volume: Large Volume
  credit_can_be_used:
    Credits can be used to add products to store. Do not expire
    and remain after app is reinstalled
  label_enable_track_qty_value: Enable Track Inventory Indicator
  label_disable_track_qty_value: Disable Track Inventory Indicator
  help_text_eq_enable_track_qty_value_1:
    Enable track inventory when this value is
    present in the feed.
  help_text_eq_enable_track_qty_value_2: "Example: 1, enable, track, back-in-stock"
  help_text_eq_disable_track_qty_value_1:
    Not track inventory when this value is present
    in the feed.
  help_text_eq_disable_track_qty_value_2: "Example: 0, false, disable, out-of-stock."
  product_sku: Product-level SKU
  feed_column_name: Feed column name
  send_message_for_help: Send message for help
  deny: Deny
  draft: Draft
  archived: Archived
  "true": "True"
  "false": "False"
  helptext_set_datetime:
    Insert date format which fits the date value in the feed.
    E.g. %d %B %Y
  ingram_micro: Ingram Micro
  store_product_filter: Store product filter
  incoming_feed_filter: Incoming feed filter
  field: Field
  column: Column
  database: Database
  default_store_weight: Default store weight unit
  label_metafield_weight_unit: Metafield weight unit
  label_metafield_volume_unit: Metafield volume unit
  expired: Expired
  add_template: Add Template
  clear_cache: Clear Cache
  template: Template
  deleted_at: Delete at
  undelete: undelete
  csv_file: CSV File
  load_csv: Load
  load_csv_10_rows: Load 10 rows(force)
  load_csv_filter: Load with filter
  load_csv_raw: Load raw
  download_s3: Download S3 Link
  receiving_email: Receiving Email
  update_feed: Update feed
  bems: BEM'S
  bulk_update: Bulk update
  run_on_local: Run on local
  run_on_fargate: Run on fargate
  product_listing: Product Listing
  warning_load_product_listing:
    Warning, we're loading %{preLoadStore} of product,
    do you want to proceed?
  load_first_250: Load first 250 only
  load_since_last_logs: Load since last logs
  vendor_listing: Vendor Listing
  collection_listing: Collection Listing
  load_custom: Load custom
  load_smart: Load smart
  metafield_definition_listing: Metafield Definition Listing
  variant_metafield: Variants
  cache_key: Cache Key
  process_cache_key: Process
  search_from_shopify: Search from Shopify
  go: Go
  all: All
  SKU_only: SKU only
  search_logs: Search Logs
  search_logs_sku: Search logs via SKU
  search_mismatched: Search Mismatched
  search_not_in_store: Search not in store / feed
  xml_path_tester: XML path tester
  test_price_delimiter: Test Price delimiter
  test: Test
  test_col_sep: Test Col Separator
  date_of_feed_created: Date of feed created / updated at
  source_type: Source Type
  current_feed_password: Current feed password
  path_to_file: Path to file
  copy_store_id: Copy to store ID
  copy_store_token: Copy Feed to other store
  updates: Update
  edit_feed: Edit feed (Classic Mode Admin)
  stop_impersonating_admin: Stop Impersonate and Go To Admin
  match_and_delete: When found match it will not continue find same match again
  auto_hide_product_label: Auto unpublish products
  set_out_of_stock_to_zero_label: Set out of stock to Zero Quantity.
  set_out_of_stock_to_label:
    "Set out of stock to Zero Quantity that was updated by
    syncX: Stock Sync before."
  others: Others
  amrod_v2: Amrod V2
  s3_access_key: S3 Access Key
  s3_secret_key: S3 Secret Key
  categories_id: Categories ID
  refresh_token: Refresh Token
  domain_prefix: Domain Prefix
  realm_id: Realm ID
  x_qbp_api_key: X Qbp API Key
  share_url: Share URL
  token: Token
  api_country: API Country
  etsy_token: ETSY Token
  etsy_secret: ETSY Secret
  ecd_subscription_key: ECD Subscription Key
  session_id: Session ID
  auth_token: Auth Token
  customer_no: Customer No.
  box_access_token: Box Access Token
  box_refresh_token: Box Refresh Token
  run_three_product: Run 3 products
  formula: Formula
  create_location_preview: Auto add stock to location if not there
  ebihr: e-Bihr
  richmond_interiors: Richmond Interiors Sales Services
  metafield_indicator: True Indicator
  non_metafield_indicator: False Indicator
  is_true_indicator_help_text:
    "Set definition as true when this value is present
    in the feed. Example: 1, true, yes"
  is_false_indicator_help_text:
    "Set definition as false when this value is present
    in the feed. Example: 0, false, not"
  stock_status: Stock Status
  label_instock_flag: In stock indicator
  label_outstock_flag: Out of stock indicator
  help_text_in_stock:
    Set stock satus as In stock when this value is present in the
    feed.
  help_text_in_stock_2: "Example: 1, true, instock"
  help_text_out_stock:
    Set stock status as Out of stock when this value is present
    in the feed.
  help_text_out_stock_2: "Example: 0, false, outofstock"
  remove_products_advanced: Remove products / variants
  remove_product_helptext:
    "<0>Removing products and <1>no undo process.</1> Please
    make sure to <1>add Filters</1> to avoid removing active products.</0>"
  stock_management: Stock Management (Track stock quantity for products)
  eof_error: Please check file name and permission. File not exist in directory.
  ftp_perm_error: Please check file name and permission. File not exist in directory.
  google_drive_auth_error:
    Please check file name and permission. File not exist in
    directory.
  ftp_file_not_found: Please check file name and permission. File not exist in directory.
  run_time_no_file: Please check file name and permission. File not exist in directory.
  sftp_no_file: Please check file name and permission. File not exist in directory.
  storeden: Storeden
  exchange: Exchange
  between: Between
  disabled_stock_management_advanced_settings:
    Disable Stock Management in Quantity
    field settings to display Stock Status on products
  schedule_not_enabled: Turn on
  low_stock_level_helptext:
    "When product reach the level above, syncX: Stock Sync
    will send low stock alert via email. Enable the low stock alert"
  empty_ftp_path: The directory does not contain any files.
  woocommerce_credentials: Woocommerce Credentials
  woocommerce_key: Woocommerce Key
  woocommerce_secret: Woocommerce Secret
  help_text_date_formats:
    "<0>Insert a date format which fits the date value in the
    feed. E.g. %d %B %Y <2><0></0>Learn More</2></0>"
  bpn: Bin Picking Number
  ftp_connection_failed: Connection fail. Please check Source HOST.
  sftp_file_not_found: Please check file name and permission. File not exist in directory.
  one_drive_url_not_found: OneDrive URL not available.
  reduce_payment: "Want save more? "
  cancel_subscription: Need to cancel your subscription?
  sad_to_see_you_go: We're sad to see you go.
  cancel_subscription_button: Cancel subscription
  update_billing: Update Billing
  add_custom_field: Add Custom Field
  archive_products_feed_setting_summary: Archive products
  sku++vendor: SKU + Vendor
  troubleshoot: Troubleshoot
  product_troubleshoot: Product Troubleshoot
  product_quantity: Quantity
  title_or_sku: Title/SKU
  search_title_or_sku: Search Title/SKU
  help_text_is_in_stock:
    "Set stock status as in stock when this value is present
    in the feed. Example: 1, true, instock"
  help_text_is_out_stock:
    "Set stock status as out of stock when this value is present
    in the feed. Example: 0, false, outofstock"
  parent_category_id: Parent Category ID
  parent_category_helptext:
    Enter the Product Category ID and the matching column
    value will be nested as subcategory for the Product Category
  storage_account: Storage Account Name
  container: Container Name
  access_key: Access Key
  blob: Blob
  azure_blob: Azure Blob
  no_changes_display: No Changes Display
  blob_help_text: Blob name belonging to the container. eg. product.csv
  access_key_help_text: Any one of the two access key in storage account
  rekman: Rekman
  public_key: Public Key
  private_key: Private Key
  no_changes_found: No changes found
  stock_management_extra_attribute: Stock Management
  german: German
  french: French
  reset_matching_column: Reset matching column
  change_connection_method: Change connection method
  change_connection_content:
    Would you like to proceed with the current matching column
    settings?
  sku_last_update: Updated
  sku_last_removed: Removed
  sku_last_added: Added
  sku_last_export: Exported
  success: Success
  fail: Fail
  delay_support: Delay Support
  sku++handle: SKU + Handle
  publish: Publish
  unpublish: Unpublish
  label_metafield_dimension_unit: Metafield dimension unit
  shopify_flow: Shopify Flow
  vietti: Vietti
  how_to_apply_filter: How to apply filters
  hypercel: Hypercel
  polish: Polish
  spanish: Spanish
  dutch: Dutch
  feed_rows_filters: Feed rows filtered
  total_feed_rows: Total feed rows
  backmarket: BackMarket
  uk_distributor: UK Distributor
  banned_apparel: Banned Apparel
  synnex: Synnex
  special_volume: Special Volume
  add: Add
  congratulations_successful_feed: Feed's looking good!
  rate_review: How was your experience?
  like_app: I like the app
  have_idea: I have an idea
  suppliers: Suppliers
  connections: Connections
  search_for_more: Search for more suppliers/connections
  shopify_self: Bulk Product Edit
  country: Country
  au: Australia
  be: Belgium
  ca: Canada
  canda: Canda
  cl: Chile
  cn: China
  cz: Czechia
  dk: Denmark
  de: Germany
  global: Global
  gr: Greece
  hk: Hong Kong
  it: Italy
  lv: Latvia
  my: Malaysia
  mx: Mexico
  nl: Netherlands
  nz: New Zealand
  nno: Norway
  pl: Poland
  ro: Romania
  za: South Africa
  es: Spain
  se: Sweden
  gb: UK
  us: USA
  lt: Lithuania
  accounting_and_financial: Accounting & Financial
  adult_fashion: Adult, Fashion
  art_supplies_and_craft_kits: Art Supplies and Craft Kits
  automobiles_and_motorcycles: Automobiles & Motorcycles
  books: Books
  business_sales_and_opportunities: Business Sales and Opportunities
  computers_and_office: Computers & Office
  contact_lenses_and_cosmetics: Contact Lenses & Cosmetics
  dropshipping_supplier: Dropshipping Supplier
  electronics_and_electrical: Electronics & Electrical
  engineering_and_fabrication: Engineering & Fabrication
  event_lighting_equipment: Event Lighting Equipment
  fair_trade_crafts_and_artisan_products: Fair Trade Crafts & Artisan Products
  fashion: Fashion
  fashion_accessories: Fashion, Accessories
  furniture: Furniture
  furniture_home_and_garden: Furniture, Home & Garden
  gifts_and_gadgets: Gifts & Gadgets
  gifts_and_gadgets_fashion: Gifts & Gadgets, Fashion
  health_and_beauty: Health & Beauty
  health_and_beauty_gifts_and_gadgets_fashion: Health & Beauty, Gifts & Gadgets, Fashion
  home_and_garden: Home & Garden
  home_and_garden_furniture_electronics_sporting_goods:
    Home and Garden, Furniture,
    Electronics, Sporting Goods
  in: India
  inventory_management_software: Inventory Management Software
  jewelleries_and_accessories: Jewelleries & Accessories
  lighting_and_electrical: Lighting & Electrical
  marketing_and_affiliate: Marketing & Affiliate
  mobile_phones_and_accessories: Mobile Phones & Accessories
  mother_and_kids: Mother & Kids
  online_backup_and_cloud_storage: Online Backup & Cloud Storage
  online_invoicing_and_billing_software: Online Invoicing & Billing Software
  order_fulfillment_services: Order Fulfillment Services
  pos_solution: POS Solution
  pet_products_and_accessories: Pet Products & Accessories
  product_data_and_content_solutions: Product Data & Content Solutions
  promotional_products_and_advertising_specialty_items:
    Promotional Products & Advertising
    Specialty Items
  sports_and_entertainment: Sports & Entertainment
  store_and_merchandise: Store & Merchandise
  technology: Technology
  toys_and_hobbies: Toys & Hobbies
  video_games_and_software: Video Games & Software
  wms: WMS
  wholesale_distribution: Wholesale Distribution
  wine_logistics_and_distribution: Wine Logistics & Distribution
  suppliers_available: suppliers available
  purchase_availability: Availability
  label_purchase_available_flag: Available option
  help_text_purchase_available_flag:
    "the availability flag will be set to true if
    value is present in the feed. Example: available, yes, true"
  label_purchase_preorder_flag: Preorder option
  help_text_purchase_preorder_flag:
    "the availability flag will be set to preorder
    if value is present in the feed. Example: preorder, pre-order"
  label_purchase_disabled_flag: Disabled option
  help_text_purchase_disabled_flag:
    "the availability flag will be set to disabled
    if value is present in the feed. Example: disabled, no, false"
  accessories: Accessories
  mobile_phones: Mobile Phones
  banner_billing:
    "<0>Free 25%</0> credits based on plan variant limit, for first
    time plan upgrade"
  pet_products: Pet Products
  skip_feed_rows: Skip feed rows
  helptext_skip_feed_rows: Specify number of rows to skip the empty rows from above
  jewelleries: Jewelleries
  lighting: Lighting
  software_and_service: Software & Service
  cant_find_your: Can't find your
  view_all_suppliers: View all suppliers
  notification: Notification
  account: Account
  view_all_plans: View all plans
  view_detail: View Detail
  show_less: Show Less
  Update: Update
  Add: Add
  Export: Export
  Remove: Remove
  last_run: "Last run %{lastRunAt} "
  copy_feed: Copy Feed
  copy_as_update: Copy as Update Feed
  every_interval_hour: Every %{jobInterval} hours
  every_interval_minute: Every %{jobInterval} minutes
  trigger_by_update: Post update feed
  deleted: Deleted
  quantity_zero_alert_helptext:
    Keep threshold higher as it will prevent a lot of
    products from updating to zero
  remove_product_alert: Remove product alert
  quantity_zero_alert_helpertext:
    Halt process when <0>%{warningZeroQtyUpdate}%</0>
    of products quantity set to zero. Once the  selected above is reached, the update
    stops and a warning message is displayed
  remove_zero_alert_helpetext:
    Halt process when <0>%{warningRemoveProducts}%</0>
    of products being removed. Once the threshold selected above is reached, removing
    products will stop and warning message is displayed.
  plan_v5: Current plan
  monthly_v5: "(monthly)"
  yearly_v5: "(annually)"
  payment: Payment
  per_year: year
  update_description: Existing products in store
  export_description: Products to external destination
  import_description: New products to store
  remove_description: Delete or archive unwanted products
  feed_limit_used: Feed limit
  credit_remaining_create_feed: Credit(s) remaining
  14_days_free_trial: 14 days free trial
  selection: Selection
  product_attribute: Product Attribute
  help_text_metadata_key_wc:
    Key used to identify which product attribute value to
    update
  create_new: Create New
  one_time_credit: one-time credits
  try_now: Try Now
  back_in_stock_alert: Back in stock alert
  back_in_stock_alert_helper_text:
    Variants that are back in stock from each update
    feed
  number_of_character_and_below: Not more than 255 characters
  simpro: Simpro
  custom_field_key: Custom Field Key
  create_key_if_not_found: Create key if not found
  auto_separator: Auto Separator
  alt_col_sep: Alt Col Separator
  url_unescape: URL Unescape
  _col_sep: Col Separator
  verifone: Verifone
  chain_id: Chain ID
  pay_as_you_add_products: Pay as you add products
  clean_up_store_products: Clean up store products
  no_charges_applied: "(no charges applied)"
  buy_credit_now: Buy now
  hide_unwanted_products: Delete or hide unwanted products
  ready_create_feed: Ready to create feed?
  faqs: FAQs
  update_or_add_product: Should I update or add products?
  update_or_add_product_description:
    If the products aren't in your store yet, use
    'Add Product Feed.' For products already in your store that need updating, use
    'Update Product Feed.'
  which_field_can_be_updated: What product fields can be updated?
  which_field_can_be_updated_description:
    "You can update more than 20 fields, such
    as quantity, title, price, etc. Note: Fields are updated only if they're mapped
    in the settings."
  can_revert_add_products: Can I revert added products?
  can_revert_add_products_description:
    "Yes, you can undo products added via syncX:
    Stock Sync, and any used credits will be refunded."
  search_for_category: Search for category
  search_for_country: Search for country
  create_new_feed: Create new feed
  require_credit: Require credit
  support_variant_and_options: Support variants and options
  undo_process_credit_returned: Process can Undo credits will be returned
  monthly_subscription_required: Monthly subscription required
  keep_quantity_same: Keep available quantity level same as supplier or warehouse
  adjust_pricing_with: Adjust product pricing with flexible price condition
  update_fields_available: Choose to update any 20+ product fields available
  export_from_store: Export existing products from the store
  filter_product_to_external_file: Filtered export products to external file
  remove_unwants_products: Remove/archive unwanted products
  remove_process_cant_reverted: Remove process can't be reverted
  trigger_by_not_in_feed_run_after_update: Auto clear discountinued run after update
  get_to_know_update: Update existing products in store
  get_to_know_add: Add new products to store
  get_to_know_export: Export products to external destination
  get_to_know_remove: Remove/archive unwanted products from store
  sftp_multiple: Sftp multiple
  hide_sample_logs: Hide sample logs
  sample: Sample
  show_sample_logs: Show sample
  out_of_stock_strategy_helptext:
    Disabled Out of Stock Strategy on Step 4, to sum
    up duplicate SKU quantity
  what_you_like_to_achieve: Get started
  only_add_product_that_not_exist_in_store: Auto ignore duplicate products
  can_add_products_with_variant: Can I add products with variants?
  can_add_products_with_variant_description:
    Yes, by mapping the variant group in
    the matching column on Step 2. The variants will be grouped under the same product.
  different_subscription_and_credit:
    What is the difference of monthly subscription
    and credit purchase?
  different_subscription_and_credit_description:
    To add new products to store, credits
    need to be purchased as a one time payment. To keep the store updated, monthly
    subscription is required.
  add_feed: Add Feed
  updates_feed: Update Feed
  export_feed: Export Feed
  remove_feed: Remove Feed
  learn_more_about: Learn more about
  remove_feed_helptext:
    Choose the update feed that you would like to archive/remove
    products that are no longer in the feed file
  kebo: Kebo
  archived_activitylog: Products archived
  combisteel: Combisteel
  aapd: AAPD
  support_sort_by_size: Auto sort by size e.g. Small, Medium, Large
  language: Language
  no_feed_activity_oops: Oops! No feed activity at the moment
  skip_if_empty: Skip update when feed value is blank (value zero is not blank)
  skip_if_blank: Skip if blank
  update_billing_error_message: Contact support for assistance
  box_public_file: Public File
  public_file_helper_text:
    Disable this option, if the file or folder is not public
    and needs authorization
  url_keyword: URL keyword
  email_link: Email with URL
  kitchway: Kitchway
  force_override_cost: Force override cost
  auto_delimiter: Auto Delimiter
  exceed_300_helptext:
    The limit selected exceed the $300 monthly subscription. Please
    contact support for more information.
  double_charge_faq:
    If I upgrade my plan in the middle of the month, will I be double
    charged?
  double_charge_ans:
    Absolutely not! Rest assured, you won't be double charged. We
    follow a pro-rated billing system to ensure fairness and transparency.
  ignore_dont_track_inventory: 'Ignore variants with "Track inventory". '
  auto_completed: Auto completed by template
  back_to_quickbooks_store: Back to Quickbooks Store
  back_to_woocommerce_store: Back to Woocommerce Store
  back_to_square_store: Back to Square Store
  sign_out: Sign out
  view_details: View Details
  import_trial_run_zero:
    "%{products_imported} products added. Please check store
    if the product already exist."
  import_trial_run_lack_credit:
    "%{products_imported} products added successfully.
    \ Lack of credit to add remaining %{remaining} rows."
  quantity_quality_control: Unavailable Quantity (Quality Control)
  quantity_safety_stock: Unavailable Quantity (Safety Stock)
  quantity_damaged: Unavailable Quantity (Damage)
  location_is_selected: The location is selected based on the Available Quantity field
  available_quantity_required: Available quantity required. Click "Add field" below
  type: Type
  remove_if_blank: Remove metafield when value is blank in feed
  backorders: Allow Backorder
  allow_backorder_extra_attributes: Allow
  allow_backorder_extra_attributes_helptext:
    "It will allow backorder when this value
    is present in the feed. Example: 1, true, TRUE, or any value."
  do_not_allow_backorder: Do not allow
  do_not_allow_backorder_helptext:
    "It will not allow backorder when this value is
    present in the feed. Example: 0, false, FALSE, or any value."
  allow_but_notify_customer_backorder: Allow, but notify customer
  allow_but_notify_customer_backorder_helptext:
    "It will allow backorder, but notify
    customer when this value is present in the feed. Example: 0, notify."
  credit_update_product: Do I need credits to update products?
  credit_update_product_description:
    No. Credits not needed to update or export products.
    Credits are only used for adding new products. For update, you simply need to
    subscribe to the monthly plan.
  wholecell: Wholecell
  inventory_status: Inventory Status
  translation_engine: Translation Engine
  translate_from: Translate From
  translate_to: Translate To
  reselect_your_language: Select a new language as previous language is not found
  no_changes: No changes
  ss_webinar: "syncX: Stock Sync webinar awaits"
  preview_log: Preview sync
  preview_log_desc: Preview sync cannot run when schedule on
  test_run_success: "<0>Test run <2>complete, download change log to view</2></0>"
  credit_with_combo: Credit with Combo
  each_credit_can_be_used:
    Each credit can be used to add one product and can be purchased
    at billing page
  metafield_namespace_and_key: Metafield namespace and key
  summary: Summary
  qbp_v2: Quality Bicycle Products
  sherco_network: Sherco Network
  warehouse_codes: Warehouse Codes
  overview_last_process: Overview
  add_without_create_product: Add unique products, avoid duplicates
  low_credit: Low credit
  project_verte: Project Verte
  api_secret: API Secret
  likewise_floors: Likewise Floors
  wos_action_sports: WOS Action Sports
  customer_id: Customer ID
  auth_key: Authentication Key
  source_url_helptext: The Source URL must in the public file.
  bling_authorize_tooltips:
    You might need to authorize again if failed to get authorize
    from Bling API
  dropshipping_b2b_api: Dropshipping B2B API
  identification_code: Key
  language_code: Language Code
  start_time: Start Time
  end_time: End Time
  interval: Interval
  preview_sync_tooltip:
    Display the differences between store and feed file values
    without making any updates. The schedule must be disabled to perform preview sync.
  upgrade_to_paid_plan_for_schedule: "<0>Upgrade</0> to paid plan for schedule"
  total_variant: total variants
  sku_added: products added
  sku_exported: exported
  sku_removed: removed
  sku_updated: updated
  variant_match: variant(s) match from
  product_line: Product line
  no_plan_be_selected: No plan be selected
  catalog: Catalog
  days_trial_left: "%{dayCount} days trial left."
  confirm_edit_store_key: Are you sure to edit store key?
  no_credit_required: "(no credits required)"
  quantity_incoming: Unavailable Quantity (Incoming)
  quantity_reserved: Unavailable Quantity (Other)
  bigcommerce_quantity: Current Stock Level
  syscom: Syscom
  confirm_to_proceed_run_only_few_product:
    Proceed with adding 3 products? No credits
    will be used.
  please_send_email: Please send an email
  buyers_id: Buyers ID
  quantity_incoming_note:
    Track quantity is disabled when Unavailable Quantity is
    mapped
  price_region: Markets - Price
  compare_price_at_region: Markets - Compare-at Price
  price_region_required: Markets - Price required. Click "Add field" below
  more_settings: More Setting
  hide_settings: Hide Setting
  brand_id_helptext: Input up to 5 brand ID separated by comma
  stock_quantity: Stock Quantity
  undone_at: Undone at
  within_fourteen_days: "(within 14 days)"
  emag_marketplace: Emag Marketplace
  attempt_limit_exceeded:
    You have reached the Test connection limit today. Please
    try again tomorrow.
  legacy_pricing_plan_description:
    You are currently on the old version of pricing
    plan. By upgrading to the plans below, you will be rewarded with these benefits.
  plan_usage: Plan Usage
  beauty_gross: Beauty Gross
  dont_cancel_process_will_run_after_awhile:
    Please don't cancel the process. The
    process will auto run after a while.
  shopify_variant_id: Shopify Variant ID
  shopify_product_id: Shopify Product ID
  product_created_at: Product Date Creation
  variant_created_at: Variant Date Creation
  toptex: Toptex
  ebay_v2_in: Read from eBay (Fixed price items only)
  ebay_v2_out: Write to eBay
  other_store_using_same_public_token: All seller(s) thats connected to your inventory
  webhook_log_error:
    Please try process again as another feed was running with webhook
    and the current feed failed.
  maween_trading: Maween Trading
  days: Days
  url_list: SKU list
  legacy: "(Legacy)"
  subscription_expired: Subscription expired
  barcode++option1++option2++option3: Barcode + Option 1 + Option 2 + Option 3
  airtable_v2: Airtable V2
  base_id: Base id
  furnicher: Furnicher
  airtable_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from Airtable
  table_id: Table id
  sas_token_url: SAS Token Url
  short_description: Short Description
  barcode++option1++option2: Barcode + Option 1 + Option 2
  image_force_override_help_text:
    "Imports only images with new file names. Note:
    Renamed files will be treated as new"
  total_products: Store Products
  products_added: Added
  reauthenticate_error: Please reauthorize the connection on the feed settings page
  feed_limits: Feed Limits
  priority_supports: Priority Supports
  metafields: Metafields
  product_fields: Product Fields
  filter_feature: Filters
  templates: Templates
  duplicate_sync: Duplicate SKU
  feed_assistance: Feed assistance
  one_time_credits: One-time credits
  reset_metafield: Reset Metafield
  confirm_to_reset_metafield: Remove all matched metafields on the list?
  artisan_furniture: Artisan Furniture
  ascolour: Ascolour
  key_in_email: Email
  clean_up_unmatched_variants: Auto Clear Discontinued
  usr_mkt: USR_MKT
  pwd_mkt: PWD_MKT
  storelapse_description:
    Restore and backup products to the selected version in minutes
    and view changes in timeline.
  language_timezone: Language and time zone
  view_billing: View billing
  banner_title: Welcome to the New UI!
  banner_description: All feeds and settings are unchanged. Learn more <1>here</1>
  remove_unmatched_variant: "(Remove discontinued variants in store)"
  hidden: Hidden
  is_visible: Product Visibility
  visible_option: Visible
  net13: 13 Metriquadri
  override_categories: Override Categories
  category_name_helptext:
    "When the category is not found in store, syncX: Stock Sync
    will create as new category based on feed value"
  awaiting_email: Awaiting Email
  copy_store_token_guide: Enter the destination store public token.
  where_to_get_token: Where to get token?
  log_archive: archived
  extra_setting: Extra Setting
  quantity_rules_value: Value
  atelier_api: Atelier API
  mstgolf: Mstgolf
  jdm_products: JDM products
  marashoes: Marashoes
  phorest: Phorest
  prenta: Prenta
  shipnetwork: ShipNetwork
  business_id: Business Id
  branch_id: Branch Id
  seagull: Seagull
  retailer_list: Retailer List
  heo_csv: Heo CSV
  heo_api: Heo API
  db_context: DBContext
  new: New indicator
  used: Used indicator
  refurbished: Refurbished indicator
  data_download: Data download
  unlimited: Unlimited
  file_size_mb: File Size(mb)
  currently_assisting_message:
    We are currently assisting <0>%{total_users}</0> users.
    Thank you for your patience.
  enabled_indicator: Enabled Indicator
  disabled_indicator: Disabled Indicator
  show_condition: Show Condition
  adult: Adult
  tiktokshop: TikTok Shop
  tiktokshop_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from TikTok Shop
  use_metafield_variant: To use metafield as identifier, start input with metafield.variant
  based_plan_interval_schedule: "(based on your plan daily schedule limit)"
  start_process_now: Start Process Now
  get_the_help: Get the help you need
  sku_list: Eg. 10050050294345, 10083949893743
  connection_select: Connection
  retrieve_feed_file: Select how to retrieve feed file from supplier/dropshipper
  alert_setting: Alert setting
  action: Action
  recommended: Recommended
  what_is_discontinued: What is unmatched products?
  tax_code: Tax Code
  incorrect_xml_config: Check feed file and parent node mapping
  make_product_archive:
    Make product archive for all filtered product in store (Step
    3)
  make_product_archive_match:
    Make product archive for store product match the feed
    data
  make_product_unpublish:
    Make product unpublish for all filtered product in store
    (Step 3)
  make_product_unpublish_match:
    Make product unpublish for store product match the
    feed data
  automation: Automation
  why_on_queuing: Why on queuing?
  force_start: Force Start Process
  what_is_preview_sync: What is preview sync?
  incorrect_row_sep:
    Incorrect row separator. <NAME_EMAIL>
    for assistance.
  preview_sync_display:
    Preview sync to display the <1>differences between store and
    feed file value</1> without making any updates.
  preview_sync_schedule: Preview sync cannot run when schedule on
  learn_more_here: Learn more here
  metadata: Custom Field
  add_product_attribute: Add Product Attribute
  add_metadata: Add Custom Field
  adding_product: Adding products
  product_variations: What is product variations?
  performing_remove: Performing remove
  performing_export: Performing export
  copy_feed_remark:
    These feature is enables to copy of selected feeds from one location
    to another store.
  copy_to_store: Copy to Other store
  metafield_not_supported:
    "<0>Click  <1>here</1> to check metafield that are not
    supported to updated</0>"
  metafield_not_supported_update:
    Metafield (Content Type) that not supported to be
    updated
  unauthorized_bigcommerce_store:
    BigCommerce store is unauthorized. Please ensure
    that Stock Sync is installed correctly.
  view_error_list: Please click More to view the error list
  azure_sql_db: Azure SQL Database
  database_name: Database Name
  custom_query: Custom Query
  missing_sql: No SQL query provided
  back_in_stock: Back in stock
  back_in_stock_helptext: View variant(s) that are back in stock
  quantity_selection: Quantity selection
  on_hand_quantity: On hand quantity
  on_hand_quantity_help_text:
    The unavailable quantity field will be disabled, once
    update for on hand quantity
  disable_on_hand_quantity:
    Once unavailable quantity mapped, on hand quantity will
    disable
  my_bertus: My Bertus
  account_id: Account ID
  account_code: Account Code
  ocp_key: OCP Key
  campaign_id: Campaign IDs
  run_every: Run on Every %{jobInterval} hours (%{numberOfDailySchedules} daily schedules)
  daily_schedule_based_hourly_setting: Total daily schedule based on hourly setting
  manage_sales_channel: Edit sales channel
  select_sales_channel: Products will be published to all sales channel
  publish_on: Publish on
  credit_redeem: This credit has been redeemed
  store_code: Store Code
  manage_sales_channel_drawer:
    If none selected, the products will be published to
    all sales channel
  tuscany_leather: Tuscany Leather
  stock-price: Stock Price
  archive_description_new_feed: Remove unwanted products
  wix_feed_helptext:
    Choose the update feed that you would like to remove products
    that are no longer in the feed file
  no_undo_process_for_archive_wix:
    There is no undo process. Please make sure to add
    <1>Filters (Step 3)</1> to avoid <1>removing</1> active products
  hyundai_nl: Hyundai Nl
  gateway_nwg: Gateway Nwg
  max_page: Max Page
  assortment_id: Assortment ID
  merge_variants: Merge variants into existing products
  merge_variants_helptext:
    If the same product identifier value found in store, the
    variant will not be merged
  handle_option: Handle
  unpublished: Unpublished
  overnight_mountings: Overnight Mountings
  update_only_if_nil_metafield: Only update if current metafield is empty
  update_only_if_nil_product_type: Only update if current product type is empty
  import_products: Products added
  sku_merged: variants merged
  premier_wd: Premier WD
  inventory: Inventory
  pricing: Pricing
  proceed_remove_feed:
    By proceeding, feed will auto archive the products that are
    not in feed. Please make sure to add filters accordingly
  yes_proceed: Yes, proceed
  process_archive_product:
    "<0>This process will be archive existing products in stores.
    Please add  <1>Filters (Step 3)  </1>to avoid active products.</0>"
  forge_motorsport: Forge Motorsport
  silverbene: Silverbene
  hofman_animal_care: Hofman Animal Care
  grupo_logi: Grupo Logi SAS
  engel_dropship: Engel Dropship
  auto_publish_import: Auto publish
  customize_tags: Customize tag on new added product
  custom_plan_invalid: Custom plan invalid. Please contact support
  custom_plan_price_below_25: Custom plan price below $25
  credit_not_within_boundary: Credit purchase not within boundary
  credit_purchase_invalid: Credit purchase invalid. Please contact support
  app_banner:
    "System Upgrade: Some features may be unavailable from Feb 14, 7:00PM
    to Feb 16, 4:00AM (EDT). Thank you for your understanding!"
  enable_unpublish_or_archive_product:
    "<0>To enable auto unpublish / archive product,
    please remove Published field on <1>Step 2: Matching Column </1></0>"
  export_image_option: Export Image Options
  all_product_images: All Product Images
  variant_with_product_image_as_backup: Variant with Product Image as Backup
  variant_only: Variants Only
  export_all_image: Export all images for each product
  export_image_for_each_variant:
    Export images for each variant; if missing, use the
    product image
  export_variant_image: Export only variant images; if missing, leave blank
  ulefone: Ulefone
  start_date: Start Date
  end_date: End Date
  group_together_under_product: Grouped together under a single product
  help_text_product_attribute_if_not_found:
    Enabling this would create a new product
    attribute based on the custom field key if no such key is found
  label_create_product_attribute_if_not_found:
    Auto create a new product attribute
    if not found
  help_text_product_attribute_key:
    Key used to identify which product attribute value
    to update
  popular: Popular
  merge_variant_will_tagged_with: Merge variants will tagged with
  quantity_field_mapped:
    "To enable this feature, please map quantity field on<1> Step
    2: Matching column</1>"
  video_tutorial: Video Tutorial
  keno: Keno
  sharepoint_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from Microsoft Sharepoint
  mapping_placeholder: Column name / index (eg:SKU, 1)
  export_mapping_placeholder: "Column index (eg: 1,2)"
  select_field_to_update: Select field(s) to update
  select_field_to_add: Select field(s) to add
  select_field_to_export: Select field(s) to export
  match_field_to_identify_product: Match field to identify the products
  only_matching_field_will_update_to_store:
    Only matching field will be updated to
    store
  only_matching_field_will_populated_in_store:
    Only matching field will be populated
    in store
  token_path_body: Token on Path
  auto_remove_default_title: Remove Default Title
  auto_remove_default_title_help_text:
    When enable, will remove the Default Title
    and set to blank
  not_found_in_feed: "(not found in feed)"
  want_to_auto_clear: Want to auto clear from store?
  digital_ocean: Digital Ocean
  sap_business_one_sl_api: SAP Business One Service Layer API
  login_header: Login Header
  login_url: Login URL
  data_key: Data Key
  token_key: Token Key
  data_center_region: Data Center Region
  space_bucket_name: Space Bucket Name
  quantity_on_hand: Quantity (On Hand)
  quantity_available: Quantity (Available)
  only_update_stock_location: Only update stock location
  only_update_stock_location_help_text: "(Will not update the variant quantity)"
  auto_publish_helper_text:
    "<0>The Status field mapping in <2>Step 2: Matching Column</2>
    will override the Auto Publish setting</0>"
  api_user_identifier: API User Identifier
  api_user_secret: API User Secret
  label_create_key_if_not_found_bc: Auto create a new custom field if not found
  help_text_create_key_if_not_found_bc:
    Enabling this would create a new custom field
    record based on the custom field key if no such key is found
  datepicker: Date
  share_your_feedback: Share your feedback
  satisfied: How satisfied are you with the overall process?
  siigo: Siigo
  clear_current_images_confirm:
    Enabling this option may take longer to complete.
    Would you like to proceed?
  additional_currencies: Additional Currencies
  currency: Currency
  mapping_label: Mapping
  invalid_api_body:
    API parameter(s) is invalid. Kindly check the profile settings
    and try again.
  invalid_file_extension:
    This file can't be read. Kindly ensure that the file extension
    settings matches the original file.
  invalid_google_sheet_permission:
    Please ensure that the Google Sheet permission
    is set to 'Anyone with the link' or 'Anyone on the internet' and try again.
  invalid_sftp_connection:
    The FTP/SFTP server cannot be connected to. Kindly ensure
    that the server is accessible programatically.
  create_metafield_not_found: Auto create a new metafield definition if not found
  additional_market_currencies: Additional Market currencies
  add_market_currency: Add Market Currency
  compare_price_at_region_required:
    Markets - Compare-at Price required. Click "Add
    field" below
  price_region_placeholder: Map market currency code in smart settings
  warehouses: Warehouses
  url_handle: Create URL / handle redirect
  url_handle_helptext: For example, url is product/123 > can create as to product/12345
  page_behind_captcha:
    Stock Sync is unable to access feed files behind a CAPTCHA.
    Please contact your administrator to whitelist our IP.
  categories_field: Categories
  brand_field: Brands
  metafield_helptext:
    "Customize additional product info (eg. height, length, ETA
    etc) "
  connection_type: Connection Type
  match_identifier_from_feed_to_store: To match the identifier from feed to store
  only_matching_field_will_export_to_destination:
    Only matching field will be exported
    to destination
  whitelist_required:
    Stock Sync is unable to connect to your supplier. Please contact
    your administrator to whitelist our IP.
  out_of_stock_strategy: Out of stock strategy
  admin_setting: Admin setting
  total_product: total product(s)
  view_product_scanned: View products scanned (estimate)
  reference: Reference
  auto_translate: Auto translate
  translate_for_description_field: Translation for description field
  customize_with_different_file_setting:
    Customize with different file setting. e.g.
    XML, JSON etc
  only_matching_field_will_add_to_store: Only matching field will be added to store
  manage_remove_or_archive_product_in_store: Manage remove/archive products in store
  not_archive_specific_variants_in_multi_variants_product:
    "syncX: Stock Sync does
    not archive specfic variants in a multi variants products"
  filter_product_from_feed_file: To filter feed file products
  store_product_filters: Store product filter
  to_filter_products_from_store_products: To filter store products
  timco_api: Timco API
  itembom: Itembom
  instock: Instock
  price_api: Price API
  itembom_helper: "Required field e.g: category_id, diamond_quality"
  instock_helper: If want to update/add quantity, need to fill in category_id
  price_api_helper: To use this, Itembom and Instock must be filled up
  dynamic_price: Dynamic Price
  magento: Magento
  host_helper: "E.g: www.example.com"
  engagement_semi_mount: Semi-Mount level for Engagement Category
  daily_schedule_helptext: "(total schedule frequency in a day)"
  monthly_credit:
    Monthly plan for continous access to update/export/remove. No extra
    credits needed
  included_in_plan: Included in your plan
  included: Included
  purchase_anytime: Purchase anytime to add new products
  not_used: Not used for update/export/remove
  suggested_by_ai: Suggested by AI
  product_google_drive_urls: Product Google Drive Urls
  internal_server_error:
    Your store memory limit reached. Please increase your WooCommerce
    memory limit.
  inventory_quantity: Available quantity
  operator: Condition
  page_blacklisted:
    File cannot be read. Please contact your website administrator/supplier
    to whitelist our IP.
  empty_zip_file: Zip file is empty.
  set_automation: Set automation
  view_link: View link
  marketplace: Marketplace
  height_converter: Height converter
  height_delimiter: Height delimiter
  depth_converter: Depth converter
  depth_delimiter: Depth delimiter
  width_converter: Width converter
  width_delimiter: Width delimiter
  marathon_leisure: Marathon Leisure
  access_token_secret: Access token secret
  schake: Schake
  google_sheet_published: Google Sheet (Published)
  review_text: How easy was it for you to set up your feed?
  review_helptext: " Your feedback improves the tools you rely on."
  files_api: Files API
  file_path: File Path
  total_variants_in_store:
    You can estimate the total variants <2>here</2> in your
    store and choose a plan that fits your needs.
  filter_helpdoc:
    "By adding <2>Step 3: Filters (optional)</2>, can reduce the total
    number of variants counted in your store"
  tip_reduce_payment: "Tips: Want to reduce payment?"
  disable_schedule_button: Yes, disable schedule
  save_changes: Save changes
  svs_vetchannel: SVS VetChannel
  puckator_api: Puckator Dropship API
  note_for_image_and_desc:
    Product image & description may take a few moments to appear
    after processing is complete
  note: "Note:"
  synnex_ftp: Synnex FTP
  proceed_stop_process: Yes, abort process
  product_category_id: Product category
  enterprise_solution: Need Enterprise Solutions?
  enterprise_helptext: Full-stack ERP sync tailored for enterprise merchants
  enterprise_helptext_01: Custom 10 min schedule for critical updates
  contact_support: Contact support
  retrieving_products: Retrieving product
  onshopfront: Shopfront API
  onshopfront_authorize_tooltips:
    You might need to authorize again if failed to get
    authorize from Shopfront API
  sync_inventory_availability: Sync Inventory Availability
  canceling: Canceling
  qbp_pos: QBP POS
  note_scanned:
    "Note: The scanned estimate does not apply to SKU (Variant’s SKU),
    Available quantity and Collection filters"
  auto_clear_helptext: Every process runs it will archived the unmatched variants
