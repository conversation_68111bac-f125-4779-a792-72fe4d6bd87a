# Preview all emails at http://localhost:3000/rails/mailers/backend_mailer
class BackendMailerPreview < ActionMailer::Preview
  def daily_report
    BackendMailer.daily_report
  end

  def queuing_profile_email
    BackendMailer.queuing_profile_email("2")
  end

  def start_instance_status_email
    BackendMailer.start_instance_status_email("Auto start instance failed to start.")
  end

  def restart_job_instance_status_email
    BackendMailer.restart_job_instance_status_email("Auto restart job instance failed.")
  end

  def stopped_fargate_tasks
    tasks = [OpenStruct.new({
      profile_id: 45074,
      current_definition: nil,
      upgrade_memory: true,
      updated_definition: "Job-CPU1-2GB:2",
      upgraded: true,
      fargate_task_definition: "e3184de8-bc7a-486d-a751-2a7a6c372e8d"
    })]
    failures = [Aws::ECS::Types::Failure.new({
      arn: "arn:aws:ecs:us-east-1:284639385388:task/e3184de8-bc7a-486d-a751-2a7a6c372e8d",
      reason: "MISSING"
    })]
    BackendMailer.stopped_fargate_tasks(failures, tasks)
  end

  def fargate_running_more_than_twelve_hours
    task_ids = [{task: "a39fd67a1b194a54864eb4852aeab4bf", profile: "309708", health_status: "UNHEALTHY"},
      {task: "f0366a2868aa41aa80d83cf35a595682", profile: "363028", health_status: "UNHEALTHY"}]
    bulk_profiles_stuck = [
      {profile: 111202, bulk_status: "EXPIRED", bulk_id: "gid://shopify/BulkOperation/4834909257907"},
      {profile: 186119, bulk_status: "N/A", bulk_id: "gid://shopify/BulkOperation/6109015081309"},
      {profile: 148038, bulk_status: "EXPIRED", bulk_id: "gid://shopify/BulkOperation/5597885006165"},
      {profile: 336716, bulk_status: "N/A", bulk_id: "gid://shopify/BulkOperation/5465475383633"},
      {profile: 318540, bulk_status: "EXPIRED", bulk_id: "gid://shopify/BulkOperation/5621627322710"},
      {profile: 302703, bulk_status: "COMPLETED", bulk_id: "gid://shopify/BulkOperation/3057170055376"}
    ]
    BackendMailer.fargate_running_more_than_twelve_hours(task_ids, bulk_profiles_stuck)
  end
end
